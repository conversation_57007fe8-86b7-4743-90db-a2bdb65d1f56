const bcrypt = require('bcryptjs');
const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const accountSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6),
  name: Joi.string().required(),
  role: Joi.string().valid('student', 'supervisor', 'admin', 'assistant', 'dentist').required(),
  studentId: Joi.string().when('role', { is: 'student', then: Joi.required(), otherwise: Joi.forbidden() }),
  universityId: Joi.string().when('role', {
    is: Joi.string().valid('student', 'supervisor', 'admin'),
    then: Joi.required(),
    otherwise: Joi.when('role', {
      is: 'assistant',
      then: Joi.when('affiliationType', {
        is: 'university',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      otherwise: Joi.forbidden()
    })
  }),
  dentistId: Joi.string().when('role', { is: 'assistant', then: Joi.optional(), otherwise: Joi.forbidden() }),
  affiliationType: Joi.string().valid('university', 'dentist').when('role', { is: 'assistant', then: Joi.required(), otherwise: Joi.optional() }),
});

const updateAccountSchema = Joi.object({
  email: Joi.string().email(),
  password: Joi.string().min(6),
  name: Joi.string(),
  universityId: Joi.string().when('role', {
    is: Joi.string().valid('student', 'supervisor', 'admin'),
    then: Joi.required(),
    otherwise: Joi.when('role', {
      is: 'assistant',
      then: Joi.when('affiliationType', {
        is: 'university',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      otherwise: Joi.forbidden()
    })
  }),
  dentistId: Joi.string().when('role', { is: 'assistant', then: Joi.optional(), otherwise: Joi.forbidden() }),
  affiliationType: Joi.string().valid('university', 'dentist').when('role', { is: 'assistant', then: Joi.optional() }),
}).min(1);

exports.getAllAccounts = async (req, res) => {
  try {
    const [students, supervisors, admins, assistants, superadmins, dentists] = await Promise.all([
      Student.find().select('email name role studentId university createdAt updatedAt'),
      Supervisor.find().select('email name role university createdAt updatedAt'),
      Admin.find().select('email name role university createdAt updatedAt'),
      Assistant.find().select('email name role university dentistId affiliation createdAt updatedAt'),
      Superadmin.find().select('email name role createdAt updatedAt'),
      Dentist.find().select('dentistId name role contactInfo createdAt updatedAt'),
    ]);

    const accounts = [
      ...students.map(s => ({
        id: s._id.toString(),
        email: s.email,
        name: s.name,
        role: s.role,
        universityId: s.university,
        studentId: s.studentId,
        // Password field removed as requested
        createdAt: s.createdAt,
        updatedAt: s.updatedAt
      })),
      ...supervisors.map(s => ({
        id: s._id.toString(),
        email: s.email,
        name: s.name,
        role: s.role,
        universityId: s.university,
        // Password field removed as requested
        createdAt: s.createdAt,
        updatedAt: s.updatedAt
      })),
      ...admins.map(a => ({
        id: a._id.toString(),
        email: a.email,
        name: a.name,
        role: a.role,
        universityId: a.university,
        // Password field removed as requested
        createdAt: a.createdAt,
        updatedAt: a.updatedAt
      })),
      ...assistants.map(a => ({
        id: a._id.toString(),
        email: a.email,
        name: a.name,
        role: a.role,
        universityId: a.university,
        dentistId: a.dentistId,
        affiliation: a.affiliation,
        // Password field removed as requested
        createdAt: a.createdAt,
        updatedAt: a.updatedAt
      })),
      ...superadmins.map(s => ({
        id: s._id.toString(),
        email: s.email,
        name: s.name,
        role: s.role,
        // Password field removed as requested
        createdAt: s.createdAt,
        updatedAt: s.updatedAt
      })),
      ...dentists.map(d => ({
        id: d._id.toString(),
        email: d.contactInfo.email,
        name: d.name.en,
        role: 'dentist',
        dentistId: d.dentistId,
        // Password field removed as requested
        createdAt: d.createdAt,
        updatedAt: d.updatedAt
      })),
    ];

    res.status(200).json(accounts);
  } catch (error) {
    console.error('Error fetching accounts:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.createAccount = async (req, res) => {
  const { error } = accountSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { email, password, name, role, studentId, universityId, dentistId } = req.body;

  try {
    const existingUser = await Promise.all([
      Student.findOne({ email }),
      Supervisor.findOne({ email }),
      Admin.findOne({ email }),
      Assistant.findOne({ email }),
      Superadmin.findOne({ email }),
      Dentist.findOne({ 'contactInfo.email': email }),
    ]);

    if (existingUser.some(user => user)) {
      return res.status(400).json({ message: 'Email already exists' });
    }

    // Check university ID for roles that require it
    if (['student', 'supervisor', 'admin'].includes(role) ||
        (role === 'assistant' && req.body.affiliationType === 'university')) {
      if (!universityId) {
        return res.status(400).json({
          message: 'University ID is required for this role'
        });
      }
      const university = await University.findOne({ universityId });
      if (!university) {
        return res.status(400).json({ message: 'Invalid university ID' });
      }
    }

    if (role === 'assistant' && dentistId && dentistId.trim() !== '') {
      const dentist = await Dentist.findOne({ dentistId });
      if (!dentist) {
        return res.status(400).json({ message: 'Invalid dentist ID' });
      }
    }

    let newUser;
    // Don't hash the password here - let the model's pre-save hook handle it

    if (role === 'student') {
      const existingStudent = await Student.findOne({ studentId });
      if (existingStudent) {
        return res.status(400).json({ message: 'Student ID already exists' });
      }
      newUser = new Student({
        studentId,
        email,
        password, // Use plain password - will be hashed by pre-save hook
        name,
        role,
        university: universityId,
      });
    } else if (role === 'supervisor') {
      newUser = new Supervisor({
        email,
        password, // Use plain password - will be hashed by pre-save hook
        name,
        role,
        university: universityId,
      });
    } else if (role === 'admin') {
      newUser = new Admin({
        email,
        password, // Use plain password - will be hashed by pre-save hook
        name,
        role,
        university: universityId,
      });
    } else if (role === 'assistant') {
      const affiliationType = req.body.affiliationType || 'university';

      // Determine which ID to use based on affiliation type
      let affiliationId;

      if (affiliationType === 'university') {
        affiliationId = universityId;
      } else {
        // For dentist affiliation
        affiliationId = dentistId || '';
      }

      newUser = new Assistant({
        email,
        password, // Use plain password - will be hashed by pre-save hook
        name,
        role,
        // Set only the relevant field based on affiliation type
        university: affiliationType === 'university' ? universityId : undefined,
        dentistId: affiliationType === 'dentist' ? (dentistId || '') : undefined,
        // Set the affiliation object
        affiliation: {
          type: affiliationType,
          id: affiliationId
        },
      });
    } else if (role === 'dentist') {
      const existingDentist = await Dentist.findOne({ email });
      if (existingDentist) {
        return res.status(400).json({ message: 'Dentist email already exists' });
      }
      newUser = new Dentist({
        dentistId: email,
        email,
        password, // Use plain password - will be hashed by pre-save hook
        name: { en: name, ar: name },
        clinicName: { en: `${name}'s Clinic`, ar: `عيادة ${name}` },
        about: { en: '', ar: '' },
        services: [],
        address: {
          street: { en: '', ar: '' },
          city: { en: '', ar: '' },
          country: { en: '', ar: '' },
          postalCode: '',
        },
        contactInfo: { phone: '', email, website: '' },
        slotBeginDate: new Date(),
        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
        role: 'dentist',
      });
    }

    await newUser.save();

    // Log account creation activity
    try {
      await ActivityLog.create({
        userId: req.user._id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Created user account',
        details: `Role: ${role}, Email: ${email}, Name: ${name}`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging account creation:', logError);
    }

    res.status(201).json({ message: 'Account created successfully' });
  } catch (error) {
    console.error('Error creating account:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.updateAccount = async (req, res) => {
  const { error } = updateAccountSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { id } = req.params;
  const updates = req.body;

  try {
    let user;
    const models = [Student, Supervisor, Admin, Assistant, Superadmin, Dentist];
    for (const Model of models) {
      user = await Model.findById(id);
      if (user) break;
    }

    if (!user) {
      return res.status(404).json({ message: 'Account not found' });
    }

    if (updates.email) {
      const existingUser = await Promise.all([
        Student.findOne({ email: updates.email, _id: { $ne: id } }),
        Supervisor.findOne({ email: updates.email, _id: { $ne: id } }),
        Admin.findOne({ email: updates.email, _id: { $ne: id } }),
        Assistant.findOne({ email: updates.email, _id: { $ne: id } }),
        Superadmin.findOne({ email: updates.email, _id: { $ne: id } }),
        Dentist.findOne({ 'contactInfo.email': updates.email, _id: { $ne: id } }),
      ]);

      if (existingUser.some(u => u)) {
        return res.status(400).json({ message: 'Email already exists' });
      }
    }

    if (updates.universityId) {
      const university = await University.findOne({ universityId: updates.universityId });
      if (!university) {
        return res.status(400).json({ message: 'Invalid university ID' });
      }
    }

    if (updates.dentistId) {
      const dentist = await Dentist.findOne({ dentistId: updates.dentistId });
      if (!dentist) {
        return res.status(400).json({ message: 'Invalid dentist ID' });
      }
    }

    // Don't hash the password here - let the model's pre-save hook handle it
    // The password will be hashed by the pre-save hook in the model

    Object.assign(user, updates);
    await user.save();
    res.status(200).json({ message: 'Account updated successfully' });
  } catch (error) {
    console.error('Error updating account:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.deleteAccount = async (req, res) => {
  const { id } = req.params;

  try {
    let user;
    const models = [Student, Supervisor, Admin, Assistant, Superadmin, Dentist];

    // First find the user to get details for logging
    for (const Model of models) {
      user = await Model.findById(id);
      if (user) break;
    }

    if (!user) {
      return res.status(404).json({ message: 'Account not found' });
    }

    // Store user details for logging before deletion
    const userEmail = user.email || user.contactInfo?.email || 'Unknown';
    const userName = user.name?.en || user.name || 'Unknown';
    const userRole = user.role || 'Unknown';

    // Now delete the user
    for (const Model of models) {
      const deletedUser = await Model.findByIdAndDelete(id);
      if (deletedUser) break;
    }

    // Log account deletion activity
    try {
      await ActivityLog.create({
        userId: req.user._id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Deleted user account',
        details: `Deleted ${userRole}: ${userName} (${userEmail})`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging account deletion:', logError);
    }

    res.status(200).json({ message: 'Account deleted successfully' });
  } catch (error) {
    console.error('Error deleting account:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Reset password for an account
exports.resetPassword = async (req, res) => {
  const { id } = req.params;
  const { newPassword } = req.body;

  if (!newPassword || newPassword.length < 6) {
    return res.status(400).json({ message: 'Password must be at least 6 characters' });
  }

  try {
    let user;
    const models = [Student, Supervisor, Admin, Assistant, Superadmin, Dentist];

    for (const Model of models) {
      user = await Model.findById(id);
      if (user) {
        console.log(`Found user in ${Model.modelName} collection`);
        break;
      }
    }

    if (!user) {
      return res.status(404).json({ message: 'Account not found' });
    }

    // Set the new password (will be hashed by pre-save hook)
    user.password = newPassword;
    await user.save();

    // Log password reset activity
    try {
      const userEmail = user.email || user.contactInfo?.email || 'Unknown';
      const userName = user.name?.en || user.name || 'Unknown';

      await ActivityLog.create({
        userId: req.user._id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Reset user password',
        details: `Reset password for: ${userName} (${userEmail})`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging password reset:', logError);
    }

    res.status(200).json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error('Error resetting password:', error);
    res.status(500).json({ message: 'Server error' });
  }
};