const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const Joi = require('joi');
const config = require('../config/config');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { StudentHelpers } = require('../models/firebase/Student');
const { SupervisorHelpers } = require('../models/firebase/Supervisor');
const { AdminHelpers } = require('../models/firebase/Admin');


const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
});

const login = async (req, res) => {
  const { error } = loginSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { email, password } = req.body;

  try {
    const userTypes = [
      { collection: COLLECTIONS.STUDENTS, role: 'student' },
      { collection: COLLECTIONS.SUPERVISORS, role: 'supervisor' },
      { collection: COLLECTIONS.ADMINS, role: 'admin' },
      { collection: COLLECTIONS.ASSISTANTS, role: 'assistant' },
    ];

    let foundUser = null;
    let role = '';

    // Check all standard collections first
    for (const { collection, role: roleName } of userTypes) {
      const user = await FirestoreHelpers.findOne(collection, { field: 'email', operator: '==', value: email });
      console.log(`Checking ${roleName} for email ${email}: ${user ? 'Found' : 'Not found'}`);
      if (user) {
        const isMatch = await bcrypt.compare(password, user.password);
        console.log(`Password match for ${roleName}: ${isMatch}`);
        if (isMatch) {
          foundUser = user;
          role = roleName;
          break;
        }
      }
    }

    // Check superadmin collection
    if (!foundUser) {
      const superadmin = await FirestoreHelpers.findOne(COLLECTIONS.CONFIGS, { field: 'email', operator: '==', value: email });
      console.log(`Checking superadmin for email ${email}: ${superadmin ? 'Found' : 'Not found'}`);
      if (superadmin && superadmin.role === 'superadmin') {
        const isMatch = await bcrypt.compare(password, superadmin.password);
        console.log(`Password match for superadmin: ${isMatch}`);
        if (isMatch) {
          foundUser = superadmin;
          role = 'superadmin';
        }
      }
    }

    if (!foundUser) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    console.log('Signing JWT for:', { id: foundUser.id, role }); // Debug log
    const token = jwt.sign(
      { id: foundUser.id, role },
      config.JWT_SECRET,
      { expiresIn: config.JWT_ACCESS_EXPIRATION }
    );

    // Customize the user object based on role
    let userResponse = {
      id: foundUser.id,
      email: foundUser.email,
      name: foundUser.name,
      role,
      university: foundUser.university || '',
    };

    // Add role-specific fields
    if (role === 'student') {
      userResponse.studentId = foundUser.studentId || foundUser.id;
    } else if (role === 'assistant') {
      userResponse.dentistId = foundUser.dentistId;
      // Include affiliation information if available
      if (foundUser.affiliation) {
        userResponse.affiliation = foundUser.affiliation;
      }
    }

    // Log successful login
    try {
      await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
        userId: foundUser.id,
        userName: foundUser.name,
        userRole: role,
        action: 'User logged in',
        details: `Email: ${email}`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging login activity:', logError);
      // Don't fail login if logging fails
    }

    res.json({
      token,
      role,
      user: userResponse,
    });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

const getCurrentUser = async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    const userData = {
      id: req.user.id,
      role: req.user.role,
      name: req.user.name,
      email: req.user.email,
      university: req.user.university || '',
    };
    if (req.user.role === 'student') {
      userData.studentId = req.user.studentId || req.user.id;
    }

    res.json(userData);
  } catch (error) {
    console.error('getCurrentUser error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get user profile
const getProfile = async (req, res) => {
  try {
    const { id, role } = req.user;
    let user;

    // Fetch user based on role
    if (role === 'student') {
      user = await FirestoreHelpers.findById(COLLECTIONS.STUDENTS, id);
    } else if (role === 'supervisor') {
      user = await FirestoreHelpers.findById(COLLECTIONS.SUPERVISORS, id);
    } else if (role === 'admin') {
      user = await FirestoreHelpers.findById(COLLECTIONS.ADMINS, id);
    } else if (role === 'assistant') {
      user = await FirestoreHelpers.findById(COLLECTIONS.ASSISTANTS, id);
    } else if (role === 'superadmin') {
      user = await FirestoreHelpers.findById(COLLECTIONS.CONFIGS, id);
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Remove password from response
    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: role,
      university: user.university || '',
      studentId: role === 'student' ? user.studentId || user.id : undefined,
    };

    res.json(userData);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: error.message });
  }
};


module.exports = { login, getCurrentUser, getProfile };