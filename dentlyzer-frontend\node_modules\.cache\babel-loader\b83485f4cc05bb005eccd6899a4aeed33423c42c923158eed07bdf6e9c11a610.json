{"ast": null, "code": "import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport function composeInitialProps(ForComponent) {\n  return ctx => new Promise(resolve => {\n    const i18nInitialProps = getInitialProps();\n    if (ForComponent.getInitialProps) {\n      ForComponent.getInitialProps(ctx).then(componentsInitialProps => {\n        resolve({\n          ...componentsInitialProps,\n          ...i18nInitialProps\n        });\n      });\n    } else {\n      resolve(i18nInitialProps);\n    }\n  });\n}\nexport function getInitialProps() {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces ? i18n.reportNamespaces.getUsedNamespaces() : [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n}", "map": {"version": 3, "names": ["createContext", "getDefaults", "setDefaults", "getI18n", "setI18n", "initReactI18next", "I18nContext", "ReportNamespaces", "constructor", "usedNamespaces", "addUsedNamespaces", "namespaces", "for<PERSON>ach", "ns", "getUsedNamespaces", "Object", "keys", "composeInitialProps", "ForComponent", "ctx", "Promise", "resolve", "i18nInitialProps", "getInitialProps", "then", "componentsInitialProps", "i18n", "reportNamespaces", "ret", "initialI18nStore", "languages", "l", "getResourceBundle", "initialLanguage", "language"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/node_modules/react-i18next/dist/es/context.js"], "sourcesContent": ["import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport function composeInitialProps(ForComponent) {\n  return ctx => new Promise(resolve => {\n    const i18nInitialProps = getInitialProps();\n    if (ForComponent.getInitialProps) {\n      ForComponent.getInitialProps(ctx).then(componentsInitialProps => {\n        resolve({\n          ...componentsInitialProps,\n          ...i18nInitialProps\n        });\n      });\n    } else {\n      resolve(i18nInitialProps);\n    }\n  });\n}\nexport function getInitialProps() {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces ? i18n.reportNamespaces.getUsedNamespaces() : [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,WAAW,EAAEC,WAAW,QAAQ,eAAe;AACxD,SAASC,OAAO,EAAEC,OAAO,QAAQ,mBAAmB;AACpD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASJ,WAAW,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,gBAAgB;AACrE,OAAO,MAAMC,WAAW,GAAGN,aAAa,CAAC,CAAC;AAC1C,OAAO,MAAMO,gBAAgB,CAAC;EAC5BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;EAC1B;EACAC,iBAAiBA,CAACC,UAAU,EAAE;IAC5BA,UAAU,CAACC,OAAO,CAACC,EAAE,IAAI;MACvB,IAAI,CAAC,IAAI,CAACJ,cAAc,CAACI,EAAE,CAAC,EAAE,IAAI,CAACJ,cAAc,CAACI,EAAE,CAAC,GAAG,IAAI;IAC9D,CAAC,CAAC;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAClB,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACP,cAAc,CAAC;EACzC;AACF;AACA,OAAO,SAASQ,mBAAmBA,CAACC,YAAY,EAAE;EAChD,OAAOC,GAAG,IAAI,IAAIC,OAAO,CAACC,OAAO,IAAI;IACnC,MAAMC,gBAAgB,GAAGC,eAAe,CAAC,CAAC;IAC1C,IAAIL,YAAY,CAACK,eAAe,EAAE;MAChCL,YAAY,CAACK,eAAe,CAACJ,GAAG,CAAC,CAACK,IAAI,CAACC,sBAAsB,IAAI;QAC/DJ,OAAO,CAAC;UACN,GAAGI,sBAAsB;UACzB,GAAGH;QACL,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLD,OAAO,CAACC,gBAAgB,CAAC;IAC3B;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAMG,IAAI,GAAGvB,OAAO,CAAC,CAAC;EACtB,MAAMQ,UAAU,GAAGe,IAAI,CAACC,gBAAgB,GAAGD,IAAI,CAACC,gBAAgB,CAACb,iBAAiB,CAAC,CAAC,GAAG,EAAE;EACzF,MAAMc,GAAG,GAAG,CAAC,CAAC;EACd,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3BH,IAAI,CAACI,SAAS,CAAClB,OAAO,CAACmB,CAAC,IAAI;IAC1BF,gBAAgB,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;IACxBpB,UAAU,CAACC,OAAO,CAACC,EAAE,IAAI;MACvBgB,gBAAgB,CAACE,CAAC,CAAC,CAAClB,EAAE,CAAC,GAAGa,IAAI,CAACM,iBAAiB,CAACD,CAAC,EAAElB,EAAE,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC,CAAC;EACJ,CAAC,CAAC;EACFe,GAAG,CAACC,gBAAgB,GAAGA,gBAAgB;EACvCD,GAAG,CAACK,eAAe,GAAGP,IAAI,CAACQ,QAAQ;EACnC,OAAON,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}