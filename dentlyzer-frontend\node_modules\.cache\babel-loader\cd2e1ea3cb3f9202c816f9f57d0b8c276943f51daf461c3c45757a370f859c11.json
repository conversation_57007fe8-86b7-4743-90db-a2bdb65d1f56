{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\assistant\\\\AssignStudentModal.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaTimes, FaUserMd, FaSearch, FaSpinner } from 'react-icons/fa';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\nconst AssignStudentModal = ({\n  isOpen,\n  onClose,\n  appointment,\n  onAssign\n}) => {\n  _s();\n  const [students, setStudents] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [studentAvailability, setStudentAvailability] = useState({});\n  const {\n    token,\n    user\n  } = useAuth();\n  useEffect(() => {\n    if (isOpen && appointment) {\n      fetchStudents();\n    }\n  }, [isOpen, appointment, token, user]);\n  const fetchStudents = async () => {\n    setLoading(true);\n    try {\n      var _user$affiliation;\n      const universityId = user.university || ((_user$affiliation = user.affiliation) === null || _user$affiliation === void 0 ? void 0 : _user$affiliation.id);\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n\n      // Use the public endpoint to get all students\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/students`, config);\n\n      // Get the appointment date and time\n      const appointmentDate = new Date(appointment.date);\n      const appointmentTime = appointment.time;\n\n      // Create a map to store student availability\n      const availabilityMap = {};\n\n      // Fetch all appointments to check for conflicts\n      const appointmentsResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/schedule?university=${encodeURIComponent(universityId)}`, config);\n      const allAppointments = appointmentsResponse.data || [];\n\n      // Check each student's availability\n      response.data.forEach(student => {\n        // Find appointments for this student\n        const studentAppointments = allAppointments.filter(appt => appt.doctor === student.studentId && appt._id !== appointment._id && appt.status !== 'cancelled' // Ignore cancelled appointments\n        );\n\n        // Check if any appointment conflicts with the current one\n        const hasConflict = studentAppointments.some(appt => {\n          const apptDate = new Date(appt.date);\n          const currentDate = new Date(appointmentDate);\n\n          // Compare dates by converting to same format\n          const apptDateString = apptDate.toISOString().split('T')[0];\n          const currentDateString = currentDate.toISOString().split('T')[0];\n\n          // Check if same day and same time slot\n          return apptDateString === currentDateString && appt.time === appointmentTime;\n        });\n        console.log(`Student ${student.name} (${student.studentId}) availability check:`, {\n          appointmentDate: appointmentDate,\n          appointmentTime: appointmentTime,\n          conflictingAppointments: studentAppointments.filter(appt => {\n            const apptDate = new Date(appt.date);\n            const currentDate = new Date(appointmentDate);\n            const apptDateString = apptDate.toISOString().split('T')[0];\n            const currentDateString = currentDate.toISOString().split('T')[0];\n            return apptDateString === currentDateString && appt.time === appointmentTime;\n          }),\n          hasConflict: hasConflict\n        });\n\n        // Store availability status\n        availabilityMap[student.studentId] = !hasConflict;\n      });\n      setStudentAvailability(availabilityMap);\n      setStudents(response.data || []);\n      setError('');\n    } catch (err) {\n      var _err$response;\n      console.error('Error fetching students:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n      setError('Failed to load students. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAssign = async () => {\n    if (!selectedStudent) {\n      setError('Please select a student to assign.');\n      return;\n    }\n\n    // Check if the selected student is available\n    if (!studentAvailability[selectedStudent.studentId]) {\n      setError('This student is not available at the selected time. Please choose another student.');\n      return;\n    }\n    try {\n      // Call the onAssign callback with the selected student\n      await onAssign(appointment._id, selectedStudent);\n      onClose();\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Error in handleAssign:', err);\n\n      // Check if this is a conflict error from the backend\n      if ((_err$response2 = err.response) !== null && _err$response2 !== void 0 && (_err$response2$data = _err$response2.data) !== null && _err$response2$data !== void 0 && _err$response2$data.conflict) {\n        setError('This student already has an appointment at this time. Please choose another student.');\n\n        // Update the availability status for this student\n        setStudentAvailability(prev => ({\n          ...prev,\n          [selectedStudent.studentId]: false\n        }));\n      } else {\n        setError('Failed to assign student. Please try again.');\n      }\n    }\n  };\n  const filteredStudents = students.filter(student => {\n    var _student$name, _student$studentId;\n    return ((_student$name = student.name) === null || _student$name === void 0 ? void 0 : _student$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_student$studentId = student.studentId) === null || _student$studentId === void 0 ? void 0 : _student$studentId.toLowerCase().includes(searchTerm.toLowerCase()));\n  });\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      exit: {\n        opacity: 0\n      },\n      className: \"fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4\",\n      onClick: onClose,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          scale: 1,\n          opacity: 1,\n          y: 0\n        },\n        exit: {\n          scale: 0.9,\n          opacity: 0,\n          y: 20\n        },\n        transition: {\n          type: \"spring\",\n          damping: 25,\n          stiffness: 300\n        },\n        className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto relative\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sticky top-0 z-10 text-white p-6 rounded-t-2xl flex justify-between items-center\",\n          style: {\n            background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n              className: \"h-6 w-6 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold\",\n              children: \"Assign Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"text-white hover:text-gray-200 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(FaTimes, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 p-3 bg-red-50 border-l-4 border-red-500 text-red-700 rounded\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg\",\n                style: {\n                  outline: 'none',\n                  boxShadow: 'none',\n                  borderColor: '#e5e7eb',\n                  ':focus': {\n                    borderColor: colorPalette.primary\n                  }\n                },\n                placeholder: \"Search students by name or ID...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center items-center py-8\",\n              children: /*#__PURE__*/_jsxDEV(FaSpinner, {\n                className: \"h-8 w-8 animate-spin\",\n                style: {\n                  color: colorPalette.primary\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this) : filteredStudents.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8 text-gray-500\",\n              children: \"No students found. Try a different search term.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 gap-3 max-h-[40vh] overflow-y-auto pr-2\",\n              children: filteredStudents.map(student => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 border rounded-lg transition-colors ${studentAvailability[student.studentId] ? 'cursor-pointer ' + ((selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.studentId) !== student.studentId ? 'hover:bg-gray-50' : '') : 'opacity-70 cursor-not-allowed'}`,\n                style: {\n                  backgroundColor: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.studentId) === student.studentId ? `${colorPalette.primary}10` : !studentAvailability[student.studentId] ? '#f9fafb' : colorPalette.background,\n                  borderColor: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.studentId) === student.studentId ? colorPalette.primary : !studentAvailability[student.studentId] ? '#ef444430' : '#e5e7eb'\n                },\n                onClick: () => {\n                  // Only allow selecting available students\n                  if (studentAvailability[student.studentId]) {\n                    setSelectedStudent(student);\n                    setError('');\n                  } else {\n                    setError('This student is not available at the selected time. Please choose another student.');\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rounded-full p-2 mr-3\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}15`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaUserMd, {\n                        className: \"h-5 w-5\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-medium text-gray-900\",\n                        children: student.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\"ID: \", student.studentId]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: studentAvailability[student.studentId] ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 text-xs rounded-full bg-green-100 text-green-800\",\n                      children: \"Available\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 text-xs rounded-full bg-red-100 text-red-800\",\n                      children: \"Not Available\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 23\n                }, this)\n              }, student._id || student.studentId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sticky bottom-0 bg-white p-6 border-t rounded-b-2xl flex justify-end space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"px-6 py-2 border rounded-lg font-medium transition-colors hover:bg-gray-50\",\n            style: {\n              borderColor: '#e5e7eb',\n              color: colorPalette.text\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAssign,\n            disabled: !selectedStudent || loading,\n            className: \"px-6 py-2 text-white rounded-lg transition-colors shadow-md hover:shadow-lg font-medium hover:brightness-110\",\n            style: {\n              background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`,\n              opacity: !selectedStudent || loading ? 0.5 : 1,\n              cursor: !selectedStudent || loading ? 'not-allowed' : 'pointer'\n            },\n            children: \"Assign\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(AssignStudentModal, \"h3abPzsDiW5Yjv5qs5VU8WQYsP4=\", false, function () {\n  return [useAuth];\n});\n_c = AssignStudentModal;\nexport default AssignStudentModal;\nvar _c;\n$RefreshReg$(_c, \"AssignStudentModal\");", "map": {"version": 3, "names": ["useState", "useEffect", "motion", "AnimatePresence", "FaTimes", "FaUserMd", "FaSearch", "FaSpinner", "axios", "useAuth", "jsxDEV", "_jsxDEV", "colorPalette", "primary", "secondary", "background", "text", "accent", "AssignStudentModal", "isOpen", "onClose", "appointment", "onAssign", "_s", "students", "setStudents", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedStudent", "setSelectedStudent", "studentAvailability", "setStudentAvailability", "token", "user", "fetchStudents", "_user$affiliation", "universityId", "university", "affiliation", "id", "config", "headers", "Authorization", "response", "get", "process", "env", "REACT_APP_API_URL", "appointmentDate", "Date", "date", "appointmentTime", "time", "availabilityMap", "appointmentsResponse", "encodeURIComponent", "allAppointments", "data", "for<PERSON>ach", "student", "studentAppointments", "filter", "appt", "doctor", "studentId", "_id", "status", "hasConflict", "some", "apptDate", "currentDate", "apptDateString", "toISOString", "split", "currentDateString", "console", "log", "name", "conflictingAppointments", "err", "_err$response", "message", "handleAssign", "_err$response2", "_err$response2$data", "conflict", "prev", "filteredStudents", "_student$name", "_student$studentId", "toLowerCase", "includes", "children", "div", "initial", "opacity", "animate", "exit", "className", "onClick", "scale", "y", "transition", "type", "damping", "stiffness", "e", "stopPropagation", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "outline", "boxShadow", "borderColor", "placeholder", "value", "onChange", "target", "color", "length", "map", "backgroundColor", "disabled", "cursor", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/assistant/AssignStudentModal.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaTimes, FaUserMd, FaS<PERSON>ch, FaSpinner } from 'react-icons/fa';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\n\n// Website color palette\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\nconst AssignStudentModal = ({ isOpen, onClose, appointment, onAssign }) => {\n  const [students, setStudents] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [studentAvailability, setStudentAvailability] = useState({});\n  const { token, user } = useAuth();\n\n  useEffect(() => {\n    if (isOpen && appointment) {\n      fetchStudents();\n    }\n  }, [isOpen, appointment, token, user]);\n\n  const fetchStudents = async () => {\n    setLoading(true);\n    try {\n      const universityId = user.university || user.affiliation?.id;\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n\n      // Use the public endpoint to get all students\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/students`, config);\n\n      // Get the appointment date and time\n      const appointmentDate = new Date(appointment.date);\n      const appointmentTime = appointment.time;\n\n      // Create a map to store student availability\n      const availabilityMap = {};\n\n      // Fetch all appointments to check for conflicts\n      const appointmentsResponse = await axios.get(\n        `${process.env.REACT_APP_API_URL}/api/appointments/schedule?university=${encodeURIComponent(universityId)}`,\n        config\n      );\n\n      const allAppointments = appointmentsResponse.data || [];\n\n      // Check each student's availability\n      response.data.forEach(student => {\n        // Find appointments for this student\n        const studentAppointments = allAppointments.filter(\n          appt => appt.doctor === student.studentId &&\n                 appt._id !== appointment._id &&\n                 appt.status !== 'cancelled' // Ignore cancelled appointments\n        );\n\n        // Check if any appointment conflicts with the current one\n        const hasConflict = studentAppointments.some(appt => {\n          const apptDate = new Date(appt.date);\n          const currentDate = new Date(appointmentDate);\n\n          // Compare dates by converting to same format\n          const apptDateString = apptDate.toISOString().split('T')[0];\n          const currentDateString = currentDate.toISOString().split('T')[0];\n\n          // Check if same day and same time slot\n          return apptDateString === currentDateString && appt.time === appointmentTime;\n        });\n\n        console.log(`Student ${student.name} (${student.studentId}) availability check:`, {\n          appointmentDate: appointmentDate,\n          appointmentTime: appointmentTime,\n          conflictingAppointments: studentAppointments.filter(appt => {\n            const apptDate = new Date(appt.date);\n            const currentDate = new Date(appointmentDate);\n            const apptDateString = apptDate.toISOString().split('T')[0];\n            const currentDateString = currentDate.toISOString().split('T')[0];\n            return apptDateString === currentDateString && appt.time === appointmentTime;\n          }),\n          hasConflict: hasConflict\n        });\n\n        // Store availability status\n        availabilityMap[student.studentId] = !hasConflict;\n      });\n\n      setStudentAvailability(availabilityMap);\n      setStudents(response.data || []);\n      setError('');\n    } catch (err) {\n      console.error('Error fetching students:', err.response?.data || err.message);\n      setError('Failed to load students. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAssign = async () => {\n    if (!selectedStudent) {\n      setError('Please select a student to assign.');\n      return;\n    }\n\n    // Check if the selected student is available\n    if (!studentAvailability[selectedStudent.studentId]) {\n      setError('This student is not available at the selected time. Please choose another student.');\n      return;\n    }\n\n    try {\n      // Call the onAssign callback with the selected student\n      await onAssign(appointment._id, selectedStudent);\n      onClose();\n    } catch (err) {\n      console.error('Error in handleAssign:', err);\n\n      // Check if this is a conflict error from the backend\n      if (err.response?.data?.conflict) {\n        setError('This student already has an appointment at this time. Please choose another student.');\n\n        // Update the availability status for this student\n        setStudentAvailability(prev => ({\n          ...prev,\n          [selectedStudent.studentId]: false\n        }));\n      } else {\n        setError('Failed to assign student. Please try again.');\n      }\n    }\n  };\n\n  const filteredStudents = students.filter(student =>\n    student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    student.studentId?.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0, y: 20 }}\n          animate={{ scale: 1, opacity: 1, y: 0 }}\n          exit={{ scale: 0.9, opacity: 0, y: 20 }}\n          transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\n          className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto relative\"\n          onClick={(e) => e.stopPropagation()}\n        >\n          {/* Header */}\n          <div className=\"sticky top-0 z-10 text-white p-6 rounded-t-2xl flex justify-between items-center\"\n            style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}>\n            <div className=\"flex items-center\">\n              <FaUserMd className=\"h-6 w-6 mr-3\" />\n              <h2 className=\"text-xl font-bold\">Assign Student</h2>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"text-white hover:text-gray-200 transition-colors\"\n            >\n              <FaTimes className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          {/* Main content */}\n          <div className=\"p-6\">\n            {error && (\n              <div className=\"mb-4 p-3 bg-red-50 border-l-4 border-red-500 text-red-700 rounded\">\n                {error}\n              </div>\n            )}\n\n            {/* Search bar */}\n            <div className=\"mb-6\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <FaSearch className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg\"\n                  style={{\n                    outline: 'none',\n                    boxShadow: 'none',\n                    borderColor: '#e5e7eb',\n                    ':focus': { borderColor: colorPalette.primary }\n                  }}\n                  placeholder=\"Search students by name or ID...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n\n            {/* Students list */}\n            <div className=\"mb-6\">\n              {loading ? (\n                <div className=\"flex justify-center items-center py-8\">\n                  <FaSpinner className=\"h-8 w-8 animate-spin\" style={{ color: colorPalette.primary }} />\n                </div>\n              ) : filteredStudents.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  No students found. Try a different search term.\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 gap-3 max-h-[40vh] overflow-y-auto pr-2\">\n                  {filteredStudents.map((student) => (\n                    <div\n                      key={student._id || student.studentId}\n                      className={`p-4 border rounded-lg transition-colors ${\n                        studentAvailability[student.studentId]\n                          ? 'cursor-pointer ' + (selectedStudent?.studentId !== student.studentId ? 'hover:bg-gray-50' : '')\n                          : 'opacity-70 cursor-not-allowed'\n                      }`}\n                      style={{\n                        backgroundColor: selectedStudent?.studentId === student.studentId\n                          ? `${colorPalette.primary}10`\n                          : !studentAvailability[student.studentId]\n                            ? '#f9fafb'\n                            : colorPalette.background,\n                        borderColor: selectedStudent?.studentId === student.studentId\n                          ? colorPalette.primary\n                          : !studentAvailability[student.studentId]\n                            ? '#ef444430'\n                            : '#e5e7eb'\n                      }}\n                      onClick={() => {\n                        // Only allow selecting available students\n                        if (studentAvailability[student.studentId]) {\n                          setSelectedStudent(student);\n                          setError('');\n                        } else {\n                          setError('This student is not available at the selected time. Please choose another student.');\n                        }\n                      }}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <div className=\"rounded-full p-2 mr-3\" style={{ backgroundColor: `${colorPalette.primary}15` }}>\n                            <FaUserMd className=\"h-5 w-5\" style={{ color: colorPalette.primary }} />\n                          </div>\n                          <div>\n                            <h3 className=\"font-medium text-gray-900\">{student.name}</h3>\n                            <p className=\"text-sm text-gray-500\">ID: {student.studentId}</p>\n                          </div>\n                        </div>\n                        <div>\n                          {studentAvailability[student.studentId] ? (\n                            <span className=\"px-2 py-1 text-xs rounded-full bg-green-100 text-green-800\">Available</span>\n                          ) : (\n                            <span className=\"px-2 py-1 text-xs rounded-full bg-red-100 text-red-800\">Not Available</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Footer with action buttons */}\n          <div className=\"sticky bottom-0 bg-white p-6 border-t rounded-b-2xl flex justify-end space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"px-6 py-2 border rounded-lg font-medium transition-colors hover:bg-gray-50\"\n              style={{\n                borderColor: '#e5e7eb',\n                color: colorPalette.text\n              }}\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleAssign}\n              disabled={!selectedStudent || loading}\n              className=\"px-6 py-2 text-white rounded-lg transition-colors shadow-md hover:shadow-lg font-medium hover:brightness-110\"\n              style={{\n                background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`,\n                opacity: !selectedStudent || loading ? 0.5 : 1,\n                cursor: !selectedStudent || loading ? 'not-allowed' : 'pointer'\n              }}\n            >\n              Assign\n            </button>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default AssignStudentModal;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClE,MAAM;IAAEoC,KAAK;IAAEC;EAAK,CAAC,GAAG5B,OAAO,CAAC,CAAC;EAEjCR,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,IAAIE,WAAW,EAAE;MACzBiB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACnB,MAAM,EAAEE,WAAW,EAAEe,KAAK,EAAEC,IAAI,CAAC,CAAC;EAEtC,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAAY,iBAAA;MACF,MAAMC,YAAY,GAAGH,IAAI,CAACI,UAAU,MAAAF,iBAAA,GAAIF,IAAI,CAACK,WAAW,cAAAH,iBAAA,uBAAhBA,iBAAA,CAAkBI,EAAE;MAC5D,MAAMC,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUV,KAAK;QAAG;MAAE,CAAC;;MAEhE;MACA,MAAMW,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAEP,MAAM,CAAC;;MAEzF;MACA,MAAMQ,eAAe,GAAG,IAAIC,IAAI,CAAChC,WAAW,CAACiC,IAAI,CAAC;MAClD,MAAMC,eAAe,GAAGlC,WAAW,CAACmC,IAAI;;MAExC;MACA,MAAMC,eAAe,GAAG,CAAC,CAAC;;MAE1B;MACA,MAAMC,oBAAoB,GAAG,MAAMlD,KAAK,CAACwC,GAAG,CAC1C,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,yCAAyCQ,kBAAkB,CAACnB,YAAY,CAAC,EAAE,EAC3GI,MACF,CAAC;MAED,MAAMgB,eAAe,GAAGF,oBAAoB,CAACG,IAAI,IAAI,EAAE;;MAEvD;MACAd,QAAQ,CAACc,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI;QAC/B;QACA,MAAMC,mBAAmB,GAAGJ,eAAe,CAACK,MAAM,CAChDC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKJ,OAAO,CAACK,SAAS,IAClCF,IAAI,CAACG,GAAG,KAAKhD,WAAW,CAACgD,GAAG,IAC5BH,IAAI,CAACI,MAAM,KAAK,WAAW,CAAC;QACrC,CAAC;;QAED;QACA,MAAMC,WAAW,GAAGP,mBAAmB,CAACQ,IAAI,CAACN,IAAI,IAAI;UACnD,MAAMO,QAAQ,GAAG,IAAIpB,IAAI,CAACa,IAAI,CAACZ,IAAI,CAAC;UACpC,MAAMoB,WAAW,GAAG,IAAIrB,IAAI,CAACD,eAAe,CAAC;;UAE7C;UACA,MAAMuB,cAAc,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC3D,MAAMC,iBAAiB,GAAGJ,WAAW,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;UAEjE;UACA,OAAOF,cAAc,KAAKG,iBAAiB,IAAIZ,IAAI,CAACV,IAAI,KAAKD,eAAe;QAC9E,CAAC,CAAC;QAEFwB,OAAO,CAACC,GAAG,CAAC,WAAWjB,OAAO,CAACkB,IAAI,KAAKlB,OAAO,CAACK,SAAS,uBAAuB,EAAE;UAChFhB,eAAe,EAAEA,eAAe;UAChCG,eAAe,EAAEA,eAAe;UAChC2B,uBAAuB,EAAElB,mBAAmB,CAACC,MAAM,CAACC,IAAI,IAAI;YAC1D,MAAMO,QAAQ,GAAG,IAAIpB,IAAI,CAACa,IAAI,CAACZ,IAAI,CAAC;YACpC,MAAMoB,WAAW,GAAG,IAAIrB,IAAI,CAACD,eAAe,CAAC;YAC7C,MAAMuB,cAAc,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAMC,iBAAiB,GAAGJ,WAAW,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjE,OAAOF,cAAc,KAAKG,iBAAiB,IAAIZ,IAAI,CAACV,IAAI,KAAKD,eAAe;UAC9E,CAAC,CAAC;UACFgB,WAAW,EAAEA;QACf,CAAC,CAAC;;QAEF;QACAd,eAAe,CAACM,OAAO,CAACK,SAAS,CAAC,GAAG,CAACG,WAAW;MACnD,CAAC,CAAC;MAEFpC,sBAAsB,CAACsB,eAAe,CAAC;MACvChC,WAAW,CAACsB,QAAQ,CAACc,IAAI,IAAI,EAAE,CAAC;MAChChC,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOsD,GAAG,EAAE;MAAA,IAAAC,aAAA;MACZL,OAAO,CAACnD,KAAK,CAAC,0BAA0B,EAAE,EAAAwD,aAAA,GAAAD,GAAG,CAACpC,QAAQ,cAAAqC,aAAA,uBAAZA,aAAA,CAAcvB,IAAI,KAAIsB,GAAG,CAACE,OAAO,CAAC;MAC5ExD,QAAQ,CAAC,4CAA4C,CAAC;IACxD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACtD,eAAe,EAAE;MACpBH,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;;IAEA;IACA,IAAI,CAACK,mBAAmB,CAACF,eAAe,CAACoC,SAAS,CAAC,EAAE;MACnDvC,QAAQ,CAAC,oFAAoF,CAAC;MAC9F;IACF;IAEA,IAAI;MACF;MACA,MAAMP,QAAQ,CAACD,WAAW,CAACgD,GAAG,EAAErC,eAAe,CAAC;MAChDZ,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO+D,GAAG,EAAE;MAAA,IAAAI,cAAA,EAAAC,mBAAA;MACZT,OAAO,CAACnD,KAAK,CAAC,wBAAwB,EAAEuD,GAAG,CAAC;;MAE5C;MACA,KAAAI,cAAA,GAAIJ,GAAG,CAACpC,QAAQ,cAAAwC,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAc1B,IAAI,cAAA2B,mBAAA,eAAlBA,mBAAA,CAAoBC,QAAQ,EAAE;QAChC5D,QAAQ,CAAC,sFAAsF,CAAC;;QAEhG;QACAM,sBAAsB,CAACuD,IAAI,KAAK;UAC9B,GAAGA,IAAI;UACP,CAAC1D,eAAe,CAACoC,SAAS,GAAG;QAC/B,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLvC,QAAQ,CAAC,6CAA6C,CAAC;MACzD;IACF;EACF,CAAC;EAED,MAAM8D,gBAAgB,GAAGnE,QAAQ,CAACyC,MAAM,CAACF,OAAO;IAAA,IAAA6B,aAAA,EAAAC,kBAAA;IAAA,OAC9C,EAAAD,aAAA,GAAA7B,OAAO,CAACkB,IAAI,cAAAW,aAAA,uBAAZA,aAAA,CAAcE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjE,UAAU,CAACgE,WAAW,CAAC,CAAC,CAAC,OAAAD,kBAAA,GAC9D9B,OAAO,CAACK,SAAS,cAAAyB,kBAAA,uBAAjBA,kBAAA,CAAmBC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjE,UAAU,CAACgE,WAAW,CAAC,CAAC,CAAC;EAAA,CACrE,CAAC;EAED,IAAI,CAAC3E,MAAM,EAAE,OAAO,IAAI;EAExB,oBACER,OAAA,CAACR,eAAe;IAAA6F,QAAA,eACdrF,OAAA,CAACT,MAAM,CAAC+F,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAE;MAAE,CAAE;MACxBE,IAAI,EAAE;QAAEF,OAAO,EAAE;MAAE,CAAE;MACrBG,SAAS,EAAC,gFAAgF;MAC1FC,OAAO,EAAEnF,OAAQ;MAAA4E,QAAA,eAEjBrF,OAAA,CAACT,MAAM,CAAC+F,GAAG;QACTC,OAAO,EAAE;UAAEM,KAAK,EAAE,GAAG;UAAEL,OAAO,EAAE,CAAC;UAAEM,CAAC,EAAE;QAAG,CAAE;QAC3CL,OAAO,EAAE;UAAEI,KAAK,EAAE,CAAC;UAAEL,OAAO,EAAE,CAAC;UAAEM,CAAC,EAAE;QAAE,CAAE;QACxCJ,IAAI,EAAE;UAAEG,KAAK,EAAE,GAAG;UAAEL,OAAO,EAAE,CAAC;UAAEM,CAAC,EAAE;QAAG,CAAE;QACxCC,UAAU,EAAE;UAAEC,IAAI,EAAE,QAAQ;UAAEC,OAAO,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAE;QAC5DP,SAAS,EAAC,6FAA6F;QACvGC,OAAO,EAAGO,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;QAAAf,QAAA,gBAGpCrF,OAAA;UAAK2F,SAAS,EAAC,kFAAkF;UAC/FU,KAAK,EAAE;YAAEjG,UAAU,EAAE,6BAA6BH,YAAY,CAACC,OAAO,KAAKD,YAAY,CAACE,SAAS;UAAI,CAAE;UAAAkF,QAAA,gBACvGrF,OAAA;YAAK2F,SAAS,EAAC,mBAAmB;YAAAN,QAAA,gBAChCrF,OAAA,CAACN,QAAQ;cAACiG,SAAS,EAAC;YAAc;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrCzG,OAAA;cAAI2F,SAAS,EAAC,mBAAmB;cAAAN,QAAA,EAAC;YAAc;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNzG,OAAA;YACE4F,OAAO,EAAEnF,OAAQ;YACjBkF,SAAS,EAAC,kDAAkD;YAAAN,QAAA,eAE5DrF,OAAA,CAACP,OAAO;cAACkG,SAAS,EAAC;YAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNzG,OAAA;UAAK2F,SAAS,EAAC,KAAK;UAAAN,QAAA,GACjBpE,KAAK,iBACJjB,OAAA;YAAK2F,SAAS,EAAC,mEAAmE;YAAAN,QAAA,EAC/EpE;UAAK;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDzG,OAAA;YAAK2F,SAAS,EAAC,MAAM;YAAAN,QAAA,eACnBrF,OAAA;cAAK2F,SAAS,EAAC,UAAU;cAAAN,QAAA,gBACvBrF,OAAA;gBAAK2F,SAAS,EAAC,sEAAsE;gBAAAN,QAAA,eACnFrF,OAAA,CAACL,QAAQ;kBAACgG,SAAS,EAAC;gBAAuB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNzG,OAAA;gBACEgG,IAAI,EAAC,MAAM;gBACXL,SAAS,EAAC,gEAAgE;gBAC1EU,KAAK,EAAE;kBACLK,OAAO,EAAE,MAAM;kBACfC,SAAS,EAAE,MAAM;kBACjBC,WAAW,EAAE,SAAS;kBACtB,QAAQ,EAAE;oBAAEA,WAAW,EAAE3G,YAAY,CAACC;kBAAQ;gBAChD,CAAE;gBACF2G,WAAW,EAAC,kCAAkC;gBAC9CC,KAAK,EAAE3F,UAAW;gBAClB4F,QAAQ,EAAGZ,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACa,MAAM,CAACF,KAAK;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzG,OAAA;YAAK2F,SAAS,EAAC,MAAM;YAAAN,QAAA,EAClBtE,OAAO,gBACNf,OAAA;cAAK2F,SAAS,EAAC,uCAAuC;cAAAN,QAAA,eACpDrF,OAAA,CAACJ,SAAS;gBAAC+F,SAAS,EAAC,sBAAsB;gBAACU,KAAK,EAAE;kBAAEY,KAAK,EAAEhH,YAAY,CAACC;gBAAQ;cAAE;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,GACJzB,gBAAgB,CAACkC,MAAM,KAAK,CAAC,gBAC/BlH,OAAA;cAAK2F,SAAS,EAAC,gCAAgC;cAAAN,QAAA,EAAC;YAEhD;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAENzG,OAAA;cAAK2F,SAAS,EAAC,0DAA0D;cAAAN,QAAA,EACtEL,gBAAgB,CAACmC,GAAG,CAAE/D,OAAO,iBAC5BpD,OAAA;gBAEE2F,SAAS,EAAE,2CACTpE,mBAAmB,CAAC6B,OAAO,CAACK,SAAS,CAAC,GAClC,iBAAiB,IAAI,CAAApC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoC,SAAS,MAAKL,OAAO,CAACK,SAAS,GAAG,kBAAkB,GAAG,EAAE,CAAC,GAChG,+BAA+B,EAClC;gBACH4C,KAAK,EAAE;kBACLe,eAAe,EAAE,CAAA/F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoC,SAAS,MAAKL,OAAO,CAACK,SAAS,GAC7D,GAAGxD,YAAY,CAACC,OAAO,IAAI,GAC3B,CAACqB,mBAAmB,CAAC6B,OAAO,CAACK,SAAS,CAAC,GACrC,SAAS,GACTxD,YAAY,CAACG,UAAU;kBAC7BwG,WAAW,EAAE,CAAAvF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoC,SAAS,MAAKL,OAAO,CAACK,SAAS,GACzDxD,YAAY,CAACC,OAAO,GACpB,CAACqB,mBAAmB,CAAC6B,OAAO,CAACK,SAAS,CAAC,GACrC,WAAW,GACX;gBACR,CAAE;gBACFmC,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACA,IAAIrE,mBAAmB,CAAC6B,OAAO,CAACK,SAAS,CAAC,EAAE;oBAC1CnC,kBAAkB,CAAC8B,OAAO,CAAC;oBAC3BlC,QAAQ,CAAC,EAAE,CAAC;kBACd,CAAC,MAAM;oBACLA,QAAQ,CAAC,oFAAoF,CAAC;kBAChG;gBACF,CAAE;gBAAAmE,QAAA,eAEFrF,OAAA;kBAAK2F,SAAS,EAAC,mCAAmC;kBAAAN,QAAA,gBAChDrF,OAAA;oBAAK2F,SAAS,EAAC,mBAAmB;oBAAAN,QAAA,gBAChCrF,OAAA;sBAAK2F,SAAS,EAAC,uBAAuB;sBAACU,KAAK,EAAE;wBAAEe,eAAe,EAAE,GAAGnH,YAAY,CAACC,OAAO;sBAAK,CAAE;sBAAAmF,QAAA,eAC7FrF,OAAA,CAACN,QAAQ;wBAACiG,SAAS,EAAC,SAAS;wBAACU,KAAK,EAAE;0BAAEY,KAAK,EAAEhH,YAAY,CAACC;wBAAQ;sBAAE;wBAAAoG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC,eACNzG,OAAA;sBAAAqF,QAAA,gBACErF,OAAA;wBAAI2F,SAAS,EAAC,2BAA2B;wBAAAN,QAAA,EAAEjC,OAAO,CAACkB;sBAAI;wBAAAgC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7DzG,OAAA;wBAAG2F,SAAS,EAAC,uBAAuB;wBAAAN,QAAA,GAAC,MAAI,EAACjC,OAAO,CAACK,SAAS;sBAAA;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzG,OAAA;oBAAAqF,QAAA,EACG9D,mBAAmB,CAAC6B,OAAO,CAACK,SAAS,CAAC,gBACrCzD,OAAA;sBAAM2F,SAAS,EAAC,4DAA4D;sBAAAN,QAAA,EAAC;oBAAS;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAE7FzG,OAAA;sBAAM2F,SAAS,EAAC,wDAAwD;sBAAAN,QAAA,EAAC;oBAAa;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAC7F;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA7CDrD,OAAO,CAACM,GAAG,IAAIN,OAAO,CAACK,SAAS;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8ClC,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzG,OAAA;UAAK2F,SAAS,EAAC,gFAAgF;UAAAN,QAAA,gBAC7FrF,OAAA;YACE4F,OAAO,EAAEnF,OAAQ;YACjBkF,SAAS,EAAC,4EAA4E;YACtFU,KAAK,EAAE;cACLO,WAAW,EAAE,SAAS;cACtBK,KAAK,EAAEhH,YAAY,CAACI;YACtB,CAAE;YAAAgF,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzG,OAAA;YACE4F,OAAO,EAAEjB,YAAa;YACtB0C,QAAQ,EAAE,CAAChG,eAAe,IAAIN,OAAQ;YACtC4E,SAAS,EAAC,8GAA8G;YACxHU,KAAK,EAAE;cACLjG,UAAU,EAAE,6BAA6BH,YAAY,CAACC,OAAO,KAAKD,YAAY,CAACE,SAAS,GAAG;cAC3FqF,OAAO,EAAE,CAACnE,eAAe,IAAIN,OAAO,GAAG,GAAG,GAAG,CAAC;cAC9CuG,MAAM,EAAE,CAACjG,eAAe,IAAIN,OAAO,GAAG,aAAa,GAAG;YACxD,CAAE;YAAAsE,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEtB,CAAC;AAAC7F,EAAA,CAhSIL,kBAAkB;EAAA,QAOET,OAAO;AAAA;AAAAyH,EAAA,GAP3BhH,kBAAkB;AAkSxB,eAAeA,kBAAkB;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}