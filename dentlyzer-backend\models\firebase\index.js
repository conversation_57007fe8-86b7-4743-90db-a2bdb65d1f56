const Joi = require('joi');

// Collection names
const COLLECTIONS = {
  PATIENTS: 'patients',
  STUDENTS: 'students',
  SUPERVISORS: 'supervisors',
  ADMINS: 'admins',
  ASSISTANTS: 'assistants',
  UNIVERSITIES: 'universities',
  APPOINTMENTS: 'appointments',
  REVIEWS: 'reviews',
  TEETH_CHARTS: 'teethCharts',
  LAB_REQUESTS: 'labRequests',
  PROCEDURE_REQUESTS: 'procedureRequests',
  NEWS: 'news',
  ACTIVITY_LOGS: 'activityLogs',
  PAYMENTS: 'payments',
  CONFIGS: 'configs'
};

// Common validation schemas
const commonSchemas = {
  objectId: Joi.string().pattern(/^[a-zA-Z0-9]{20}$/), // Firestore document ID pattern
  email: Joi.string().email().required(),
  phone: Joi.string().required(),
  date: Joi.date(),
  timestamp: Joi.object({
    _seconds: Joi.number(),
    _nanoseconds: Joi.number()
  })
};

// Bilingual string schema
const bilingualStringSchema = Joi.object({
  en: Joi.string().required(),
  ar: Joi.string().required()
});

// Consent schema
const consentSchema = Joi.object({
  signatureText: Joi.string().default(''),
  signatureImage: Joi.string().default(''),
  signedAt: Joi.date().allow(null),
  isSigned: Joi.boolean().default(false)
});

// Sheet schema for treatment sheets
const sheetSchema = Joi.object({
  type: Joi.string()
    .valid('Operative', 'Fixed Prosthodontics', 'Removable Prosthodontics', 'Endodontics', 'Periodontics')
    .required(),
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date()),
  details: Joi.object({
    diagnosis: Joi.string().required(),
    treatmentPlan: Joi.string().required(),
    notes: Joi.string().default(''),
    specificData: Joi.object().default({})
  }).required()
});

// Time slot schema
const timeSlotSchema = Joi.object({
  date: Joi.date().required(),
  time: Joi.string().required(),
  isAvailable: Joi.boolean().default(true),
  duration: Joi.number().default(120)
});

// Address schema
const addressSchema = Joi.object({
  street: bilingualStringSchema,
  city: bilingualStringSchema,
  country: bilingualStringSchema,
  postalCode: Joi.string().allow('')
});

// Contact info schema
const contactInfoSchema = Joi.object({
  phone: Joi.string().required(),
  email: Joi.string().email().required(),
  website: Joi.string().allow('')
});

// Medical info schema
const medicalInfoSchema = Joi.object({
  chronicDiseases: Joi.array().items(Joi.string()).default([]),
  recentSurgicalProcedures: Joi.string().default(''),
  currentMedications: Joi.string().default(''),
  chiefComplaint: Joi.string().default('')
});

// Image/file schema
const imageSchema = Joi.object({
  url: Joi.string().required(),
  date: Joi.date().default(() => new Date()),
  note: Joi.string().default('')
});

module.exports = {
  COLLECTIONS,
  commonSchemas,
  bilingualStringSchema,
  consentSchema,
  sheetSchema,
  timeSlotSchema,
  addressSchema,
  contactInfoSchema,
  medicalInfoSchema,
  imageSchema
};
