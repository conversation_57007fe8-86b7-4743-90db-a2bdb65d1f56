const { connectFirestore, FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

// Sample data for testing Firebase setup
const sampleData = {
  universities: [
    {
      universityId: 'AIU',
      name: {
        en: 'Arab International University',
        ar: 'الجامعة العربية الدولية'
      },
      description: {
        en: 'Leading dental education institution',
        ar: 'مؤسسة تعليمية رائدة في طب الأسنان'
      },
      dentistryInfo: {
        en: 'Comprehensive dental programs',
        ar: 'برامج شاملة في طب الأسنان'
      },
      facilities: {
        en: 'Modern dental facilities',
        ar: 'مرافق حديثة لطب الأسنان'
      },
      program: {
        en: 'Bachelor of Dental Surgery',
        ar: 'بكالوريوس جراحة الأسنان'
      },
      dentistryServices: [
        { en: 'General Dentistry', ar: 'طب الأسنان العام' },
        { en: 'Orthodontics', ar: 'تقويم الأسنان' },
        { en: 'Oral Surgery', ar: 'جراحة الفم' }
      ],
      address: {
        street: { en: '123 University Street', ar: 'شارع الجامعة 123' },
        city: { en: 'Damascus', ar: 'دمشق' },
        country: { en: 'Syria', ar: 'سوريا' },
        postalCode: '12345'
      },
      contactInfo: {
        phone: '+963-11-1234567',
        email: '<EMAIL>',
        website: 'https://aiu.edu.sy'
      },
      image: '',
      logo: '',
      mapUrl: '',
      timeSlots: [],
      slotBeginDate: new Date('2024-01-01'),
      slotEndDate: new Date('2024-12-31'),
      holidays: ['Friday'],
      students: [],
      supervisors: [],
      admins: [],
      assistants: []
    }
  ],
  
  admins: [
    {
      email: '<EMAIL>',
      password: '$2a$10$example.hash.here', // This should be properly hashed
      plainPassword: 'admin123',
      name: 'Admin User',
      role: 'admin',
      university: 'AIU'
    }
  ],
  
  students: [
    {
      studentId: 'STU001',
      email: '<EMAIL>',
      password: '$2a$10$example.hash.here', // This should be properly hashed
      plainPassword: 'student123',
      name: 'John Doe',
      role: 'student',
      patients: [],
      reviews: [],
      appointments: [],
      university: 'AIU'
    }
  ],
  
  supervisors: [
    {
      email: '<EMAIL>',
      password: '$2a$10$example.hash.here', // This should be properly hashed
      plainPassword: 'supervisor123',
      name: 'Dr. Jane Smith',
      role: 'supervisor',
      university: 'AIU',
      students: []
    }
  ]
};

const migrateToFirebase = async () => {
  try {
    console.log('🚀 Starting Firebase migration...');
    
    // Connect to Firebase
    await connectFirestore();
    
    console.log('📊 Creating sample data in Firebase...');
    
    // Create universities
    for (const university of sampleData.universities) {
      const created = await FirestoreHelpers.create(COLLECTIONS.UNIVERSITIES, {
        ...university,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`✅ Created university: ${university.universityId}`);
    }
    
    // Create admins
    for (const admin of sampleData.admins) {
      const created = await FirestoreHelpers.create(COLLECTIONS.ADMINS, {
        ...admin,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`✅ Created admin: ${admin.email}`);
    }
    
    // Create students
    for (const student of sampleData.students) {
      const created = await FirestoreHelpers.create(COLLECTIONS.STUDENTS, {
        ...student,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`✅ Created student: ${student.email}`);
    }
    
    // Create supervisors
    for (const supervisor of sampleData.supervisors) {
      const created = await FirestoreHelpers.create(COLLECTIONS.SUPERVISORS, {
        ...supervisor,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`✅ Created supervisor: ${supervisor.email}`);
    }
    
    console.log('🎉 Firebase migration completed successfully!');
    console.log('📝 Sample data created:');
    console.log('   - 1 University (AIU)');
    console.log('   - 1 Admin user');
    console.log('   - 1 Student user');
    console.log('   - 1 Supervisor user');
    console.log('');
    console.log('🔑 Login credentials:');
    console.log('   Admin: <EMAIL> / admin123');
    console.log('   Student: <EMAIL> / student123');
    console.log('   Supervisor: <EMAIL> / supervisor123');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
};

// Run migration if this file is executed directly
if (require.main === module) {
  migrateToFirebase()
    .then(() => {
      console.log('Migration completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateToFirebase };
