# Firebase Migration Setup Guide

## Overview
This guide explains how to set up and use the Firebase migration for the ODenta backend.

## Prerequisites
1. Firebase project created (odenta-82359)
2. Firebase Admin SDK credentials
3. Node.js environment with required dependencies

## Environment Variables

Add the following to your `.env` file:

```env
# Firebase Configuration (optional - for production with service account)
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"odenta-82359",...}

# Other existing environment variables
NODE_ENV=development
PORT=5000
JWT_SECRET=your_jwt_secret
JWT_ACCESS_EXPIRATION=24h
FRONTEND_URL=http://localhost:3000
MAX_FILE_SIZE=50mb
UPLOAD_PATH=./uploads
```

## Firebase Setup Steps

### 1. Install Dependencies
```bash
npm install firebase-admin
```

### 2. Initialize Firebase (Development)
For development, the app will use the default Firebase configuration. Make sure you have:
- Firebase project ID: `odenta-82359`
- Proper Firebase rules configured

### 3. Run Migration
```bash
npm run migrate:firebase
```

This will:
- Initialize Firebase connection
- Create sample data including:
  - 1 University (AIU)
  - 1 Admin user
  - 1 Student user  
  - 1 Supervisor user

### 4. Test Login Credentials
After migration, you can test with these credentials:
- **Admin**: <EMAIL> / admin123
- **Student**: <EMAIL> / student123
- **Supervisor**: <EMAIL> / supervisor123

## Firebase Collections Structure

The following Firestore collections will be created:

- `patients` - Patient records
- `students` - Student accounts
- `supervisors` - Supervisor accounts
- `admins` - Admin accounts
- `assistants` - Assistant accounts
- `universities` - University information
- `appointments` - Appointment records
- `reviews` - Review submissions
- `teethCharts` - Dental charts
- `labRequests` - Lab request records
- `procedureRequests` - Procedure requests
- `news` - News articles
- `activityLogs` - System activity logs
- `payments` - Payment records
- `configs` - System configurations

## Key Changes from MongoDB

1. **Document IDs**: Firestore uses auto-generated IDs instead of ObjectIds
2. **References**: String IDs instead of ObjectId references
3. **Queries**: Different query syntax using field/operator/value structure
4. **Transactions**: Firebase transaction syntax
5. **Validation**: Joi schemas for data validation

## Production Setup

For production deployment:

1. Generate a Firebase service account key
2. Add the service account JSON to `FIREBASE_SERVICE_ACCOUNT_KEY` environment variable
3. Ensure proper Firebase security rules are configured
4. Set up proper indexing for queries

## Troubleshooting

### Common Issues:

1. **Permission Denied**: Check Firebase security rules
2. **Connection Failed**: Verify project ID and credentials
3. **Validation Errors**: Check data format against Joi schemas

### Debug Mode:
Set `NODE_ENV=development` to see detailed error messages.

## Migration Notes

- All existing MongoDB operations have been converted to Firestore operations
- Password hashing remains the same (bcryptjs)
- File uploads still use Cloudinary
- Socket.io functionality preserved
- JWT authentication unchanged

## Next Steps

1. Test all API endpoints
2. Verify data integrity
3. Update frontend API calls if needed
4. Configure production Firebase rules
5. Set up monitoring and logging
