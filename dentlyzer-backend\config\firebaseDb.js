const { initializeFirebase, getFirestore } = require('./firebase');
const config = require('./config');

let db;

const connectFirestore = async () => {
  try {
    // Initialize Firebase
    initializeFirebase();
    
    // Get Firestore instance
    db = getFirestore();
    
    console.log(`✅ Connected to Firebase Firestore - Project: odenta-82359`);
    console.log(`🌐 Environment: ${config.NODE_ENV}`);
    
    return db;
  } catch (error) {
    console.error('❌ Firebase Firestore connection error:', error.message);
    process.exit(1);
  }
};

// Get the database instance
const getDb = () => {
  if (!db) {
    throw new Error('Database not initialized. Call connectFirestore() first.');
  }
  return db;
};

// Helper functions for common Firestore operations
const FirestoreHelpers = {
  // Create a document
  async create(collection, data, docId = null) {
    const db = getDb();
    const collectionRef = db.collection(collection);
    
    if (docId) {
      await collectionRef.doc(docId).set(data);
      return { id: docId, ...data };
    } else {
      const docRef = await collectionRef.add(data);
      return { id: docRef.id, ...data };
    }
  },

  // Find a document by ID
  async findById(collection, id) {
    const db = getDb();
    const doc = await db.collection(collection).doc(id).get();
    
    if (!doc.exists) {
      return null;
    }
    
    return { id: doc.id, ...doc.data() };
  },

  // Find documents with query
  async find(collection, whereClause = null, orderBy = null, limit = null) {
    const db = getDb();
    let query = db.collection(collection);
    
    if (whereClause) {
      query = query.where(whereClause.field, whereClause.operator, whereClause.value);
    }
    
    if (orderBy) {
      query = query.orderBy(orderBy.field, orderBy.direction || 'asc');
    }
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  },

  // Find one document
  async findOne(collection, whereClause) {
    const results = await this.find(collection, whereClause, null, 1);
    return results.length > 0 ? results[0] : null;
  },

  // Update a document
  async update(collection, id, data) {
    const db = getDb();
    await db.collection(collection).doc(id).update(data);
    return { id, ...data };
  },

  // Delete a document
  async delete(collection, id) {
    const db = getDb();
    await db.collection(collection).doc(id).delete();
    return { id };
  },

  // Count documents
  async count(collection, whereClause = null) {
    const db = getDb();
    let query = db.collection(collection);
    
    if (whereClause) {
      query = query.where(whereClause.field, whereClause.operator, whereClause.value);
    }
    
    const snapshot = await query.get();
    return snapshot.size;
  },

  // Batch operations
  async batchWrite(operations) {
    const db = getDb();
    const batch = db.batch();
    
    operations.forEach(op => {
      const docRef = db.collection(op.collection).doc(op.id);
      
      switch (op.type) {
        case 'set':
          batch.set(docRef, op.data);
          break;
        case 'update':
          batch.update(docRef, op.data);
          break;
        case 'delete':
          batch.delete(docRef);
          break;
      }
    });
    
    await batch.commit();
  },

  // Transaction
  async runTransaction(updateFunction) {
    const db = getDb();
    return await db.runTransaction(updateFunction);
  }
};

module.exports = {
  connectFirestore,
  getDb,
  FirestoreHelpers
};
