[{"D:\\Dentlyzer\\dentlyzer-frontend\\src\\index.js": "1", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\App.js": "2", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\reportWebVitals.js": "3", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Home.jsx": "4", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Loader.jsx": "5", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Footer.jsx": "6", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\i18n.js": "7", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "8", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx": "9", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "10", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "11", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Login.jsx": "12", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\About.jsx": "13", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "14", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "15", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx": "16", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "17", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Clinics.jsx": "18", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx": "19", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Payment.jsx": "20", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Select.jsx": "21", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "22", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx": "23", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "24", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\router.js": "25", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\context\\AuthContext.js": "26", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "27", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\XRay.jsx": "28", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "29", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "30", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "31", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "32", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "33", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Patients.jsx": "34", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "35", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "36", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "37", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx": "38", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "39", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx": "40", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx": "41", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Patients.jsx": "42", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx": "43", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\XRay.jsx": "44", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx": "45", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx": "46", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx": "47", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "48", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "49", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "50", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "51", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx": "52", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx": "53", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx": "54", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "55", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Profile.jsx": "56", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "57", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "58", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "59", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\News.jsx": "60", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "61", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\people.jsx": "62", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\People.jsx": "63", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\index.js": "64", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\reportWebVitals.js": "65", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\App.js": "66", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\router.js": "67", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\i18n.js": "68", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\context\\AuthContext.js": "69", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Profile.jsx": "70", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Login.jsx": "71", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx": "72", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Home.jsx": "73", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "74", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx": "75", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "76", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "77", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\About.jsx": "78", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Select.jsx": "79", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "80", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Clinics.jsx": "81", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx": "82", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "83", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "84", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx": "85", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "86", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Payment.jsx": "87", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "88", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Patients.jsx": "89", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "90", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "91", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "92", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "93", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "94", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "95", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\XRay.jsx": "96", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "97", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "98", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "99", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\People.jsx": "100", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "101", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "102", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "103", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "104", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\News.jsx": "105", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx": "106", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\XRay.jsx": "107", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Patients.jsx": "108", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx": "109", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx": "110", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx": "111", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx": "112", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx": "113", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx": "114", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "115", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "116", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Loader.jsx": "117", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "118", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Footer.jsx": "119", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "120", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "121", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "122", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx": "123", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx": "124", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx": "125", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx": "126", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx": "127", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx": "128", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx": "129", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\News.jsx": "130", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx": "131", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx": "132", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx": "133", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx": "134", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sheets.jsx": "135", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx": "136", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\History.jsx": "137", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx": "138", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx": "139", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx": "140", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx": "141", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx": "142", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Consent.jsx": "143", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx": "144", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx": "145", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx": "146", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Support.jsx": "147", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx": "148", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx": "149", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx": "150", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx": "151", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx": "152", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx": "153", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx": "154", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\index.js": "155", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\reportWebVitals.js": "156", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\App.js": "157", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\router.js": "158", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\context\\AuthContext.js": "159", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\i18n.js": "160", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Support.jsx": "161", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Profile.jsx": "162", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx": "163", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Home.jsx": "164", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "165", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "166", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Login.jsx": "167", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\About.jsx": "168", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "169", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "170", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "171", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "172", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "173", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "174", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "175", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Patients.jsx": "176", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "177", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "178", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sheets.jsx": "179", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "180", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "181", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "182", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "183", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "184", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Consent.jsx": "185", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\History.jsx": "186", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "187", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\People.jsx": "188", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\News.jsx": "189", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "190", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "191", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "192", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "193", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx": "194", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "195", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx": "196", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\News.jsx": "197", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx": "198", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx": "199", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx": "200", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx": "201", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx": "202", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "203", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "204", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Loader.jsx": "205", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Footer.jsx": "206", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "207", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "208", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx": "209", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx": "210", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx": "211", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx": "212", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx": "213", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "214", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx": "215", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx": "216", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx": "217", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx": "218", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx": "219", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx": "220", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx": "221", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx": "222", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx": "223", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx": "224", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx": "225", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\ProcedureRequests.jsx": "226", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ProcedureRequestsWidget.jsx": "227", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\AppointmentsWidget.jsx": "228", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\utils\\pdfUtils.js": "229", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Activity.jsx": "230", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Patients.jsx": "231", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Lab.jsx": "232", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\LabRequests.jsx": "233", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\LabRequests.jsx": "234"}, {"size": 535, "mtime": 1745523871993, "results": "235", "hashOfConfig": "236"}, {"size": 344, "mtime": 1745593572980, "results": "237", "hashOfConfig": "236"}, {"size": 362, "mtime": 1745521607447, "results": "238", "hashOfConfig": "236"}, {"size": 25522, "mtime": 1746026848966, "results": "239", "hashOfConfig": "236"}, {"size": 4449, "mtime": 1746028375819, "results": "240", "hashOfConfig": "236"}, {"size": 16619, "mtime": 1746027908524, "results": "241", "hashOfConfig": "236"}, {"size": 41067, "mtime": 1746027767487, "results": "242", "hashOfConfig": "236"}, {"size": 15000, "mtime": 1746027574659, "results": "243", "hashOfConfig": "236"}, {"size": 12368, "mtime": 1745533141636, "results": "244", "hashOfConfig": "236"}, {"size": 13375, "mtime": 1745936744323, "results": "245", "hashOfConfig": "236"}, {"size": 12529, "mtime": 1745533076800, "results": "246", "hashOfConfig": "236"}, {"size": 8477, "mtime": 1745593861157, "results": "247", "hashOfConfig": "236"}, {"size": 16004, "mtime": 1745936541582, "results": "248", "hashOfConfig": "236"}, {"size": 25091, "mtime": 1745537009810, "results": "249", "hashOfConfig": "236"}, {"size": 19988, "mtime": 1745737631275, "results": "250", "hashOfConfig": "236"}, {"size": 17000, "mtime": 1745737806371, "results": "251", "hashOfConfig": "236"}, {"size": 10462, "mtime": 1745736150751, "results": "252", "hashOfConfig": "236"}, {"size": 10639, "mtime": 1745737746063, "results": "253", "hashOfConfig": "236"}, {"size": 0, "mtime": 1745579442828, "results": "254", "hashOfConfig": "236"}, {"size": 0, "mtime": 1745579449398, "results": "255", "hashOfConfig": "236"}, {"size": 4794, "mtime": 1745937254876, "results": "256", "hashOfConfig": "236"}, {"size": 17070, "mtime": 1745938807584, "results": "257", "hashOfConfig": "236"}, {"size": 0, "mtime": 1745579393455, "results": "258", "hashOfConfig": "236"}, {"size": 14691, "mtime": 1745777641172, "results": "259", "hashOfConfig": "236"}, {"size": 8330, "mtime": 1745869519257, "results": "260", "hashOfConfig": "236"}, {"size": 2667, "mtime": 1745659005519, "results": "261", "hashOfConfig": "236"}, {"size": 38724, "mtime": 1745692735196, "results": "262", "hashOfConfig": "236"}, {"size": 11723, "mtime": 1745617858943, "results": "263", "hashOfConfig": "236"}, {"size": 12455, "mtime": 1745617379798, "results": "264", "hashOfConfig": "236"}, {"size": 17414, "mtime": 1745781818524, "results": "265", "hashOfConfig": "236"}, {"size": 27311, "mtime": 1745686427761, "results": "266", "hashOfConfig": "236"}, {"size": 72447, "mtime": 1745704882940, "results": "267", "hashOfConfig": "236"}, {"size": 29816, "mtime": 1745790566641, "results": "268", "hashOfConfig": "236"}, {"size": 33179, "mtime": 1745623491029, "results": "269", "hashOfConfig": "236"}, {"size": 24357, "mtime": 1745655454135, "results": "270", "hashOfConfig": "236"}, {"size": 13189, "mtime": 1745869227266, "results": "271", "hashOfConfig": "236"}, {"size": 28031, "mtime": 1745696382431, "results": "272", "hashOfConfig": "236"}, {"size": 20716, "mtime": 1745593781630, "results": "273", "hashOfConfig": "236"}, {"size": 0, "mtime": 1745265060947, "results": "274", "hashOfConfig": "236"}, {"size": 34793, "mtime": 1745593788566, "results": "275", "hashOfConfig": "236"}, {"size": 0, "mtime": 1745781813233, "results": "276", "hashOfConfig": "236"}, {"size": 33183, "mtime": 1745593804781, "results": "277", "hashOfConfig": "236"}, {"size": 6170, "mtime": 1743975365356, "results": "278", "hashOfConfig": "236"}, {"size": 6117, "mtime": 1743975382403, "results": "279", "hashOfConfig": "236"}, {"size": 13795, "mtime": 1744193211886, "results": "280", "hashOfConfig": "236"}, {"size": 15608, "mtime": 1744192669925, "results": "281", "hashOfConfig": "236"}, {"size": 54388, "mtime": 1745274429676, "results": "282", "hashOfConfig": "236"}, {"size": 33472, "mtime": 1745792982698, "results": "283", "hashOfConfig": "236"}, {"size": 3201, "mtime": 1745616939355, "results": "284", "hashOfConfig": "236"}, {"size": 10848, "mtime": 1745781804706, "results": "285", "hashOfConfig": "236"}, {"size": 3380, "mtime": 1745617128650, "results": "286", "hashOfConfig": "236"}, {"size": 4227, "mtime": 1745266557952, "results": "287", "hashOfConfig": "236"}, {"size": 4300, "mtime": 1745260321900, "results": "288", "hashOfConfig": "236"}, {"size": 8692, "mtime": 1744048711130, "results": "289", "hashOfConfig": "236"}, {"size": 24004, "mtime": 1745789621184, "results": "290", "hashOfConfig": "236"}, {"size": 5842, "mtime": 1745909321963, "results": "291", "hashOfConfig": "236"}, {"size": 3575, "mtime": 1745869264459, "results": "292", "hashOfConfig": "236"}, {"size": 8028, "mtime": 1745867656061, "results": "293", "hashOfConfig": "236"}, {"size": 13592, "mtime": 1745867635690, "results": "294", "hashOfConfig": "236"}, {"size": 6292, "mtime": 1745869103376, "results": "295", "hashOfConfig": "236"}, {"size": 11714, "mtime": 1745869124338, "results": "296", "hashOfConfig": "236"}, {"size": 13725, "mtime": 1745869505266, "results": "297", "hashOfConfig": "236"}, {"size": 18292, "mtime": 1745916840861, "results": "298", "hashOfConfig": "236"}, {"size": 535, "mtime": 1745523871993, "results": "299", "hashOfConfig": "300"}, {"size": 362, "mtime": 1745521607447, "results": "301", "hashOfConfig": "300"}, {"size": 344, "mtime": 1745593572980, "results": "302", "hashOfConfig": "300"}, {"size": 10910, "mtime": 1747475995764, "results": "303", "hashOfConfig": "300"}, {"size": 50383, "mtime": 1747404842150, "results": "304", "hashOfConfig": "300"}, {"size": 2667, "mtime": 1745659005519, "results": "305", "hashOfConfig": "300"}, {"size": 5850, "mtime": 1747428149748, "results": "306", "hashOfConfig": "300"}, {"size": 10684, "mtime": 1747425747846, "results": "307", "hashOfConfig": "300"}, {"size": 12527, "mtime": 1747425232861, "results": "308", "hashOfConfig": "300"}, {"size": 25785, "mtime": 1747325877026, "results": "309", "hashOfConfig": "300"}, {"size": 12683, "mtime": 1747424765007, "results": "310", "hashOfConfig": "300"}, {"size": 18324, "mtime": 1747494439022, "results": "311", "hashOfConfig": "300"}, {"size": 17132, "mtime": 1747424064804, "results": "312", "hashOfConfig": "300"}, {"size": 25328, "mtime": 1747478452947, "results": "313", "hashOfConfig": "300"}, {"size": 18234, "mtime": 1747423594113, "results": "314", "hashOfConfig": "300"}, {"size": 3589, "mtime": 1747425356350, "results": "315", "hashOfConfig": "300"}, {"size": 22212, "mtime": 1747494311372, "results": "316", "hashOfConfig": "300"}, {"size": 14883, "mtime": 1747494399105, "results": "317", "hashOfConfig": "300"}, {"size": 0, "mtime": 1745579442828, "results": "318", "hashOfConfig": "300"}, {"size": 11180, "mtime": 1747494346747, "results": "319", "hashOfConfig": "300"}, {"size": 15376, "mtime": 1747479345968, "results": "320", "hashOfConfig": "300"}, {"size": 0, "mtime": 1745579393455, "results": "321", "hashOfConfig": "300"}, {"size": 17301, "mtime": 1747479769046, "results": "322", "hashOfConfig": "300"}, {"size": 0, "mtime": 1745579449398, "results": "323", "hashOfConfig": "300"}, {"size": 40674, "mtime": 1747514654819, "results": "324", "hashOfConfig": "300"}, {"size": 46027, "mtime": 1747514862877, "results": "325", "hashOfConfig": "300"}, {"size": 27784, "mtime": 1747476930996, "results": "326", "hashOfConfig": "300"}, {"size": 19974, "mtime": 1747476730513, "results": "327", "hashOfConfig": "300"}, {"size": 28285, "mtime": 1747465927821, "results": "328", "hashOfConfig": "300"}, {"size": 27390, "mtime": 1747475380406, "results": "329", "hashOfConfig": "300"}, {"size": 38168, "mtime": 1747466995840, "results": "330", "hashOfConfig": "300"}, {"size": 62529, "mtime": 1747466715266, "results": "331", "hashOfConfig": "300"}, {"size": 21853, "mtime": 1746816821604, "results": "332", "hashOfConfig": "300"}, {"size": 46169, "mtime": 1747476516386, "results": "333", "hashOfConfig": "300"}, {"size": 72726, "mtime": 1747477357174, "results": "334", "hashOfConfig": "300"}, {"size": 85588, "mtime": 1747503514872, "results": "335", "hashOfConfig": "300"}, {"size": 42190, "mtime": 1747519685845, "results": "336", "hashOfConfig": "300"}, {"size": 37741, "mtime": 1747521640488, "results": "337", "hashOfConfig": "300"}, {"size": 56736, "mtime": 1747520158742, "results": "338", "hashOfConfig": "300"}, {"size": 20139, "mtime": 1747545610769, "results": "339", "hashOfConfig": "300"}, {"size": 54586, "mtime": 1747521219705, "results": "340", "hashOfConfig": "300"}, {"size": 9955, "mtime": 1747521450018, "results": "341", "hashOfConfig": "300"}, {"size": 34793, "mtime": 1745593788566, "results": "342", "hashOfConfig": "300"}, {"size": 6117, "mtime": 1743975382403, "results": "343", "hashOfConfig": "300"}, {"size": 33183, "mtime": 1745593804781, "results": "344", "hashOfConfig": "300"}, {"size": 15608, "mtime": 1744192669925, "results": "345", "hashOfConfig": "300"}, {"size": 13795, "mtime": 1744193211886, "results": "346", "hashOfConfig": "300"}, {"size": 0, "mtime": 1745781813233, "results": "347", "hashOfConfig": "300"}, {"size": 49478, "mtime": 1747337212647, "results": "348", "hashOfConfig": "300"}, {"size": 20716, "mtime": 1745593781630, "results": "349", "hashOfConfig": "300"}, {"size": 6170, "mtime": 1743975365356, "results": "350", "hashOfConfig": "300"}, {"size": 101715, "mtime": 1747543720582, "results": "351", "hashOfConfig": "300"}, {"size": 3543, "mtime": 1747464059605, "results": "352", "hashOfConfig": "300"}, {"size": 4449, "mtime": 1746028375819, "results": "353", "hashOfConfig": "300"}, {"size": 15112, "mtime": 1747427483008, "results": "354", "hashOfConfig": "300"}, {"size": 16661, "mtime": 1747426879726, "results": "355", "hashOfConfig": "300"}, {"size": 3924, "mtime": 1747464099492, "results": "356", "hashOfConfig": "300"}, {"size": 9285, "mtime": 1747476809201, "results": "357", "hashOfConfig": "300"}, {"size": 4038, "mtime": 1747517788574, "results": "358", "hashOfConfig": "300"}, {"size": 8692, "mtime": 1744048711130, "results": "359", "hashOfConfig": "300"}, {"size": 4433, "mtime": 1746555422128, "results": "360", "hashOfConfig": "300"}, {"size": 4227, "mtime": 1745266557952, "results": "361", "hashOfConfig": "300"}, {"size": 6776, "mtime": 1746130339157, "results": "362", "hashOfConfig": "300"}, {"size": 3694, "mtime": 1746555400983, "results": "363", "hashOfConfig": "300"}, {"size": 32962, "mtime": 1746489951667, "results": "364", "hashOfConfig": "300"}, {"size": 46904, "mtime": 1747543535318, "results": "365", "hashOfConfig": "300"}, {"size": 21956, "mtime": 1746178385159, "results": "366", "hashOfConfig": "300"}, {"size": 52633, "mtime": 1746471592266, "results": "367", "hashOfConfig": "300"}, {"size": 45474, "mtime": 1746383841136, "results": "368", "hashOfConfig": "300"}, {"size": 1366, "mtime": 1746184118198, "results": "369", "hashOfConfig": "300"}, {"size": 15581, "mtime": 1747543564076, "results": "370", "hashOfConfig": "300"}, {"size": 14747, "mtime": 1747475215108, "results": "371", "hashOfConfig": "300"}, {"size": 53245, "mtime": 1747474059613, "results": "372", "hashOfConfig": "300"}, {"size": 23518, "mtime": 1747516218200, "results": "373", "hashOfConfig": "300"}, {"size": 38503, "mtime": 1747474312377, "results": "374", "hashOfConfig": "300"}, {"size": 9088, "mtime": 1747474414469, "results": "375", "hashOfConfig": "300"}, {"size": 19305, "mtime": 1747474671635, "results": "376", "hashOfConfig": "300"}, {"size": 14545, "mtime": 1746383814479, "results": "377", "hashOfConfig": "300"}, {"size": 1606, "mtime": 1746380988907, "results": "378", "hashOfConfig": "300"}, {"size": 22427, "mtime": 1747476255874, "results": "379", "hashOfConfig": "300"}, {"size": 59674, "mtime": 1747544383255, "results": "380", "hashOfConfig": "300"}, {"size": 3136, "mtime": 1747501530874, "results": "381", "hashOfConfig": "300"}, {"size": 18202, "mtime": 1747501681339, "results": "382", "hashOfConfig": "300"}, {"size": 11061, "mtime": 1747427987728, "results": "383", "hashOfConfig": "300"}, {"size": 33942, "mtime": 1747544346809, "results": "384", "hashOfConfig": "300"}, {"size": 21921, "mtime": 1747508196749, "results": "385", "hashOfConfig": "300"}, {"size": 19707, "mtime": 1747508361654, "results": "386", "hashOfConfig": "300"}, {"size": 3855, "mtime": 1747506687993, "results": "387", "hashOfConfig": "300"}, {"size": 12486, "mtime": 1747510357937, "results": "388", "hashOfConfig": "300"}, {"size": 13064, "mtime": 1747544432606, "results": "389", "hashOfConfig": "300"}, {"size": 18365, "mtime": 1747468109841, "results": "390", "hashOfConfig": "300"}, {"size": 535, "mtime": 1745523871993, "results": "391", "hashOfConfig": "392"}, {"size": 362, "mtime": 1745521607447, "results": "393", "hashOfConfig": "392"}, {"size": 344, "mtime": 1745593572980, "results": "394", "hashOfConfig": "392"}, {"size": 10866, "mtime": 1750631219617, "results": "395", "hashOfConfig": "392"}, {"size": 2667, "mtime": 1745659005519, "results": "396", "hashOfConfig": "392"}, {"size": 50136, "mtime": 1750641713855, "results": "397", "hashOfConfig": "392"}, {"size": 10994, "mtime": 1750641427997, "results": "398", "hashOfConfig": "392"}, {"size": 5850, "mtime": 1747428149748, "results": "399", "hashOfConfig": "392"}, {"size": 6776, "mtime": 1746130339157, "results": "400", "hashOfConfig": "392"}, {"size": 26294, "mtime": 1748027939369, "results": "401", "hashOfConfig": "392"}, {"size": 12887, "mtime": 1748031791438, "results": "402", "hashOfConfig": "392"}, {"size": 16450, "mtime": 1748016139440, "results": "403", "hashOfConfig": "392"}, {"size": 14559, "mtime": 1751801881348, "results": "404", "hashOfConfig": "392"}, {"size": 18211, "mtime": 1748029564302, "results": "405", "hashOfConfig": "392"}, {"size": 11191, "mtime": 1750636921767, "results": "406", "hashOfConfig": "392"}, {"size": 25328, "mtime": 1747478452947, "results": "407", "hashOfConfig": "392"}, {"size": 22223, "mtime": 1750638461294, "results": "408", "hashOfConfig": "392"}, {"size": 17334, "mtime": 1750635919904, "results": "409", "hashOfConfig": "392"}, {"size": 15490, "mtime": 1750641123736, "results": "410", "hashOfConfig": "392"}, {"size": 23140, "mtime": 1750635856630, "results": "411", "hashOfConfig": "392"}, {"size": 20007, "mtime": 1750637076557, "results": "412", "hashOfConfig": "392"}, {"size": 44975, "mtime": 1750636764796, "results": "413", "hashOfConfig": "392"}, {"size": 28318, "mtime": 1750637830404, "results": "414", "hashOfConfig": "392"}, {"size": 67893, "mtime": 1750636569156, "results": "415", "hashOfConfig": "392"}, {"size": 14758, "mtime": 1750637882739, "results": "416", "hashOfConfig": "392"}, {"size": 29101, "mtime": 1750710479288, "results": "417", "hashOfConfig": "392"}, {"size": 59031, "mtime": 1750637914822, "results": "418", "hashOfConfig": "392"}, {"size": 46204, "mtime": 1750696331686, "results": "419", "hashOfConfig": "392"}, {"size": 58529, "mtime": 1750636484791, "results": "420", "hashOfConfig": "392"}, {"size": 27434, "mtime": 1750636655450, "results": "421", "hashOfConfig": "392"}, {"size": 28045, "mtime": 1750637232740, "results": "422", "hashOfConfig": "392"}, {"size": 26517, "mtime": 1750637094742, "results": "423", "hashOfConfig": "392"}, {"size": 38914, "mtime": 1750635636092, "results": "424", "hashOfConfig": "392"}, {"size": 42234, "mtime": 1750635714447, "results": "425", "hashOfConfig": "392"}, {"size": 10390, "mtime": 1750626720144, "results": "426", "hashOfConfig": "392"}, {"size": 20161, "mtime": 1750635992171, "results": "427", "hashOfConfig": "392"}, {"size": 56758, "mtime": 1750715762783, "results": "428", "hashOfConfig": "392"}, {"size": 66993, "mtime": 1750715727400, "results": "429", "hashOfConfig": "392"}, {"size": 93089, "mtime": 1750637992043, "results": "430", "hashOfConfig": "392"}, {"size": 47246, "mtime": 1750638115013, "results": "431", "hashOfConfig": "392"}, {"size": 100933, "mtime": 1750636360325, "results": "432", "hashOfConfig": "392"}, {"size": 45474, "mtime": 1746383841136, "results": "433", "hashOfConfig": "392"}, {"size": 21628, "mtime": 1750636985528, "results": "434", "hashOfConfig": "392"}, {"size": 52867, "mtime": 1750638300478, "results": "435", "hashOfConfig": "392"}, {"size": 51009, "mtime": 1750638338832, "results": "436", "hashOfConfig": "392"}, {"size": 21932, "mtime": 1750635732808, "results": "437", "hashOfConfig": "392"}, {"size": 34092, "mtime": 1750715777876, "results": "438", "hashOfConfig": "392"}, {"size": 25244, "mtime": 1750636050201, "results": "439", "hashOfConfig": "392"}, {"size": 3736, "mtime": 1750628652252, "results": "440", "hashOfConfig": "392"}, {"size": 3924, "mtime": 1747464099492, "results": "441", "hashOfConfig": "392"}, {"size": 3964, "mtime": 1748031168996, "results": "442", "hashOfConfig": "392"}, {"size": 16360, "mtime": 1748031453441, "results": "443", "hashOfConfig": "392"}, {"size": 11916, "mtime": 1748030716874, "results": "444", "hashOfConfig": "392"}, {"size": 20902, "mtime": 1750637781699, "results": "445", "hashOfConfig": "392"}, {"size": 10703, "mtime": 1748045792103, "results": "446", "hashOfConfig": "392"}, {"size": 55507, "mtime": 1748045908880, "results": "447", "hashOfConfig": "392"}, {"size": 40213, "mtime": 1748045992367, "results": "448", "hashOfConfig": "392"}, {"size": 62904, "mtime": 1748046208783, "results": "449", "hashOfConfig": "392"}, {"size": 21499, "mtime": 1748046110445, "results": "450", "hashOfConfig": "392"}, {"size": 3978, "mtime": 1750641490277, "results": "451", "hashOfConfig": "392"}, {"size": 8516, "mtime": 1750616824045, "results": "452", "hashOfConfig": "392"}, {"size": 18202, "mtime": 1747501681339, "results": "453", "hashOfConfig": "392"}, {"size": 1366, "mtime": 1746184118198, "results": "454", "hashOfConfig": "392"}, {"size": 15581, "mtime": 1747543564076, "results": "455", "hashOfConfig": "392"}, {"size": 3506, "mtime": 1750641362959, "results": "456", "hashOfConfig": "392"}, {"size": 1606, "mtime": 1746380988907, "results": "457", "hashOfConfig": "392"}, {"size": 14545, "mtime": 1746383814479, "results": "458", "hashOfConfig": "392"}, {"size": 4068, "mtime": 1750641476258, "results": "459", "hashOfConfig": "392"}, {"size": 12508, "mtime": 1750714112874, "results": "460", "hashOfConfig": "392"}, {"size": 13064, "mtime": 1747544432606, "results": "461", "hashOfConfig": "392"}, {"size": 18387, "mtime": 1750637264629, "results": "462", "hashOfConfig": "392"}, {"size": 18724, "mtime": 1750638550695, "results": "463", "hashOfConfig": "392"}, {"size": 3419, "mtime": 1748043262730, "results": "464", "hashOfConfig": "392"}, {"size": 5567, "mtime": 1748043390657, "results": "465", "hashOfConfig": "392"}, {"size": 10432, "mtime": 1748045669415, "results": "466", "hashOfConfig": "392"}, {"size": 15097, "mtime": 1750638393016, "results": "467", "hashOfConfig": "392"}, {"size": 31129, "mtime": 1750635823422, "results": "468", "hashOfConfig": "392"}, {"size": 10111, "mtime": 1750636135585, "results": "469", "hashOfConfig": "392"}, {"size": 15761, "mtime": 1750636963841, "results": "470", "hashOfConfig": "392"}, {"size": 15598, "mtime": 1750638514477, "results": "471", "hashOfConfig": "392"}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1y3igld", {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18v30mu", {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gwk9g1", {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dentlyzer\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Footer.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\i18n.js", ["1174"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Contact.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Login.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1175", "1176", "1177"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Clinics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Payment.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Select.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\router.js", ["1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1187", "1188", "1189", "1190"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\XRay.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Gallery.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1191"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1192", "1193"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1194"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1195", "1196", "1197"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1198"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx", ["1199"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Patients.jsx", ["1200"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\XRay.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx", ["1201", "1202", "1203"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1204"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1205"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1206", "1207", "1208"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1209"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\people.jsx", ["1210", "1211", "1212", "1213"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1214"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\router.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\i18n.js", ["1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1231", "1232", "1233"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Login.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Contact.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1234", "1235"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Select.jsx", ["1236", "1237"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Payment.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1238", "1239", "1240", "1241"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1242", "1243", "1244", "1245"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Gallery.jsx", ["1246"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1247", "1248", "1249", "1250", "1251"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1252"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\XRay.jsx", ["1253"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1254", "1255", "1256"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1257", "1258"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1259", "1260", "1261", "1262"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1263"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1264", "1265", "1266", "1267", "1268"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", ["1269"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", ["1270", "1271", "1272", "1273"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1274"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx", ["1275"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\XRay.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Patients.jsx", ["1276"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Footer.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\News.jsx", ["1277"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx", ["1278"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx", ["1279"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sheets.jsx", ["1280"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx", ["1281"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\History.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx", ["1282"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx", ["1283"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Consent.jsx", ["1284", "1285", "1286", "1287"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx", ["1288"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx", ["1289", "1290", "1291"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Support.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx", ["1292", "1293"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx", ["1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx", ["1312", "1313", "1314", "1315", "1316", "1317", "1318"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx", ["1319"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\router.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\i18n.js", ["1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Support.jsx", ["1336"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1337", "1338", "1339"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Contact.jsx", ["1340"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Login.jsx", ["1341", "1342"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1343", "1344"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", ["1345"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1346", "1347", "1348", "1349", "1350", "1351"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Gallery.jsx", ["1352"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1353", "1354", "1355", "1356", "1357", "1358"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1359", "1360", "1361", "1362", "1363"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sheets.jsx", ["1364"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1365", "1366", "1367"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1368", "1369", "1370"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1371"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Consent.jsx", ["1372", "1373", "1374", "1375"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\History.jsx", ["1376"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1387"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1388"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", ["1389"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", ["1390", "1391", "1392", "1393"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1394", "1395", "1396", "1397"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", ["1398"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\News.jsx", ["1399"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx", ["1400"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx", ["1401", "1402", "1403"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx", ["1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx", ["1422", "1423"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx", ["1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", ["1432"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Footer.jsx", ["1433"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Navbar.jsx", ["1434", "1435", "1436"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", ["1437"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx", ["1438"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx", ["1439"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", ["1440"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx", ["1441", "1442", "1443"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx", ["1444"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx", ["1445"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx", ["1446"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx", ["1447"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx", ["1448"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\ProcedureRequests.jsx", ["1449", "1450"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ProcedureRequestsWidget.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\AppointmentsWidget.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\utils\\pdfUtils.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Activity.jsx", ["1451"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Patients.jsx", ["1452", "1453", "1454", "1455"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Lab.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\LabRequests.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\LabRequests.jsx", ["1456"], [], {"ruleId": "1457", "severity": 1, "message": "1458", "line": 354, "column": 9, "nodeType": "1459", "messageId": "1460", "endLine": 354, "endColumn": 15}, {"ruleId": "1461", "severity": 1, "message": "1462", "line": 8, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 8, "endColumn": 20}, {"ruleId": "1461", "severity": 1, "message": "1465", "line": 8, "column": 22, "nodeType": "1463", "messageId": "1464", "endLine": 8, "endColumn": 30}, {"ruleId": "1461", "severity": 1, "message": "1466", "line": 9, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1467", "line": 47, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 47, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1468", "line": 48, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 48, "endColumn": 23}, {"ruleId": "1461", "severity": 1, "message": "1469", "line": 49, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 49, "endColumn": 23}, {"ruleId": "1461", "severity": 1, "message": "1470", "line": 51, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 51, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1471", "line": 52, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 52, "endColumn": 29}, {"ruleId": "1461", "severity": 1, "message": "1472", "line": 53, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 53, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1473", "line": 54, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 54, "endColumn": 19}, {"ruleId": "1461", "severity": 1, "message": "1474", "line": 55, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 55, "endColumn": 25}, {"ruleId": "1461", "severity": 1, "message": "1475", "line": 56, "column": 8, "nodeType": "1463", "messageId": "1464", "endLine": 56, "endColumn": 27}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 9, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 17}, {"ruleId": "1461", "severity": 1, "message": "1477", "line": 9, "column": 44, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 56}, {"ruleId": "1461", "severity": 1, "message": "1478", "line": 9, "column": 58, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 73}, {"ruleId": "1461", "severity": 1, "message": "1479", "line": 10, "column": 38, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 55}, {"ruleId": "1461", "severity": 1, "message": "1480", "line": 9, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 20}, {"ruleId": "1481", "severity": 1, "message": "1482", "line": 132, "column": 6, "nodeType": "1483", "endLine": 132, "endColumn": 18, "suggestions": "1484"}, {"ruleId": "1461", "severity": 1, "message": "1485", "line": 725, "column": 11, "nodeType": "1463", "messageId": "1464", "endLine": 725, "endColumn": 21}, {"ruleId": "1486", "severity": 1, "message": "1487", "line": 535, "column": 31, "nodeType": "1488", "endLine": 544, "endColumn": 33}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1490", "line": 9, "column": 86, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 93}, {"ruleId": "1461", "severity": 1, "message": "1466", "line": 10, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1491", "line": 16, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 16, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1492", "line": 1, "column": 38, "nodeType": "1463", "messageId": "1464", "endLine": 1, "endColumn": 48}, {"ruleId": "1461", "severity": 1, "message": "1485", "line": 609, "column": 11, "nodeType": "1463", "messageId": "1464", "endLine": 609, "endColumn": 21}, {"ruleId": "1461", "severity": 1, "message": "1493", "line": 635, "column": 11, "nodeType": "1463", "messageId": "1464", "endLine": 635, "endColumn": 19}, {"ruleId": "1461", "severity": 1, "message": "1494", "line": 8, "column": 63, "nodeType": "1463", "messageId": "1464", "endLine": 8, "endColumn": 70}, {"ruleId": "1461", "severity": 1, "message": "1495", "line": 10, "column": 54, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 67}, {"ruleId": "1461", "severity": 1, "message": "1477", "line": 9, "column": 18, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 30}, {"ruleId": "1461", "severity": 1, "message": "1496", "line": 9, "column": 32, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 42}, {"ruleId": "1461", "severity": 1, "message": "1497", "line": 9, "column": 44, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 52}, {"ruleId": "1461", "severity": 1, "message": "1498", "line": 57, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 57, "endColumn": 13}, {"ruleId": "1499", "severity": 2, "message": "1500", "line": 22, "column": 85, "nodeType": "1501", "messageId": "1502", "endLine": 22, "endColumn": 94}, {"ruleId": "1499", "severity": 2, "message": "1503", "line": 24, "column": 91, "nodeType": "1501", "messageId": "1502", "endLine": 24, "endColumn": 102}, {"ruleId": "1499", "severity": 2, "message": "1504", "line": 25, "column": 94, "nodeType": "1501", "messageId": "1502", "endLine": 25, "endColumn": 102}, {"ruleId": "1481", "severity": 1, "message": "1505", "line": 67, "column": 6, "nodeType": "1483", "endLine": 67, "endColumn": 39, "suggestions": "1506"}, {"ruleId": "1481", "severity": 1, "message": "1505", "line": 81, "column": 6, "nodeType": "1483", "endLine": 81, "endColumn": 39, "suggestions": "1507"}, {"ruleId": "1457", "severity": 1, "message": "1508", "line": 338, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 338, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1509", "line": 339, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 339, "endColumn": 16}, {"ruleId": "1457", "severity": 1, "message": "1510", "line": 340, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 340, "endColumn": 19}, {"ruleId": "1457", "severity": 1, "message": "1511", "line": 341, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 341, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1512", "line": 342, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 342, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1513", "line": 343, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 343, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1514", "line": 370, "column": 7, "nodeType": "1459", "messageId": "1460", "endLine": 370, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1515", "line": 388, "column": 7, "nodeType": "1459", "messageId": "1460", "endLine": 388, "endColumn": 21}, {"ruleId": "1457", "severity": 1, "message": "1516", "line": 397, "column": 7, "nodeType": "1459", "messageId": "1460", "endLine": 397, "endColumn": 16}, {"ruleId": "1457", "severity": 1, "message": "1517", "line": 409, "column": 5, "nodeType": "1459", "messageId": "1460", "endLine": 409, "endColumn": 18}, {"ruleId": "1457", "severity": 1, "message": "1508", "line": 707, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 707, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1509", "line": 708, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 708, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1510", "line": 709, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 709, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1511", "line": 710, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 710, "endColumn": 31}, {"ruleId": "1457", "severity": 1, "message": "1512", "line": 711, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 711, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1513", "line": 712, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 712, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1477", "line": 9, "column": 18, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 30}, {"ruleId": "1461", "severity": 1, "message": "1496", "line": 9, "column": 32, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 42}, {"ruleId": "1461", "severity": 1, "message": "1497", "line": 9, "column": 44, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 52}, {"ruleId": "1461", "severity": 1, "message": "1465", "line": 8, "column": 22, "nodeType": "1463", "messageId": "1464", "endLine": 8, "endColumn": 30}, {"ruleId": "1461", "severity": 1, "message": "1466", "line": 9, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1518", "line": 1, "column": 17, "nodeType": "1463", "messageId": "1464", "endLine": 1, "endColumn": 25}, {"ruleId": "1461", "severity": 1, "message": "1519", "line": 4, "column": 41, "nodeType": "1463", "messageId": "1464", "endLine": 4, "endColumn": 53}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 9, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 17}, {"ruleId": "1461", "severity": 1, "message": "1477", "line": 9, "column": 44, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 56}, {"ruleId": "1461", "severity": 1, "message": "1478", "line": 9, "column": 58, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 73}, {"ruleId": "1461", "severity": 1, "message": "1479", "line": 10, "column": 38, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 55}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1490", "line": 9, "column": 86, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 93}, {"ruleId": "1461", "severity": 1, "message": "1520", "line": 9, "column": 95, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 103}, {"ruleId": "1461", "severity": 1, "message": "1466", "line": 10, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1498", "line": 213, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 213, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1521", "line": 9, "column": 96, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 111}, {"ruleId": "1461", "severity": 1, "message": "1522", "line": 25, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 25, "endColumn": 32}, {"ruleId": "1461", "severity": 1, "message": "1523", "line": 28, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 28, "endColumn": 25}, {"ruleId": "1461", "severity": 1, "message": "1524", "line": 202, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 202, "endColumn": 26}, {"ruleId": "1461", "severity": 1, "message": "1498", "line": 215, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 215, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 23, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 27}, {"ruleId": "1461", "severity": 1, "message": "1525", "line": 9, "column": 43, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 53}, {"ruleId": "1461", "severity": 1, "message": "1526", "line": 10, "column": 19, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 25}, {"ruleId": "1481", "severity": 1, "message": "1527", "line": 53, "column": 6, "nodeType": "1483", "endLine": 53, "endColumn": 8, "suggestions": "1528"}, {"ruleId": "1481", "severity": 1, "message": "1527", "line": 78, "column": 6, "nodeType": "1483", "endLine": 78, "endColumn": 32, "suggestions": "1529"}, {"ruleId": "1481", "severity": 1, "message": "1482", "line": 132, "column": 6, "nodeType": "1483", "endLine": 132, "endColumn": 18, "suggestions": "1530"}, {"ruleId": "1461", "severity": 1, "message": "1485", "line": 726, "column": 11, "nodeType": "1463", "messageId": "1464", "endLine": 726, "endColumn": 21}, {"ruleId": "1461", "severity": 1, "message": "1531", "line": 11, "column": 33, "nodeType": "1463", "messageId": "1464", "endLine": 11, "endColumn": 47}, {"ruleId": "1461", "severity": 1, "message": "1532", "line": 12, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1533", "line": 12, "column": 31, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 44}, {"ruleId": "1461", "severity": 1, "message": "1534", "line": 40, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 40, "endColumn": 20}, {"ruleId": "1481", "severity": 1, "message": "1505", "line": 126, "column": 6, "nodeType": "1483", "endLine": 126, "endColumn": 39, "suggestions": "1535"}, {"ruleId": "1461", "severity": 1, "message": "1531", "line": 12, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 17}, {"ruleId": "1461", "severity": 1, "message": "1491", "line": 16, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 16, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1536", "line": 17, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 17, "endColumn": 15}, {"ruleId": "1461", "severity": 1, "message": "1537", "line": 18, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 18, "endColumn": 9}, {"ruleId": "1461", "severity": 1, "message": "1538", "line": 81, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 81, "endColumn": 14}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 87, "column": 6, "nodeType": "1483", "endLine": 87, "endColumn": 29, "suggestions": "1540"}, {"ruleId": "1461", "severity": 1, "message": "1541", "line": 14, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 14, "endColumn": 10}, {"ruleId": "1461", "severity": 1, "message": "1542", "line": 16, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 16, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1543", "line": 17, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 17, "endColumn": 11}, {"ruleId": "1461", "severity": 1, "message": "1544", "line": 18, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 18, "endColumn": 19}, {"ruleId": "1481", "severity": 1, "message": "1545", "line": 72, "column": 6, "nodeType": "1483", "endLine": 72, "endColumn": 29, "suggestions": "1546"}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1498", "line": 110, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 110, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1547", "line": 5, "column": 37, "nodeType": "1463", "messageId": "1464", "endLine": 5, "endColumn": 43}, {"ruleId": "1461", "severity": 1, "message": "1548", "line": 2, "column": 103, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 110}, {"ruleId": "1461", "severity": 1, "message": "1549", "line": 25, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 25, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1550", "line": 257, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 257, "endColumn": 21}, {"ruleId": "1461", "severity": 1, "message": "1551", "line": 91, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 91, "endColumn": 31}, {"ruleId": "1461", "severity": 1, "message": "1548", "line": 2, "column": 106, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 113}, {"ruleId": "1461", "severity": 1, "message": "1552", "line": 4, "column": 23, "nodeType": "1463", "messageId": "1464", "endLine": 4, "endColumn": 32}, {"ruleId": "1461", "severity": 1, "message": "1494", "line": 4, "column": 43, "nodeType": "1463", "messageId": "1464", "endLine": 4, "endColumn": 50}, {"ruleId": "1461", "severity": 1, "message": "1553", "line": 14, "column": 11, "nodeType": "1463", "messageId": "1464", "endLine": 14, "endColumn": 15}, {"ruleId": "1461", "severity": 1, "message": "1554", "line": 28, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 28, "endColumn": 23}, {"ruleId": "1461", "severity": 1, "message": "1555", "line": 3, "column": 25, "nodeType": "1463", "messageId": "1464", "endLine": 3, "endColumn": 38}, {"ruleId": "1461", "severity": 1, "message": "1554", "line": 31, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 31, "endColumn": 23}, {"ruleId": "1481", "severity": 1, "message": "1556", "line": 98, "column": 6, "nodeType": "1483", "endLine": 98, "endColumn": 24, "suggestions": "1557"}, {"ruleId": "1461", "severity": 1, "message": "1558", "line": 219, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 219, "endColumn": 28}, {"ruleId": "1461", "severity": 1, "message": "1559", "line": 26, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 26, "endColumn": 18}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 420, "column": 6, "nodeType": "1483", "endLine": 420, "endColumn": 29, "suggestions": "1560"}, {"ruleId": "1461", "severity": 1, "message": "1561", "line": 10, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 12}, {"ruleId": "1461", "severity": 1, "message": "1562", "line": 12, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1525", "line": 14, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 14, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1543", "line": 15, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 15, "endColumn": 11}, {"ruleId": "1461", "severity": 1, "message": "1542", "line": 16, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 16, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1544", "line": 17, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 17, "endColumn": 19}, {"ruleId": "1461", "severity": 1, "message": "1563", "line": 18, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 18, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1564", "line": 19, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 19, "endColumn": 21}, {"ruleId": "1461", "severity": 1, "message": "1565", "line": 21, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 21, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1566", "line": 21, "column": 15, "nodeType": "1463", "messageId": "1464", "endLine": 21, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1567", "line": 21, "column": 20, "nodeType": "1463", "messageId": "1464", "endLine": 21, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1568", "line": 66, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 66, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1569", "line": 171, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 171, "endColumn": 36}, {"ruleId": "1461", "severity": 1, "message": "1570", "line": 185, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 185, "endColumn": 36}, {"ruleId": "1461", "severity": 1, "message": "1571", "line": 211, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 211, "endColumn": 35}, {"ruleId": "1461", "severity": 1, "message": "1572", "line": 232, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 232, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1573", "line": 268, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 268, "endColumn": 25}, {"ruleId": "1461", "severity": 1, "message": "1574", "line": 323, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 323, "endColumn": 29}, {"ruleId": "1461", "severity": 1, "message": "1562", "line": 10, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1561", "line": 12, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 12}, {"ruleId": "1461", "severity": 1, "message": "1575", "line": 73, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 73, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1559", "line": 74, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 74, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1568", "line": 82, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 82, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1498", "line": 92, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 92, "endColumn": 13}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 153, "column": 6, "nodeType": "1483", "endLine": 153, "endColumn": 29, "suggestions": "1576"}, {"ruleId": "1481", "severity": 1, "message": "1577", "line": 29, "column": 6, "nodeType": "1483", "endLine": 29, "endColumn": 40, "suggestions": "1578"}, {"ruleId": "1457", "severity": 1, "message": "1508", "line": 336, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 336, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1509", "line": 337, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 337, "endColumn": 16}, {"ruleId": "1457", "severity": 1, "message": "1510", "line": 338, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 338, "endColumn": 19}, {"ruleId": "1457", "severity": 1, "message": "1511", "line": 339, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 339, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1512", "line": 340, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 340, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1513", "line": 341, "column": 11, "nodeType": "1459", "messageId": "1460", "endLine": 341, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1514", "line": 368, "column": 7, "nodeType": "1459", "messageId": "1460", "endLine": 368, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1515", "line": 386, "column": 7, "nodeType": "1459", "messageId": "1460", "endLine": 386, "endColumn": 21}, {"ruleId": "1457", "severity": 1, "message": "1516", "line": 395, "column": 7, "nodeType": "1459", "messageId": "1460", "endLine": 395, "endColumn": 16}, {"ruleId": "1457", "severity": 1, "message": "1517", "line": 407, "column": 5, "nodeType": "1459", "messageId": "1460", "endLine": 407, "endColumn": 18}, {"ruleId": "1457", "severity": 1, "message": "1508", "line": 702, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 702, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1509", "line": 703, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 703, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1510", "line": 704, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 704, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1511", "line": 705, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 705, "endColumn": 31}, {"ruleId": "1457", "severity": 1, "message": "1512", "line": 706, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 706, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1513", "line": 707, "column": 15, "nodeType": "1459", "messageId": "1460", "endLine": 707, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 4, "column": 43, "nodeType": "1463", "messageId": "1464", "endLine": 4, "endColumn": 50}, {"ruleId": "1461", "severity": 1, "message": "1477", "line": 9, "column": 18, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 30}, {"ruleId": "1461", "severity": 1, "message": "1496", "line": 9, "column": 32, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 42}, {"ruleId": "1461", "severity": 1, "message": "1497", "line": 9, "column": 44, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 52}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 9, "column": 47, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 54}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 6, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 6, "endColumn": 17}, {"ruleId": "1481", "severity": 1, "message": "1579", "line": 55, "column": 6, "nodeType": "1483", "endLine": 55, "endColumn": 8, "suggestions": "1580"}, {"ruleId": "1461", "severity": 1, "message": "1465", "line": 8, "column": 22, "nodeType": "1463", "messageId": "1464", "endLine": 8, "endColumn": 30}, {"ruleId": "1461", "severity": 1, "message": "1466", "line": 9, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 10, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 17}, {"ruleId": "1461", "severity": 1, "message": "1543", "line": 12, "column": 34, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 42}, {"ruleId": "1461", "severity": 1, "message": "1477", "line": 12, "column": 44, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 56}, {"ruleId": "1461", "severity": 1, "message": "1478", "line": 12, "column": 58, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 73}, {"ruleId": "1461", "severity": 1, "message": "1533", "line": 12, "column": 75, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 88}, {"ruleId": "1461", "severity": 1, "message": "1581", "line": 35, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 35, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1582", "line": 136, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 136, "endColumn": 28}, {"ruleId": "1461", "severity": 1, "message": "1498", "line": 213, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 213, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1583", "line": 9, "column": 18, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 28}, {"ruleId": "1461", "severity": 1, "message": "1490", "line": 9, "column": 86, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 93}, {"ruleId": "1461", "severity": 1, "message": "1520", "line": 9, "column": 95, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 103}, {"ruleId": "1461", "severity": 1, "message": "1466", "line": 10, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1584", "line": 197, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 197, "endColumn": 25}, {"ruleId": "1461", "severity": 1, "message": "1489", "line": 2, "column": 23, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 27}, {"ruleId": "1461", "severity": 1, "message": "1585", "line": 345, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 345, "endColumn": 30}, {"ruleId": "1461", "severity": 1, "message": "1586", "line": 358, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 358, "endColumn": 29}, {"ruleId": "1461", "severity": 1, "message": "1587", "line": 367, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 367, "endColumn": 33}, {"ruleId": "1461", "severity": 1, "message": "1588", "line": 409, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 409, "endColumn": 32}, {"ruleId": "1461", "severity": 1, "message": "1549", "line": 25, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 25, "endColumn": 22}, {"ruleId": "1461", "severity": 1, "message": "1522", "line": 25, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 25, "endColumn": 32}, {"ruleId": "1461", "severity": 1, "message": "1523", "line": 28, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 28, "endColumn": 25}, {"ruleId": "1461", "severity": 1, "message": "1524", "line": 226, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 226, "endColumn": 26}, {"ruleId": "1461", "severity": 1, "message": "1526", "line": 10, "column": 19, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 25}, {"ruleId": "1481", "severity": 1, "message": "1527", "line": 53, "column": 6, "nodeType": "1483", "endLine": 53, "endColumn": 8, "suggestions": "1589"}, {"ruleId": "1481", "severity": 1, "message": "1527", "line": 78, "column": 6, "nodeType": "1483", "endLine": 78, "endColumn": 32, "suggestions": "1590"}, {"ruleId": "1461", "severity": 1, "message": "1485", "line": 592, "column": 11, "nodeType": "1463", "messageId": "1464", "endLine": 592, "endColumn": 21}, {"ruleId": "1461", "severity": 1, "message": "1552", "line": 4, "column": 23, "nodeType": "1463", "messageId": "1464", "endLine": 4, "endColumn": 32}, {"ruleId": "1461", "severity": 1, "message": "1494", "line": 4, "column": 43, "nodeType": "1463", "messageId": "1464", "endLine": 4, "endColumn": 50}, {"ruleId": "1461", "severity": 1, "message": "1553", "line": 14, "column": 11, "nodeType": "1463", "messageId": "1464", "endLine": 14, "endColumn": 15}, {"ruleId": "1461", "severity": 1, "message": "1554", "line": 30, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 30, "endColumn": 23}, {"ruleId": "1461", "severity": 1, "message": "1591", "line": 9, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 27}, {"ruleId": "1461", "severity": 1, "message": "1561", "line": 10, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 12}, {"ruleId": "1461", "severity": 1, "message": "1541", "line": 11, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 11, "endColumn": 10}, {"ruleId": "1461", "severity": 1, "message": "1531", "line": 12, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 17}, {"ruleId": "1461", "severity": 1, "message": "1491", "line": 16, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 16, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1536", "line": 17, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 17, "endColumn": 15}, {"ruleId": "1461", "severity": 1, "message": "1537", "line": 18, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 18, "endColumn": 9}, {"ruleId": "1461", "severity": 1, "message": "1542", "line": 19, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 19, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1543", "line": 20, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 20, "endColumn": 11}, {"ruleId": "1461", "severity": 1, "message": "1538", "line": 82, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 82, "endColumn": 14}, {"ruleId": "1592", "severity": 1, "message": "1593", "line": 875, "column": 42, "nodeType": "1594", "messageId": "1595", "endLine": 875, "endColumn": 100}, {"ruleId": "1481", "severity": 1, "message": "1505", "line": 126, "column": 6, "nodeType": "1483", "endLine": 126, "endColumn": 39, "suggestions": "1596"}, {"ruleId": "1481", "severity": 1, "message": "1545", "line": 74, "column": 6, "nodeType": "1483", "endLine": 74, "endColumn": 29, "suggestions": "1597"}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 87, "column": 6, "nodeType": "1483", "endLine": 87, "endColumn": 29, "suggestions": "1598"}, {"ruleId": "1461", "severity": 1, "message": "1541", "line": 14, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 14, "endColumn": 10}, {"ruleId": "1461", "severity": 1, "message": "1542", "line": 16, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 16, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1543", "line": 17, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 17, "endColumn": 11}, {"ruleId": "1461", "severity": 1, "message": "1544", "line": 18, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 18, "endColumn": 19}, {"ruleId": "1461", "severity": 1, "message": "1531", "line": 11, "column": 33, "nodeType": "1463", "messageId": "1464", "endLine": 11, "endColumn": 47}, {"ruleId": "1461", "severity": 1, "message": "1532", "line": 12, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1533", "line": 12, "column": 31, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 44}, {"ruleId": "1461", "severity": 1, "message": "1534", "line": 50, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 50, "endColumn": 20}, {"ruleId": "1461", "severity": 1, "message": "1478", "line": 5, "column": 24, "nodeType": "1463", "messageId": "1464", "endLine": 5, "endColumn": 39}, {"ruleId": "1461", "severity": 1, "message": "1498", "line": 107, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 107, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1547", "line": 5, "column": 37, "nodeType": "1463", "messageId": "1464", "endLine": 5, "endColumn": 43}, {"ruleId": "1461", "severity": 1, "message": "1599", "line": 11, "column": 96, "nodeType": "1463", "messageId": "1464", "endLine": 11, "endColumn": 102}, {"ruleId": "1461", "severity": 1, "message": "1541", "line": 11, "column": 117, "nodeType": "1463", "messageId": "1464", "endLine": 11, "endColumn": 124}, {"ruleId": "1461", "severity": 1, "message": "1600", "line": 11, "column": 126, "nodeType": "1463", "messageId": "1464", "endLine": 11, "endColumn": 141}, {"ruleId": "1461", "severity": 1, "message": "1561", "line": 10, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 12}, {"ruleId": "1461", "severity": 1, "message": "1562", "line": 12, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1525", "line": 14, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 14, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1543", "line": 15, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 15, "endColumn": 11}, {"ruleId": "1461", "severity": 1, "message": "1542", "line": 16, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 16, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1544", "line": 17, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 17, "endColumn": 19}, {"ruleId": "1461", "severity": 1, "message": "1563", "line": 18, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 18, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1564", "line": 19, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 19, "endColumn": 21}, {"ruleId": "1461", "severity": 1, "message": "1565", "line": 21, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 21, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1566", "line": 21, "column": 15, "nodeType": "1463", "messageId": "1464", "endLine": 21, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1567", "line": 21, "column": 20, "nodeType": "1463", "messageId": "1464", "endLine": 21, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1568", "line": 66, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 66, "endColumn": 18}, {"ruleId": "1461", "severity": 1, "message": "1569", "line": 171, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 171, "endColumn": 36}, {"ruleId": "1461", "severity": 1, "message": "1570", "line": 185, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 185, "endColumn": 36}, {"ruleId": "1461", "severity": 1, "message": "1571", "line": 211, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 211, "endColumn": 35}, {"ruleId": "1461", "severity": 1, "message": "1572", "line": 232, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 232, "endColumn": 24}, {"ruleId": "1461", "severity": 1, "message": "1573", "line": 268, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 268, "endColumn": 25}, {"ruleId": "1461", "severity": 1, "message": "1574", "line": 323, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 323, "endColumn": 29}, {"ruleId": "1461", "severity": 1, "message": "1559", "line": 26, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 26, "endColumn": 18}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 416, "column": 6, "nodeType": "1483", "endLine": 416, "endColumn": 29, "suggestions": "1601"}, {"ruleId": "1461", "severity": 1, "message": "1562", "line": 10, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 10, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1602", "line": 11, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 11, "endColumn": 16}, {"ruleId": "1461", "severity": 1, "message": "1561", "line": 12, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 12, "endColumn": 12}, {"ruleId": "1461", "severity": 1, "message": "1480", "line": 13, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 13, "endColumn": 13}, {"ruleId": "1461", "severity": 1, "message": "1603", "line": 21, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 21, "endColumn": 17}, {"ruleId": "1461", "severity": 1, "message": "1604", "line": 22, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 22, "endColumn": 14}, {"ruleId": "1461", "severity": 1, "message": "1605", "line": 77, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 77, "endColumn": 22}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 163, "column": 6, "nodeType": "1483", "endLine": 163, "endColumn": 29, "suggestions": "1606"}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 8, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 8, "endColumn": 10}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 5, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 5, "endColumn": 17}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 6, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 6, "endColumn": 17}, {"ruleId": "1461", "severity": 1, "message": "1607", "line": 69, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 69, "endColumn": 31}, {"ruleId": "1461", "severity": 1, "message": "1608", "line": 110, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 110, "endColumn": 17}, {"ruleId": "1481", "severity": 1, "message": "1609", "line": 41, "column": 6, "nodeType": "1483", "endLine": 41, "endColumn": 32, "suggestions": "1610"}, {"ruleId": "1461", "severity": 1, "message": "1550", "line": 292, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 292, "endColumn": 21}, {"ruleId": "1461", "severity": 1, "message": "1551", "line": 92, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 92, "endColumn": 31}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 9, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 10}, {"ruleId": "1461", "severity": 1, "message": "1554", "line": 31, "column": 10, "nodeType": "1463", "messageId": "1464", "endLine": 31, "endColumn": 23}, {"ruleId": "1481", "severity": 1, "message": "1556", "line": 98, "column": 6, "nodeType": "1483", "endLine": 98, "endColumn": 24, "suggestions": "1611"}, {"ruleId": "1461", "severity": 1, "message": "1558", "line": 219, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 219, "endColumn": 28}, {"ruleId": "1461", "severity": 1, "message": "1548", "line": 2, "column": 103, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 110}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 9, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 9, "endColumn": 10}, {"ruleId": "1461", "severity": 1, "message": "1548", "line": 2, "column": 106, "nodeType": "1463", "messageId": "1464", "endLine": 2, "endColumn": 113}, {"ruleId": "1461", "severity": 1, "message": "1476", "line": 6, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 6, "endColumn": 10}, {"ruleId": "1481", "severity": 1, "message": "1577", "line": 29, "column": 6, "nodeType": "1483", "endLine": 29, "endColumn": 40, "suggestions": "1612"}, {"ruleId": "1461", "severity": 1, "message": "1613", "line": 13, "column": 3, "nodeType": "1463", "messageId": "1464", "endLine": 13, "endColumn": 11}, {"ruleId": "1461", "severity": 1, "message": "1614", "line": 32, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 32, "endColumn": 17}, {"ruleId": "1481", "severity": 1, "message": "1615", "line": 33, "column": 6, "nodeType": "1483", "endLine": 33, "endColumn": 32, "suggestions": "1616"}, {"ruleId": "1461", "severity": 1, "message": "1614", "line": 41, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 41, "endColumn": 17}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 104, "column": 6, "nodeType": "1483", "endLine": 104, "endColumn": 19, "suggestions": "1617"}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 118, "column": 6, "nodeType": "1483", "endLine": 118, "endColumn": 19, "suggestions": "1618"}, {"ruleId": "1481", "severity": 1, "message": "1539", "line": 160, "column": 6, "nodeType": "1483", "endLine": 160, "endColumn": 8, "suggestions": "1619"}, {"ruleId": "1461", "severity": 1, "message": "1620", "line": 4, "column": 95, "nodeType": "1463", "messageId": "1464", "endLine": 4, "endColumn": 100}, "no-dupe-keys", "Duplicate key 'select'.", "ObjectExpression", "unexpected", "no-unused-vars", "'FaLinkedin' is defined but never used.", "Identifier", "unusedVar", "'FaGithub' is defined but never used.", "'RiAiGenerate' is defined but never used.", "'DentistDashboard' is defined but never used.", "'DentistCalendar' is defined but never used.", "'DentistPatients' is defined but never used.", "'DentistAnalytics' is defined but never used.", "'DentistPatientProfile' is defined but never used.", "'DentistGallery' is defined but never used.", "'DentistXRay' is defined but never used.", "'Dentist<PERSON><PERSON><PERSON><PERSON>hart' is defined but never used.", "'DentistAppointments' is defined but never used.", "'FaTooth' is defined but never used.", "'FaUniversity' is defined but never used.", "'FaClinicMedical' is defined but never used.", "'MdHealthAndSafety' is defined but never used.", "'FaChartPie' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'upperTeethNumbers'. Either include it or remove the dependency array.", "ArrayExpression", ["1621"], "'toothColor' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Link' is defined but never used.", "'FaPills' is defined but never used.", "'FaNewspaper' is defined but never used.", "'useContext' is defined but never used.", "'surfaces' is assigned a value but never used.", "'FaTimes' is defined but never used.", "'FaFileMedical' is defined but never used.", "'FaEnvelope' is defined but never used.", "'FaIdCard' is defined but never used.", "'item' is assigned a value but never used.", "react/jsx-no-undef", "'FaUserAlt' is not defined.", "JSXIdentifier", "undefined", "'FaUserNurse' is not defined.", "'FaUserMd' is not defined.", "React Hook useEffect has a missing dependency: 'categories'. Either include it or remove the dependency array.", ["1622"], ["1623"], "Duplicate key 'NationalID'.", "Duplicate key 'Age'.", "Duplicate key 'Gender'.", "Duplicate key '<PERSON><PERSON><PERSON><PERSON><PERSON>'.", "Duplicate key 'Occupation'.", "Duplicate key 'Address'.", "Duplicate key 'universityInfo'.", "Duplicate key 'universities'.", "Duplicate key 'clinics'.", "Duplicate key 'dentistInfo'.", "'useState' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'FaUpload' is defined but never used.", "'FaHourglassHalf' is defined but never used.", "'procedureTypeAnalytics' is assigned a value but never used.", "'showReviewModal' is assigned a value but never used.", "'handleReviewClick' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'FaUser' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReviewSteps'. Either include it or remove the dependency array.", ["1624"], ["1625"], ["1626"], "'FaUserGraduate' is defined but never used.", "'FaCalendarCheck' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'allReviews' is assigned a value but never used.", ["1627"], "'FaArrowRight' is defined but never used.", "'FaBell' is defined but never used.", "'news' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["1628"], "'FaUsers' is defined but never used.", "'FaUserNurse' is defined but never used.", "'FaUserMd' is defined but never used.", "'FaClipboardCheck' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchNews'. Either include it or remove the dependency array.", ["1629"], "'FaEdit' is defined but never used.", "'FaImage' is defined but never used.", "'savedSheetId' is assigned a value but never used.", "'renderSelect' is assigned a value but never used.", "'handleDeepNestedChange' is assigned a value but never used.", "'FaFileAlt' is defined but never used.", "'user' is assigned a value but never used.", "'canvasContext' is assigned a value but never used.", "'FaTimesCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSavedSignature'. Either include it or remove the dependency array.", ["1630"], "'toggleSignatureType' is assigned a value but never used.", "'students' is assigned a value but never used.", ["1631"], "'FaUserAlt' is defined but never used.", "'FaChartLine' is defined but never used.", "'FaExclamationTriangle' is defined but never used.", "'FaRegCalendarCheck' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Line' is defined but never used.", "'container' is assigned a value but never used.", "'appointmentsStatusChartData' is assigned a value but never used.", "'appointmentsByTypeChartData' is assigned a value but never used.", "'appointmentsByDayChartData' is assigned a value but never used.", "'pieChartOptions' is assigned a value but never used.", "'lineChartOptions' is assigned a value but never used.", "'downloadAppointments' is assigned a value but never used.", "'patients' is assigned a value but never used.", ["1632"], "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1633"], "React Hook useEffect has a missing dependency: 'handleGoogleResponse'. Either include it or remove the dependency array.", ["1634"], "'chronicDiseases' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "'FaUserPlus' is defined but never used.", "'handleAddPatient' is assigned a value but never used.", "'appointmentStatusData' is assigned a value but never used.", "'appointmentTypesData' is assigned a value but never used.", "'appointmentsPerMonthData' is assigned a value but never used.", "'treatmentSheetTrendData' is assigned a value but never used.", ["1635"], ["1636"], "'printElementAsPDF' is defined but never used.", "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", ["1637"], ["1638"], ["1639"], "'FaCogs' is defined but never used.", "'FaGraduationCap' is defined but never used.", ["1640"], "'FaCalendarAlt' is defined but never used.", "'FaMapMarkerAlt' is defined but never used.", "'FaBriefcase' is defined but never used.", "'appointments' is assigned a value but never used.", ["1641"], "'toggleServicesDropdown' is assigned a value but never used.", "'dropdown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLabRequests'. Either include it or remove the dependency array.", ["1642"], ["1643"], ["1644"], "'FaFilter' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchActivities'. Either include it or remove the dependency array.", ["1645"], ["1646"], ["1647"], ["1648"], "'FaEye' is defined but never used.", {"desc": "1649", "fix": "1650"}, {"desc": "1651", "fix": "1652"}, {"desc": "1651", "fix": "1653"}, {"desc": "1654", "fix": "1655"}, {"desc": "1656", "fix": "1657"}, {"desc": "1649", "fix": "1658"}, {"desc": "1651", "fix": "1659"}, {"desc": "1660", "fix": "1661"}, {"desc": "1662", "fix": "1663"}, {"desc": "1664", "fix": "1665"}, {"desc": "1660", "fix": "1666"}, {"desc": "1660", "fix": "1667"}, {"desc": "1668", "fix": "1669"}, {"desc": "1670", "fix": "1671"}, {"desc": "1654", "fix": "1672"}, {"desc": "1656", "fix": "1673"}, {"desc": "1651", "fix": "1674"}, {"desc": "1662", "fix": "1675"}, {"desc": "1660", "fix": "1676"}, {"desc": "1660", "fix": "1677"}, {"desc": "1660", "fix": "1678"}, {"desc": "1679", "fix": "1680"}, {"desc": "1664", "fix": "1681"}, {"desc": "1668", "fix": "1682"}, {"desc": "1683", "fix": "1684"}, {"desc": "1685", "fix": "1686"}, {"desc": "1685", "fix": "1687"}, {"desc": "1688", "fix": "1689"}, "Update the dependencies array to be: [nationalId, upperTeethNumbers]", {"range": "1690", "text": "1691"}, "Update the dependencies array to be: [user, token, navigate, category, categories]", {"range": "1692", "text": "1693"}, {"range": "1694", "text": "1693"}, "Update the dependencies array to be: [fetchReviewSteps]", {"range": "1695", "text": "1696"}, "Update the dependencies array to be: [fetchReviewSteps, reviewData.procedureType]", {"range": "1697", "text": "1698"}, {"range": "1699", "text": "1691"}, {"range": "1700", "text": "1693"}, "Update the dependencies array to be: [user, token, navigate, fetchData]", {"range": "1701", "text": "1702"}, "Update the dependencies array to be: [user, token, navigate, fetchNews]", {"range": "1703", "text": "1704"}, "Update the dependencies array to be: [fetchSavedSignature, initialSignature]", {"range": "1705", "text": "1706"}, {"range": "1707", "text": "1702"}, {"range": "1708", "text": "1702"}, "Update the dependencies array to be: [isOpen, appointment, token, user, fetchStudents]", {"range": "1709", "text": "1710"}, "Update the dependencies array to be: [handleGoogleResponse]", {"range": "1711", "text": "1712"}, {"range": "1713", "text": "1696"}, {"range": "1714", "text": "1698"}, {"range": "1715", "text": "1693"}, {"range": "1716", "text": "1704"}, {"range": "1717", "text": "1702"}, {"range": "1718", "text": "1702"}, {"range": "1719", "text": "1702"}, "Update the dependencies array to be: [fetchLabRequests, nationalId, showLabPopup]", {"range": "1720", "text": "1721"}, {"range": "1722", "text": "1706"}, {"range": "1723", "text": "1710"}, "Update the dependencies array to be: [fetchActivities, filters, pagination.page]", {"range": "1724", "text": "1725"}, "Update the dependencies array to be: [user, token, fetchData]", {"range": "1726", "text": "1727"}, {"range": "1728", "text": "1727"}, "Update the dependencies array to be: [fetchData]", {"range": "1729", "text": "1730"}, [4827, 4839], "[nationalId, upperTeethNumbers]", [2682, 2715], "[user, token, navigate, category, categories]", [3328, 3361], [1741, 1743], "[fetchReviewSteps]", [2511, 2537], "[fetchReviewSteps, reviewData.procedureType]", [4827, 4839], [4697, 4730], [2932, 2955], "[user, token, navigate, fetchData]", [2276, 2299], "[user, token, navigate, fetchNews]", [3227, 3245], "[fetchSavedSignature, initialSignature]", [17029, 17052], [4536, 4559], [941, 975], "[isOpen, appointment, token, user, fetchStudents]", [1850, 1852], "[handleGoogleResponse]", [1741, 1743], [2511, 2537], [4719, 4752], [2333, 2356], [2954, 2977], [17179, 17202], [4623, 4646], [1746, 1772], "[fetchLabRequests, nationalId, showLabPopup]", [3227, 3245], [941, 975], [1008, 1034], "[fetchActivities, filters, pagination.page]", [2981, 2994], "[user, token, fetchData]", [3361, 3374], [4692, 4694], "[fetchData]"]