{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\assistant\\\\Appointments.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AssistantSidebar from './AssistantSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaCalendarAlt, FaSync, FaDownload, FaChartLine, FaUserMd, FaInfoCircle } from 'react-icons/fa';\nimport AppointmentDetailsModal from './AppointmentDetailsModal';\nimport AssignStudentModal from './AssignStudentModal';\nimport { saveAs } from 'file-saver';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst websiteColorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\nconst Appointments = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [appointments, setAppointments] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [dayFilter, setDayFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [refreshing, setRefreshing] = useState(false);\n  const [selectedAppointment, setSelectedAppointment] = useState(null);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showAssignModal, setShowAssignModal] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n\n  // Animation variants\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  const fetchData = async () => {\n    if (!user || !token) {\n      setError('Please log in to view appointments.');\n      setLoading(false);\n      return;\n    }\n    try {\n      var _user$affiliation;\n      setRefreshing(true);\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      const universityId = user.university || ((_user$affiliation = user.affiliation) === null || _user$affiliation === void 0 ? void 0 : _user$affiliation.id);\n\n      // Fetch appointments based on university - this is the working endpoint from Dashboard.jsx\n      // This endpoint is specifically allowed for assistant role\n      const appointmentsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/schedule?university=${encodeURIComponent(universityId)}`, config);\n\n      // Log the first appointment to see its structure\n      if (appointmentsRes.data && appointmentsRes.data.length > 0) {\n        console.log('First appointment data:', appointmentsRes.data[0]);\n\n        // Check if nationalId exists in the appointments\n        const hasNationalId = appointmentsRes.data.some(appt => appt.nationalId || appt.patient && appt.patient.nationalId);\n        console.log('Appointments have nationalId:', hasNationalId);\n\n        // Log all appointments with their nationalId\n        appointmentsRes.data.forEach((appt, index) => {\n          var _appt$patient;\n          const nationalId = appt.nationalId || ((_appt$patient = appt.patient) === null || _appt$patient === void 0 ? void 0 : _appt$patient.nationalId);\n          console.log(`Appointment ${index + 1} - nationalId:`, nationalId || 'N/A');\n        });\n      }\n      setAppointments(appointmentsRes.data || []);\n\n      // We don't need to fetch students separately since we can display appointments without student names\n      // This avoids the access denied error from the admin endpoints\n      setStudents([]);\n      setError('');\n      if (appointmentsRes.data.length === 0) {\n        setError('No appointments found for your university.');\n      }\n      console.log(`Fetched ${appointmentsRes.data.length} appointments for university: ${universityId}`);\n    } catch (err) {\n      var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data, _err$response6;\n      console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 404 ? 'Appointments endpoint not found.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403 ? 'Access denied. You do not have permission to view this data.' : ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.message) || 'Failed to load appointments';\n      setError(errorMessage);\n      if (((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : _err$response6.status) === 401) navigate('/login');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // Refresh appointments data\n  const handleRefresh = () => {\n    fetchData();\n  };\n\n  // Handle opening the appointment details modal\n  const handleAppointmentClick = appointment => {\n    setSelectedAppointment(appointment);\n    setShowDetailsModal(true);\n  };\n\n  // Handle opening the assign student modal\n  const handleAssignClick = (e, appointment) => {\n    e.stopPropagation(); // Prevent triggering the row click\n    setSelectedAppointment(appointment);\n    setShowAssignModal(true);\n  };\n\n  // Handle assigning a student to an appointment\n  const handleAssignStudent = async (appointmentId, student) => {\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n\n      // Create the update data\n      const updateData = {\n        doctor: student.studentId,\n        doctorModel: 'Student'\n      };\n\n      // Make the API call to update the appointment\n      const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/appointments/${appointmentId}/assign`, updateData, config);\n\n      // Get the updated appointment data from the response\n      const updatedAppointment = response.data;\n\n      // If we get here, the assignment was successful\n\n      // Find the appointment that was assigned\n      const appointmentToAssign = appointments.find(appt => appt._id === appointmentId);\n      if (appointmentToAssign) {\n        // Create a patient record for the student using appointment information\n        try {\n          var _appointmentToAssign$, _appointmentToAssign$2, _appointmentToAssign$3;\n          // Extract the national ID from the appointment\n          let nationalId = '';\n          if (appointmentToAssign.nationalId && appointmentToAssign.nationalId.trim() !== '') {\n            nationalId = appointmentToAssign.nationalId.trim();\n            console.log('Found nationalId in appointment object:', nationalId);\n          } else if (appointmentToAssign.patient && appointmentToAssign.patient.nationalId && appointmentToAssign.patient.nationalId.trim() !== '') {\n            nationalId = appointmentToAssign.patient.nationalId.trim();\n            console.log('Found nationalId in patient object:', nationalId);\n          } else {\n            console.log('No valid nationalId found in appointment:', appointmentToAssign);\n            // Only generate a temporary ID if we truly have no national ID\n            // AND if the appointment doesn't already have a real national ID\n            if (!appointmentToAssign.nationalId || appointmentToAssign.nationalId.startsWith('TEMP-')) {\n              nationalId = `TEMP-${Date.now()}`;\n              console.log('Generated temporary nationalId:', nationalId);\n\n              // Update the appointment with the temporary nationalId\n              try {\n                // Make an API call to update the appointment with the nationalId\n                await axios.put(`http://localhost:5000/api/appointments/${appointmentToAssign._id}`, {\n                  nationalId\n                }, {\n                  headers: {\n                    Authorization: `Bearer ${token}`\n                  }\n                });\n                console.log('Updated appointment with nationalId:', nationalId);\n              } catch (err) {\n                console.error('Error updating appointment with nationalId:', err);\n              }\n            } else {\n              // Use the existing national ID from the appointment\n              nationalId = appointmentToAssign.nationalId;\n              console.log('Using existing national ID from appointment:', nationalId);\n            }\n          }\n\n          // Log the appointment data for debugging\n          console.log('Appointment data:', {\n            _id: appointmentToAssign._id,\n            patient: appointmentToAssign.patient,\n            nationalId: nationalId,\n            fullName: ((_appointmentToAssign$ = appointmentToAssign.patient) === null || _appointmentToAssign$ === void 0 ? void 0 : _appointmentToAssign$.fullName) || appointmentToAssign.fullName,\n            appointmentNationalId: appointmentToAssign.nationalId,\n            patientNationalId: (_appointmentToAssign$2 = appointmentToAssign.patient) === null || _appointmentToAssign$2 === void 0 ? void 0 : _appointmentToAssign$2.nationalId\n          });\n\n          // Extract the patient name\n          const patientName = ((_appointmentToAssign$3 = appointmentToAssign.patient) === null || _appointmentToAssign$3 === void 0 ? void 0 : _appointmentToAssign$3.fullName) || appointmentToAssign.fullName || 'Unknown Patient';\n\n          // First, check if the student already has this patient in their list\n          let patientExists = false;\n          let existingPatientId = null;\n          if (nationalId) {\n            try {\n              // Get all patients for this student\n              const studentsPatients = await axios.get(`http://localhost:5000/api/patients/student/${student.studentId}`, config);\n\n              // Check if any patient has the same national ID\n              const existingPatient = studentsPatients.data.find(patient => patient.nationalId === nationalId);\n              if (existingPatient) {\n                patientExists = true;\n                existingPatientId = existingPatient._id;\n                console.log(`Found existing patient ${existingPatient.fullName} (${nationalId}) for student ${student.studentId}`);\n\n                // Set flag for success message\n                window.patientRecordCreated = false;\n              }\n            } catch (checkErr) {\n              console.log('Error checking student patients:', checkErr.message);\n              // Continue to create a new patient if check fails\n            }\n          }\n          if (!patientExists) {\n            var _appointmentToAssign$4, _appointmentToAssign$5, _appointmentToAssign$6, _appointmentToAssign$7, _appointmentToAssign$8;\n            // Check if we have a valid national ID - only generate temp if we truly don't have one\n            if (!nationalId || nationalId.trim() === '' || nationalId.startsWith('TEMP-')) {\n              console.warn('No valid national ID found for patient. Using a placeholder value.');\n              nationalId = `TEMP-${Date.now()}`; // Generate a temporary ID if none exists\n            } else {\n              console.log('Using existing national ID:', nationalId);\n            }\n\n            // Extract patient information from the appointment\n            const patientData = {\n              fullName: patientName,\n              nationalId: nationalId,\n              phoneNumber: ((_appointmentToAssign$4 = appointmentToAssign.patient) === null || _appointmentToAssign$4 === void 0 ? void 0 : _appointmentToAssign$4.phoneNumber) || appointmentToAssign.phoneNumber || '************',\n              gender: ((_appointmentToAssign$5 = appointmentToAssign.patient) === null || _appointmentToAssign$5 === void 0 ? void 0 : _appointmentToAssign$5.gender) || appointmentToAssign.gender || 'other',\n              age: ((_appointmentToAssign$6 = appointmentToAssign.patient) === null || _appointmentToAssign$6 === void 0 ? void 0 : _appointmentToAssign$6.age) || appointmentToAssign.age || 0,\n              address: ((_appointmentToAssign$7 = appointmentToAssign.patient) === null || _appointmentToAssign$7 === void 0 ? void 0 : _appointmentToAssign$7.address) || appointmentToAssign.address || '',\n              occupation: ((_appointmentToAssign$8 = appointmentToAssign.patient) === null || _appointmentToAssign$8 === void 0 ? void 0 : _appointmentToAssign$8.occupation) || appointmentToAssign.occupation || '',\n              drId: student.studentId,\n              // This is required by the backend - use studentId, not _id\n              medicalInfo: {\n                chiefComplaint: appointmentToAssign.chiefComplaint || '',\n                chronicDiseases: [],\n                recentSurgicalProcedures: '',\n                currentMedications: ''\n              }\n            };\n            console.log('Creating patient with drId (studentId):', student.studentId);\n            console.log('Creating patient with the following data:', {\n              fullName: patientData.fullName,\n              nationalId: patientData.nationalId,\n              phoneNumber: patientData.phoneNumber,\n              gender: patientData.gender,\n              age: patientData.age,\n              drId: patientData.drId\n            });\n\n            // Make API call to create a patient record\n            try {\n              console.log('Creating patient with data:', patientData);\n              const createResponse = await axios.post(`${process.env.REACT_APP_API_URL}/api/patients`, patientData, config);\n              console.log('Patient creation response:', createResponse.data);\n              if (createResponse.data.patient) {\n                console.log(`Created patient record for ${patientData.fullName} assigned to student ${student.studentId}`);\n                window.patientRecordCreated = true;\n\n                // Store the new patient ID for appointment association\n                existingPatientId = createResponse.data.patient._id;\n              } else {\n                console.log('Patient created but no patient data returned');\n                window.patientRecordCreated = true;\n              }\n            } catch (createErr) {\n              var _createErr$response, _createErr$response2, _createErr$response2$, _createErr$response2$2;\n              console.error('Error creating patient:', ((_createErr$response = createErr.response) === null || _createErr$response === void 0 ? void 0 : _createErr$response.data) || createErr.message);\n\n              // Check if this is a duplicate key error (patient already exists)\n              if ((_createErr$response2 = createErr.response) !== null && _createErr$response2 !== void 0 && (_createErr$response2$ = _createErr$response2.data) !== null && _createErr$response2$ !== void 0 && (_createErr$response2$2 = _createErr$response2$.message) !== null && _createErr$response2$2 !== void 0 && _createErr$response2$2.includes('already exists')) {\n                console.log(`Patient with nationalId ${nationalId} already exists in the database`);\n                window.patientRecordCreated = false;\n              } else {\n                // Set error flag but don't re-throw\n                window.patientRecordError = true;\n                window.patientRecordCreated = false;\n              }\n            }\n          } else {\n            // Set flag for success message\n            window.patientRecordCreated = false;\n            console.log(`Patient record already exists for ${patientName} with student ${student.studentId}`);\n          }\n\n          // If we have a patient ID and the appointment has a patient field, update the appointment\n          if (existingPatientId && appointmentToAssign.patient) {\n            try {\n              // Update the appointment to link it to this patient\n              await axios.put(`http://localhost:5000/api/appointments/${appointmentToAssign._id}/patient`, {\n                patientId: existingPatientId\n              }, config);\n              console.log(`Updated appointment ${appointmentToAssign._id} to link to patient ${existingPatientId}`);\n            } catch (updateErr) {\n              console.error('Error updating appointment with patient:', updateErr.message);\n            }\n          }\n        } catch (patientErr) {\n          var _patientErr$response;\n          console.error('Error creating patient record:', ((_patientErr$response = patientErr.response) === null || _patientErr$response === void 0 ? void 0 : _patientErr$response.data) || patientErr.message);\n          // Set flag to indicate error\n          window.patientRecordCreated = false;\n          window.patientRecordError = true;\n          // We don't throw here to avoid interrupting the appointment assignment process\n          // Just log the error and continue\n        }\n      }\n\n      // Update the appointments list with the updated appointment\n      const updatedAppointments = appointments.map(appt => appt._id === appointmentId ? {\n        ...appt,\n        doctor: student.studentId,\n        doctorModel: 'Student',\n        studentName: student.name,\n        studentId: student.studentId\n      } : appt);\n      setAppointments(updatedAppointments);\n\n      // If this is the currently selected appointment, update it too\n      if (selectedAppointment && selectedAppointment._id === appointmentId) {\n        setSelectedAppointment({\n          ...selectedAppointment,\n          doctor: student.studentId,\n          doctorModel: 'Student',\n          studentName: student.name,\n          studentId: student.studentId\n        });\n      }\n\n      // Show success message with information about patient record\n      let successMsg = `Successfully assigned appointment to ${student.name}`;\n\n      // Add information about the patient record if we have it\n      if (appointmentToAssign) {\n        var _appointmentToAssign$9;\n        const patientName = ((_appointmentToAssign$9 = appointmentToAssign.patient) === null || _appointmentToAssign$9 === void 0 ? void 0 : _appointmentToAssign$9.fullName) || appointmentToAssign.fullName || 'Unknown Patient';\n\n        // We'll set these variables in the try/catch block for patient creation\n        if (typeof window.patientRecordCreated !== 'undefined') {\n          if (window.patientRecordError) {\n            successMsg += ` (appointment assigned, but there was an error creating patient record for ${patientName})`;\n          } else if (window.patientRecordCreated === true) {\n            successMsg += ` and created new patient record for ${patientName}`;\n          } else {\n            successMsg += ` (existing patient record found for ${patientName})`;\n          }\n          // Reset the flags\n          delete window.patientRecordCreated;\n          delete window.patientRecordError;\n        }\n      }\n      setSuccessMessage(successMsg);\n      setTimeout(() => setSuccessMessage(''), 5000);\n\n      // Refresh the appointments data to ensure consistency\n      setTimeout(() => {\n        fetchData();\n      }, 1000);\n      return updatedAppointment;\n    } catch (err) {\n      var _err$response7, _err$response8, _err$response8$data;\n      console.error('Error assigning student:', ((_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : _err$response7.data) || err.message);\n      throw new Error(((_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.message) || 'Failed to assign student');\n    }\n  };\n  useEffect(() => {\n    fetchData();\n  }, [user, token, navigate]);\n  const downloadAppointments = () => {\n    if (filteredAppointments.length === 0) {\n      setError('No appointments to download.');\n      return;\n    }\n    try {\n      const headers = ['Date', 'Time', 'Patient', 'Procedure', 'Status', 'Student', 'University'];\n      const data = filteredAppointments.map(appt => {\n        var _appt$patient2, _user$affiliation2;\n        return [new Date(appt.date).toLocaleDateString('en-US'), appt.time || 'N/A', ((_appt$patient2 = appt.patient) === null || _appt$patient2 === void 0 ? void 0 : _appt$patient2.fullName) || appt.fullName || 'Unknown', appt.treatment || appt.type || 'N/A', appt.status, appt.studentName || appt.doctor || 'N/A', appt.university || user.university || ((_user$affiliation2 = user.affiliation) === null || _user$affiliation2 === void 0 ? void 0 : _user$affiliation2.id) || 'N/A'];\n      });\n\n      // Create CSV content\n      const csvContent = [headers.join(','), ...data.map(row => row.map(cell => `\"${cell}\"`).join(','))].join('\\n');\n\n      // Create blob and download\n      const blob = new Blob([csvContent], {\n        type: 'text/csv;charset=utf-8'\n      });\n      saveAs(blob, `appointments-${new Date().toISOString().slice(0, 10)}.csv`);\n\n      // Show success message\n      const successMessage = document.createElement('div');\n      successMessage.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg';\n      successMessage.textContent = 'Appointments exported successfully!';\n      document.body.appendChild(successMessage);\n\n      // Remove success message after 3 seconds\n      setTimeout(() => {\n        document.body.removeChild(successMessage);\n      }, 3000);\n    } catch (err) {\n      console.error('Error downloading appointments:', err);\n      setError('Failed to download appointments. Please try again.');\n    }\n  };\n  const filterAppointments = () => {\n    let filtered = appointments;\n\n    // Filter by date\n    if (dayFilter === 'today') {\n      filtered = filtered.filter(a => {\n        const apptDate = new Date(a.date);\n        const todayDate = new Date();\n        return apptDate.toDateString() === todayDate.toDateString();\n      });\n    } else if (dayFilter === 'tomorrow') {\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      filtered = filtered.filter(a => {\n        const apptDate = new Date(a.date);\n        return apptDate.toDateString() === tomorrow.toDateString();\n      });\n    } else if (dayFilter === 'week') {\n      const weekEnd = new Date();\n      weekEnd.setDate(weekEnd.getDate() + 7);\n      filtered = filtered.filter(a => {\n        const apptDate = new Date(a.date);\n        return apptDate >= new Date() && apptDate <= weekEnd;\n      });\n    }\n\n    // Filter by status\n    if (statusFilter) {\n      filtered = filtered.filter(a => a.status === statusFilter);\n    }\n\n    // Sort by date and time\n    return filtered.sort((a, b) => {\n      const dateA = new Date(a.date);\n      const dateB = new Date(b.date);\n      if (dateA.getTime() !== dateB.getTime()) return dateA - dateB;\n      return (a.time || '').localeCompare(b.time || '');\n    });\n  };\n  const filteredAppointments = filterAppointments();\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 504,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AssistantSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6\",\n        style: {\n          background: `linear-gradient(to bottom right, ${websiteColorPalette.primary}10, ${websiteColorPalette.background})`\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-red-500 mr-3\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold mb-1\",\n                  style: {\n                    color: websiteColorPalette.primary\n                  },\n                  children: \"Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Manage appointments in your university\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex mt-4 md:mt-0 gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleRefresh,\n                  disabled: refreshing,\n                  className: `px-4 py-2 text-white rounded-lg transition-colors flex items-center justify-center hover:brightness-110 ${refreshing ? 'opacity-70 cursor-not-allowed' : ''}`,\n                  style: {\n                    backgroundColor: websiteColorPalette.primary\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaSync, {\n                    className: `h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: refreshing ? 'Refreshing...' : 'Refresh'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: downloadAppointments,\n                  className: \"px-4 py-2 text-white rounded-lg transition-colors flex items-center justify-center hover:brightness-110\",\n                  style: {\n                    backgroundColor: websiteColorPalette.accent\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Export CSV\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold flex items-center\",\n                    style: {\n                      color: websiteColorPalette.primary\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                      className: \"h-5 w-5 mr-2\",\n                      style: {\n                        color: websiteColorPalette.primary\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 23\n                    }, this), \"Appointment List\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaChartLine, {\n                      className: \"h-5 w-5 mr-2\",\n                      style: {\n                        color: websiteColorPalette.primary\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      style: {\n                        color: websiteColorPalette.primary\n                      },\n                      children: [\"Total: \", appointments.length]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col sm:flex-row gap-4 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: dayFilter,\n                    onChange: e => setDayFilter(e.target.value),\n                    className: \"px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm\",\n                    style: {\n                      outline: 'none',\n                      boxShadow: 'none',\n                      borderColor: '#e5e7eb',\n                      ':focus': {\n                        borderColor: websiteColorPalette.primary\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Dates\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"today\",\n                      children: \"Today\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"tomorrow\",\n                      children: \"Tomorrow\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"week\",\n                      children: \"This Week\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 598,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: statusFilter,\n                    onChange: e => setStatusFilter(e.target.value),\n                    className: \"px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm\",\n                    style: {\n                      outline: 'none',\n                      boxShadow: 'none',\n                      borderColor: '#e5e7eb',\n                      ':focus': {\n                        borderColor: websiteColorPalette.primary\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Statuses\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"pending\",\n                      children: \"Pending\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"completed\",\n                      children: \"Completed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"cancelled\",\n                      children: \"Cancelled\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      className: \"bg-gray-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Date\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 622,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Time\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 623,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Patient\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 624,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Procedure\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 625,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 626,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Student\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 627,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Actions\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 628,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 621,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: filteredAppointments.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: \"7\",\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"rounded-full p-4 mb-4\",\n                              style: {\n                                backgroundColor: `${websiteColorPalette.primary}15`\n                              },\n                              children: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                                className: \"h-10 w-10\",\n                                style: {\n                                  color: `${websiteColorPalette.primary}80`\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 637,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 636,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: \"No appointments found\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 639,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: \"No appointments scheduled for this period in your university.\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 640,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm mt-2\",\n                              style: {\n                                color: websiteColorPalette.primary\n                              },\n                              children: \"Try changing the filters or check back later.\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 641,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 635,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 634,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 633,\n                        columnNumber: 27\n                      }, this) : filteredAppointments.map(appt => {\n                        var _appt$patient3, _appt$patient4;\n                        return /*#__PURE__*/_jsxDEV(motion.tr, {\n                          variants: item,\n                          className: \"hover:bg-gray-50 cursor-pointer\",\n                          onClick: () => handleAppointmentClick(appt),\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: new Date(appt.date).toLocaleDateString('en-US', {\n                              weekday: 'short',\n                              month: 'short',\n                              day: 'numeric'\n                            })\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 653,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: appt.time || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 660,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: [((_appt$patient3 = appt.patient) === null || _appt$patient3 === void 0 ? void 0 : _appt$patient3.fullName) || appt.fullName || 'Unknown', /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs text-gray-400\",\n                              children: [\"ID: \", appt.nationalId || ((_appt$patient4 = appt.patient) === null || _appt$patient4 === void 0 ? void 0 : _appt$patient4.nationalId) || 'N/A']\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 663,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 661,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: [appt.treatment || appt.type || 'N/A', appt.chiefComplaint && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs text-gray-400 mt-1\",\n                              children: [\"Chief complaint: \", appt.chiefComplaint]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 669,\n                              columnNumber: 57\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 667,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: `px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${appt.status === 'completed' ? 'bg-green-100 text-green-800' : appt.status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`,\n                              children: appt.status\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 672,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 671,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: [appt.studentName || appt.doctor || 'N/A', appt.studentId && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs text-gray-400\",\n                              children: [\"ID: \", appt.studentId]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 686,\n                              columnNumber: 52\n                            }, this), appt.doctorModel && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs text-gray-400\",\n                              children: [\"Type: \", appt.doctorModel]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 687,\n                              columnNumber: 54\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 684,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex space-x-2\",\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                onClick: e => handleAssignClick(e, appt),\n                                className: \"transition-colors p-1 rounded-full hover:bg-blue-50\",\n                                style: {\n                                  color: websiteColorPalette.primary\n                                },\n                                title: \"Assign to Student\",\n                                children: /*#__PURE__*/_jsxDEV(FaUserMd, {\n                                  className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 699,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 691,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                onClick: e => {\n                                  e.stopPropagation();\n                                  handleAppointmentClick(appt);\n                                },\n                                className: \"transition-colors p-1 rounded-full hover:bg-gray-100\",\n                                style: {\n                                  color: '#4b5563'\n                                },\n                                title: \"View Details\",\n                                children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                                  className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 712,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 701,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 690,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 689,\n                            columnNumber: 31\n                          }, this)]\n                        }, appt._id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 647,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fade-in-out\",\n      style: {\n        backgroundColor: websiteColorPalette.accent\n      },\n      children: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AppointmentDetailsModal, {\n      isOpen: showDetailsModal,\n      onClose: () => setShowDetailsModal(false),\n      appointment: selectedAppointment\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 738,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AssignStudentModal, {\n      isOpen: showAssignModal,\n      onClose: () => setShowAssignModal(false),\n      appointment: selectedAppointment,\n      onAssign: handleAssignStudent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 507,\n    columnNumber: 5\n  }, this);\n};\n_s(Appointments, \"DvkuK7ihH56Hn09a1L760IiCYpA=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Appointments;\nexport default Appointments;\nvar _c;\n$RefreshReg$(_c, \"Appointments\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Assistant<PERSON><PERSON><PERSON>", "Loader", "motion", "FaCalendarAlt", "FaSync", "FaDownload", "FaChartLine", "FaUserMd", "FaInfoCircle", "AppointmentDetailsModal", "AssignStudentModal", "saveAs", "jsxDEV", "_jsxDEV", "websiteColorPalette", "primary", "secondary", "background", "text", "accent", "Appointments", "_s", "sidebarOpen", "setSidebarOpen", "appointments", "setAppointments", "students", "setStudents", "dayFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilter", "setStatus<PERSON>ilter", "loading", "setLoading", "error", "setError", "refreshing", "setRefreshing", "selectedAppointment", "setSelectedAppointment", "showDetailsModal", "setShowDetailsModal", "showAssignModal", "setShowAssignModal", "successMessage", "setSuccessMessage", "navigate", "user", "token", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "fetchData", "_user$affiliation", "config", "headers", "Authorization", "universityId", "university", "affiliation", "id", "appointmentsRes", "get", "process", "env", "REACT_APP_API_URL", "encodeURIComponent", "data", "length", "console", "log", "hasNationalId", "some", "appt", "nationalId", "patient", "for<PERSON>ach", "index", "_appt$patient", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "_err$response6", "response", "message", "errorMessage", "status", "handleRefresh", "handleAppointmentClick", "appointment", "handleAssignClick", "e", "stopPropagation", "handleAssignStudent", "appointmentId", "student", "updateData", "doctor", "studentId", "<PERSON><PERSON><PERSON><PERSON>", "put", "updatedAppointment", "appointmentToAssign", "find", "_id", "_appointmentToAssign$", "_appointmentToAssign$2", "_appointmentToAssign$3", "trim", "startsWith", "Date", "now", "fullName", "appointmentNationalId", "patientNationalId", "patientName", "patientExists", "existingPatientId", "studentsPatients", "existingPatient", "window", "patientRecordCreated", "checkErr", "_appointmentToAssign$4", "_appointmentToAssign$5", "_appointmentToAssign$6", "_appointmentToAssign$7", "_appointmentToAssign$8", "warn", "patientData", "phoneNumber", "gender", "age", "address", "occupation", "drId", "medicalInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chronicDiseases", "recentSurgicalProcedures", "currentMedications", "createResponse", "post", "createErr", "_createErr$response", "_createErr$response2", "_createErr$response2$", "_createErr$response2$2", "includes", "patientRecordError", "patientId", "updateErr", "patientErr", "_patientErr$response", "updatedAppointments", "map", "studentName", "name", "successMsg", "_appointmentToAssign$9", "setTimeout", "_err$response7", "_err$response8", "_err$response8$data", "Error", "downloadAppointments", "filteredAppointments", "_appt$patient2", "_user$affiliation2", "date", "toLocaleDateString", "time", "treatment", "type", "csv<PERSON><PERSON>nt", "join", "row", "cell", "blob", "Blob", "toISOString", "slice", "document", "createElement", "className", "textContent", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "filterAppointments", "filtered", "filter", "a", "apptDate", "todayDate", "toDateString", "tomorrow", "setDate", "getDate", "weekEnd", "sort", "b", "dateA", "dateB", "getTime", "localeCompare", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "isOpen", "setIsOpen", "toggleSidebar", "style", "div", "initial", "animate", "fill", "viewBox", "fillRule", "d", "clipRule", "duration", "color", "onClick", "disabled", "backgroundColor", "variants", "whileInView", "viewport", "once", "value", "onChange", "target", "outline", "boxShadow", "borderColor", "colSpan", "_appt$patient3", "_appt$patient4", "tr", "weekday", "month", "day", "title", "onClose", "onAssign", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/assistant/Appointments.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AssistantSidebar from './AssistantSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaCalendarAlt, FaSync, FaDownload, FaChartLine, FaUserMd, FaInfoCircle } from 'react-icons/fa';\nimport AppointmentDetailsModal from './AppointmentDetailsModal';\nimport AssignStudentModal from './AssignStudentModal';\nimport { saveAs } from 'file-saver';\n\n// Website color palette\nconst websiteColorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\nconst Appointments = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [appointments, setAppointments] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [dayFilter, setDayFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [refreshing, setRefreshing] = useState(false);\n  const [selectedAppointment, setSelectedAppointment] = useState(null);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showAssignModal, setShowAssignModal] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n\n  // Animation variants\n  const container = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    show: { opacity: 1, y: 0 }\n  };\n\n  const fetchData = async () => {\n    if (!user || !token) {\n      setError('Please log in to view appointments.');\n      setLoading(false);\n      return;\n    }\n\n    try {\n      setRefreshing(true);\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      const universityId = user.university || user.affiliation?.id;\n\n      // Fetch appointments based on university - this is the working endpoint from Dashboard.jsx\n      // This endpoint is specifically allowed for assistant role\n      const appointmentsRes = await axios.get(\n        `${process.env.REACT_APP_API_URL}/api/appointments/schedule?university=${encodeURIComponent(universityId)}`,\n        config\n      );\n\n      // Log the first appointment to see its structure\n      if (appointmentsRes.data && appointmentsRes.data.length > 0) {\n        console.log('First appointment data:', appointmentsRes.data[0]);\n\n        // Check if nationalId exists in the appointments\n        const hasNationalId = appointmentsRes.data.some(appt =>\n          appt.nationalId || (appt.patient && appt.patient.nationalId)\n        );\n\n        console.log('Appointments have nationalId:', hasNationalId);\n\n        // Log all appointments with their nationalId\n        appointmentsRes.data.forEach((appt, index) => {\n          const nationalId = appt.nationalId || appt.patient?.nationalId;\n          console.log(`Appointment ${index + 1} - nationalId:`, nationalId || 'N/A');\n        });\n      }\n\n      setAppointments(appointmentsRes.data || []);\n\n      // We don't need to fetch students separately since we can display appointments without student names\n      // This avoids the access denied error from the admin endpoints\n      setStudents([]);\n      setError('');\n\n      if (appointmentsRes.data.length === 0) {\n        setError('No appointments found for your university.');\n      }\n\n      console.log(`Fetched ${appointmentsRes.data.length} appointments for university: ${universityId}`);\n    } catch (err) {\n      console.error('Fetch error:', err.response?.data || err.message);\n      const errorMessage =\n        err.response?.status === 404\n          ? 'Appointments endpoint not found.'\n          : err.response?.status === 401\n          ? 'Unauthorized. Please log in again.'\n          : err.response?.status === 403\n          ? 'Access denied. You do not have permission to view this data.'\n          : err.response?.data?.message || 'Failed to load appointments';\n      setError(errorMessage);\n      if (err.response?.status === 401) navigate('/login');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // Refresh appointments data\n  const handleRefresh = () => {\n    fetchData();\n  };\n\n  // Handle opening the appointment details modal\n  const handleAppointmentClick = (appointment) => {\n    setSelectedAppointment(appointment);\n    setShowDetailsModal(true);\n  };\n\n  // Handle opening the assign student modal\n  const handleAssignClick = (e, appointment) => {\n    e.stopPropagation(); // Prevent triggering the row click\n    setSelectedAppointment(appointment);\n    setShowAssignModal(true);\n  };\n\n  // Handle assigning a student to an appointment\n  const handleAssignStudent = async (appointmentId, student) => {\n    try {\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n\n      // Create the update data\n      const updateData = {\n        doctor: student.studentId,\n        doctorModel: 'Student'\n      };\n\n      // Make the API call to update the appointment\n      const response = await axios.put(\n        `${process.env.REACT_APP_API_URL}/api/appointments/${appointmentId}/assign`,\n        updateData,\n        config\n      );\n\n      // Get the updated appointment data from the response\n      const updatedAppointment = response.data;\n\n      // If we get here, the assignment was successful\n\n      // Find the appointment that was assigned\n      const appointmentToAssign = appointments.find(appt => appt._id === appointmentId);\n\n      if (appointmentToAssign) {\n        // Create a patient record for the student using appointment information\n        try {\n          // Extract the national ID from the appointment\n          let nationalId = '';\n          if (appointmentToAssign.nationalId && appointmentToAssign.nationalId.trim() !== '') {\n            nationalId = appointmentToAssign.nationalId.trim();\n            console.log('Found nationalId in appointment object:', nationalId);\n          } else if (appointmentToAssign.patient && appointmentToAssign.patient.nationalId && appointmentToAssign.patient.nationalId.trim() !== '') {\n            nationalId = appointmentToAssign.patient.nationalId.trim();\n            console.log('Found nationalId in patient object:', nationalId);\n          } else {\n            console.log('No valid nationalId found in appointment:', appointmentToAssign);\n            // Only generate a temporary ID if we truly have no national ID\n            // AND if the appointment doesn't already have a real national ID\n            if (!appointmentToAssign.nationalId || appointmentToAssign.nationalId.startsWith('TEMP-')) {\n              nationalId = `TEMP-${Date.now()}`;\n              console.log('Generated temporary nationalId:', nationalId);\n\n              // Update the appointment with the temporary nationalId\n              try {\n                // Make an API call to update the appointment with the nationalId\n                await axios.put(\n                  `http://localhost:5000/api/appointments/${appointmentToAssign._id}`,\n                  { nationalId },\n                  { headers: { Authorization: `Bearer ${token}` } }\n                );\n                console.log('Updated appointment with nationalId:', nationalId);\n              } catch (err) {\n                console.error('Error updating appointment with nationalId:', err);\n              }\n            } else {\n              // Use the existing national ID from the appointment\n              nationalId = appointmentToAssign.nationalId;\n              console.log('Using existing national ID from appointment:', nationalId);\n            }\n          }\n\n          // Log the appointment data for debugging\n          console.log('Appointment data:', {\n            _id: appointmentToAssign._id,\n            patient: appointmentToAssign.patient,\n            nationalId: nationalId,\n            fullName: appointmentToAssign.patient?.fullName || appointmentToAssign.fullName,\n            appointmentNationalId: appointmentToAssign.nationalId,\n            patientNationalId: appointmentToAssign.patient?.nationalId\n          });\n\n          // Extract the patient name\n          const patientName = appointmentToAssign.patient?.fullName || appointmentToAssign.fullName || 'Unknown Patient';\n\n          // First, check if the student already has this patient in their list\n          let patientExists = false;\n          let existingPatientId = null;\n\n          if (nationalId) {\n            try {\n              // Get all patients for this student\n              const studentsPatients = await axios.get(\n                `http://localhost:5000/api/patients/student/${student.studentId}`,\n                config\n              );\n\n              // Check if any patient has the same national ID\n              const existingPatient = studentsPatients.data.find(\n                patient => patient.nationalId === nationalId\n              );\n\n              if (existingPatient) {\n                patientExists = true;\n                existingPatientId = existingPatient._id;\n                console.log(`Found existing patient ${existingPatient.fullName} (${nationalId}) for student ${student.studentId}`);\n\n                // Set flag for success message\n                window.patientRecordCreated = false;\n              }\n            } catch (checkErr) {\n              console.log('Error checking student patients:', checkErr.message);\n              // Continue to create a new patient if check fails\n            }\n          }\n\n          if (!patientExists) {\n            // Check if we have a valid national ID - only generate temp if we truly don't have one\n            if (!nationalId || nationalId.trim() === '' || nationalId.startsWith('TEMP-')) {\n              console.warn('No valid national ID found for patient. Using a placeholder value.');\n              nationalId = `TEMP-${Date.now()}`; // Generate a temporary ID if none exists\n            } else {\n              console.log('Using existing national ID:', nationalId);\n            }\n\n            // Extract patient information from the appointment\n            const patientData = {\n              fullName: patientName,\n              nationalId: nationalId,\n              phoneNumber: appointmentToAssign.patient?.phoneNumber || appointmentToAssign.phoneNumber || '************',\n              gender: appointmentToAssign.patient?.gender || appointmentToAssign.gender || 'other',\n              age: appointmentToAssign.patient?.age || appointmentToAssign.age || 0,\n              address: appointmentToAssign.patient?.address || appointmentToAssign.address || '',\n              occupation: appointmentToAssign.patient?.occupation || appointmentToAssign.occupation || '',\n              drId: student.studentId, // This is required by the backend - use studentId, not _id\n              medicalInfo: {\n                chiefComplaint: appointmentToAssign.chiefComplaint || '',\n                chronicDiseases: [],\n                recentSurgicalProcedures: '',\n                currentMedications: ''\n              }\n            };\n\n            console.log('Creating patient with drId (studentId):', student.studentId);\n\n            console.log('Creating patient with the following data:', {\n              fullName: patientData.fullName,\n              nationalId: patientData.nationalId,\n              phoneNumber: patientData.phoneNumber,\n              gender: patientData.gender,\n              age: patientData.age,\n              drId: patientData.drId\n            });\n\n            // Make API call to create a patient record\n            try {\n              console.log('Creating patient with data:', patientData);\n              const createResponse = await axios.post(\n                `${process.env.REACT_APP_API_URL}/api/patients`,\n                patientData,\n                config\n              );\n\n              console.log('Patient creation response:', createResponse.data);\n\n              if (createResponse.data.patient) {\n                console.log(`Created patient record for ${patientData.fullName} assigned to student ${student.studentId}`);\n                window.patientRecordCreated = true;\n\n                // Store the new patient ID for appointment association\n                existingPatientId = createResponse.data.patient._id;\n              } else {\n                console.log('Patient created but no patient data returned');\n                window.patientRecordCreated = true;\n              }\n            } catch (createErr) {\n              console.error('Error creating patient:', createErr.response?.data || createErr.message);\n\n              // Check if this is a duplicate key error (patient already exists)\n              if (createErr.response?.data?.message?.includes('already exists')) {\n                console.log(`Patient with nationalId ${nationalId} already exists in the database`);\n                window.patientRecordCreated = false;\n              } else {\n                // Set error flag but don't re-throw\n                window.patientRecordError = true;\n                window.patientRecordCreated = false;\n              }\n            }\n          } else {\n            // Set flag for success message\n            window.patientRecordCreated = false;\n            console.log(`Patient record already exists for ${patientName} with student ${student.studentId}`);\n          }\n\n          // If we have a patient ID and the appointment has a patient field, update the appointment\n          if (existingPatientId && appointmentToAssign.patient) {\n            try {\n              // Update the appointment to link it to this patient\n              await axios.put(\n                `http://localhost:5000/api/appointments/${appointmentToAssign._id}/patient`,\n                { patientId: existingPatientId },\n                config\n              );\n\n              console.log(`Updated appointment ${appointmentToAssign._id} to link to patient ${existingPatientId}`);\n            } catch (updateErr) {\n              console.error('Error updating appointment with patient:', updateErr.message);\n            }\n          }\n        } catch (patientErr) {\n          console.error('Error creating patient record:', patientErr.response?.data || patientErr.message);\n          // Set flag to indicate error\n          window.patientRecordCreated = false;\n          window.patientRecordError = true;\n          // We don't throw here to avoid interrupting the appointment assignment process\n          // Just log the error and continue\n        }\n      }\n\n      // Update the appointments list with the updated appointment\n      const updatedAppointments = appointments.map(appt =>\n        appt._id === appointmentId\n          ? {\n              ...appt,\n              doctor: student.studentId,\n              doctorModel: 'Student',\n              studentName: student.name,\n              studentId: student.studentId\n            }\n          : appt\n      );\n\n      setAppointments(updatedAppointments);\n\n      // If this is the currently selected appointment, update it too\n      if (selectedAppointment && selectedAppointment._id === appointmentId) {\n        setSelectedAppointment({\n          ...selectedAppointment,\n          doctor: student.studentId,\n          doctorModel: 'Student',\n          studentName: student.name,\n          studentId: student.studentId\n        });\n      }\n\n      // Show success message with information about patient record\n      let successMsg = `Successfully assigned appointment to ${student.name}`;\n\n      // Add information about the patient record if we have it\n      if (appointmentToAssign) {\n        const patientName = appointmentToAssign.patient?.fullName || appointmentToAssign.fullName || 'Unknown Patient';\n\n        // We'll set these variables in the try/catch block for patient creation\n        if (typeof window.patientRecordCreated !== 'undefined') {\n          if (window.patientRecordError) {\n            successMsg += ` (appointment assigned, but there was an error creating patient record for ${patientName})`;\n          } else if (window.patientRecordCreated === true) {\n            successMsg += ` and created new patient record for ${patientName}`;\n          } else {\n            successMsg += ` (existing patient record found for ${patientName})`;\n          }\n          // Reset the flags\n          delete window.patientRecordCreated;\n          delete window.patientRecordError;\n        }\n      }\n\n      setSuccessMessage(successMsg);\n      setTimeout(() => setSuccessMessage(''), 5000);\n\n      // Refresh the appointments data to ensure consistency\n      setTimeout(() => {\n        fetchData();\n      }, 1000);\n\n      return updatedAppointment;\n    } catch (err) {\n      console.error('Error assigning student:', err.response?.data || err.message);\n      throw new Error(err.response?.data?.message || 'Failed to assign student');\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, [user, token, navigate]);\n\n  const downloadAppointments = () => {\n    if (filteredAppointments.length === 0) {\n      setError('No appointments to download.');\n      return;\n    }\n\n    try {\n      const headers = ['Date', 'Time', 'Patient', 'Procedure', 'Status', 'Student', 'University'];\n      const data = filteredAppointments.map((appt) => [\n        new Date(appt.date).toLocaleDateString('en-US'),\n        appt.time || 'N/A',\n        appt.patient?.fullName || appt.fullName || 'Unknown',\n        appt.treatment || appt.type || 'N/A',\n        appt.status,\n        appt.studentName || appt.doctor || 'N/A',\n        appt.university || user.university || user.affiliation?.id || 'N/A',\n      ]);\n\n      // Create CSV content\n      const csvContent = [\n        headers.join(','),\n        ...data.map(row => row.map(cell => `\"${cell}\"`).join(','))\n      ].join('\\n');\n\n      // Create blob and download\n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });\n      saveAs(blob, `appointments-${new Date().toISOString().slice(0, 10)}.csv`);\n\n      // Show success message\n      const successMessage = document.createElement('div');\n      successMessage.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg';\n      successMessage.textContent = 'Appointments exported successfully!';\n      document.body.appendChild(successMessage);\n\n      // Remove success message after 3 seconds\n      setTimeout(() => {\n        document.body.removeChild(successMessage);\n      }, 3000);\n    } catch (err) {\n      console.error('Error downloading appointments:', err);\n      setError('Failed to download appointments. Please try again.');\n    }\n  };\n\n  const filterAppointments = () => {\n    let filtered = appointments;\n\n    // Filter by date\n    if (dayFilter === 'today') {\n      filtered = filtered.filter((a) => {\n        const apptDate = new Date(a.date);\n        const todayDate = new Date();\n        return apptDate.toDateString() === todayDate.toDateString();\n      });\n    } else if (dayFilter === 'tomorrow') {\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      filtered = filtered.filter((a) => {\n        const apptDate = new Date(a.date);\n        return apptDate.toDateString() === tomorrow.toDateString();\n      });\n    } else if (dayFilter === 'week') {\n      const weekEnd = new Date();\n      weekEnd.setDate(weekEnd.getDate() + 7);\n      filtered = filtered.filter((a) => {\n        const apptDate = new Date(a.date);\n        return apptDate >= new Date() && apptDate <= weekEnd;\n      });\n    }\n\n    // Filter by status\n    if (statusFilter) {\n      filtered = filtered.filter((a) => a.status === statusFilter);\n    }\n\n    // Sort by date and time\n    return filtered.sort((a, b) => {\n      const dateA = new Date(a.date);\n      const dateB = new Date(b.date);\n      if (dateA.getTime() !== dateB.getTime()) return dateA - dateB;\n      return (a.time || '').localeCompare(b.time || '');\n    });\n  };\n\n  const filteredAppointments = filterAppointments();\n\n  if (loading) return <Loader />;\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n        <main className=\"flex-1 overflow-y-auto p-6\" style={{ background: `linear-gradient(to bottom right, ${websiteColorPalette.primary}10, ${websiteColorPalette.background})` }}>\n          <div className=\"max-w-7xl mx-auto\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n              >\n                <div className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 text-red-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                  <p className=\"text-red-700\">{error}</p>\n                </div>\n              </motion.div>\n            )}\n\n            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\">\n                <div>\n                  <h1 className=\"text-3xl md:text-4xl font-bold mb-1\" style={{ color: websiteColorPalette.primary }}>Appointments</h1>\n                  <p className=\"text-gray-600\">Manage appointments in your university</p>\n                </div>\n                <div className=\"flex mt-4 md:mt-0 gap-3\">\n                  <button\n                    onClick={handleRefresh}\n                    disabled={refreshing}\n                    className={`px-4 py-2 text-white rounded-lg transition-colors flex items-center justify-center hover:brightness-110 ${\n                      refreshing ? 'opacity-70 cursor-not-allowed' : ''\n                    }`}\n                    style={{\n                      backgroundColor: websiteColorPalette.primary\n                    }}\n                  >\n                    <FaSync className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\n                    <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>\n                  </button>\n                  <button\n                    onClick={downloadAppointments}\n                    className=\"px-4 py-2 text-white rounded-lg transition-colors flex items-center justify-center hover:brightness-110\"\n                    style={{\n                      backgroundColor: websiteColorPalette.accent\n                    }}\n                  >\n                    <FaDownload className=\"h-4 w-4 mr-2\" />\n                    <span>Export CSV</span>\n                  </button>\n                </div>\n              </div>\n              <motion.div\n                variants={container}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true }}\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\">\n                    <h2 className=\"text-xl font-bold flex items-center\" style={{ color: websiteColorPalette.primary }}>\n                      <FaCalendarAlt className=\"h-5 w-5 mr-2\" style={{ color: websiteColorPalette.primary }} />\n                      Appointment List\n                    </h2>\n                    <div className=\"flex items-center\">\n                      <FaChartLine className=\"h-5 w-5 mr-2\" style={{ color: websiteColorPalette.primary }} />\n                      <span className=\"font-medium\" style={{ color: websiteColorPalette.primary }}>Total: {appointments.length}</span>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n                    <select\n                      value={dayFilter}\n                      onChange={(e) => setDayFilter(e.target.value)}\n                      className=\"px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm\"\n                      style={{\n                        outline: 'none',\n                        boxShadow: 'none',\n                        borderColor: '#e5e7eb',\n                        ':focus': { borderColor: websiteColorPalette.primary }\n                      }}\n                    >\n                      <option value=\"all\">All Dates</option>\n                      <option value=\"today\">Today</option>\n                      <option value=\"tomorrow\">Tomorrow</option>\n                      <option value=\"week\">This Week</option>\n                    </select>\n                    <select\n                      value={statusFilter}\n                      onChange={(e) => setStatusFilter(e.target.value)}\n                      className=\"px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm\"\n                      style={{\n                        outline: 'none',\n                        boxShadow: 'none',\n                        borderColor: '#e5e7eb',\n                        ':focus': { borderColor: websiteColorPalette.primary }\n                      }}\n                    >\n                      <option value=\"\">All Statuses</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"completed\">Completed</option>\n                      <option value=\"cancelled\">Cancelled</option>\n                    </select>\n                  </div>\n\n                  <div className=\"overflow-x-auto\">\n                    <table className=\"min-w-full divide-y divide-gray-200\">\n                      <thead className=\"bg-gray-50\">\n                        <tr>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Date</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Time</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Patient</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Procedure</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Student</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {filteredAppointments.length === 0 ? (\n                          <tr>\n                            <td colSpan=\"7\" className=\"px-6 py-8 text-center\">\n                              <div className=\"flex flex-col items-center justify-center\">\n                                <div className=\"rounded-full p-4 mb-4\" style={{ backgroundColor: `${websiteColorPalette.primary}15` }}>\n                                  <FaCalendarAlt className=\"h-10 w-10\" style={{ color: `${websiteColorPalette.primary}80` }} />\n                                </div>\n                                <h3 className=\"text-lg font-medium text-gray-900\">No appointments found</h3>\n                                <p className=\"mt-1 text-gray-500\">No appointments scheduled for this period in your university.</p>\n                                <p className=\"text-sm mt-2\" style={{ color: websiteColorPalette.primary }}>Try changing the filters or check back later.</p>\n                              </div>\n                            </td>\n                          </tr>\n                        ) : (\n                          filteredAppointments.map((appt) => (\n                            <motion.tr\n                              key={appt._id}\n                              variants={item}\n                              className=\"hover:bg-gray-50 cursor-pointer\"\n                              onClick={() => handleAppointmentClick(appt)}\n                            >\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                                {new Date(appt.date).toLocaleDateString('en-US', {\n                                  weekday: 'short',\n                                  month: 'short',\n                                  day: 'numeric',\n                                })}\n                              </td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{appt.time || 'N/A'}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                {appt.patient?.fullName || appt.fullName || 'Unknown'}\n                                <div className=\"text-xs text-gray-400\">\n                                  ID: {appt.nationalId || appt.patient?.nationalId || 'N/A'}\n                                </div>\n                              </td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                {appt.treatment || appt.type || 'N/A'}\n                                {appt.chiefComplaint && <div className=\"text-xs text-gray-400 mt-1\">Chief complaint: {appt.chiefComplaint}</div>}\n                              </td>\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\n                                <span\n                                  className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                                    appt.status === 'completed'\n                                      ? 'bg-green-100 text-green-800'\n                                      : appt.status === 'cancelled'\n                                      ? 'bg-red-100 text-red-800'\n                                      : 'bg-yellow-100 text-yellow-800'\n                                  }`}\n                                >\n                                  {appt.status}\n                                </span>\n                              </td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                {appt.studentName || appt.doctor || 'N/A'}\n                                {appt.studentId && <div className=\"text-xs text-gray-400\">ID: {appt.studentId}</div>}\n                                {appt.doctorModel && <div className=\"text-xs text-gray-400\">Type: {appt.doctorModel}</div>}\n                              </td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                <div className=\"flex space-x-2\">\n                                  <button\n                                    onClick={(e) => handleAssignClick(e, appt)}\n                                    className=\"transition-colors p-1 rounded-full hover:bg-blue-50\"\n                                    style={{\n                                      color: websiteColorPalette.primary\n                                    }}\n                                    title=\"Assign to Student\"\n                                  >\n                                    <FaUserMd className=\"h-5 w-5\" />\n                                  </button>\n                                  <button\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      handleAppointmentClick(appt);\n                                    }}\n                                    className=\"transition-colors p-1 rounded-full hover:bg-gray-100\"\n                                    style={{\n                                      color: '#4b5563'\n                                    }}\n                                    title=\"View Details\"\n                                  >\n                                    <FaInfoCircle className=\"h-5 w-5\" />\n                                  </button>\n                                </div>\n                              </td>\n                            </motion.tr>\n                          ))\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </motion.div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n\n      {/* Success Message */}\n      {successMessage && (\n        <div className=\"fixed bottom-4 right-4 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fade-in-out\"\n          style={{ backgroundColor: websiteColorPalette.accent }}>\n          {successMessage}\n        </div>\n      )}\n\n      {/* Appointment Details Modal */}\n      <AppointmentDetailsModal\n        isOpen={showDetailsModal}\n        onClose={() => setShowDetailsModal(false)}\n        appointment={selectedAppointment}\n      />\n\n      {/* Assign Student Modal */}\n      <AssignStudentModal\n        isOpen={showAssignModal}\n        onClose={() => setShowAssignModal(false)}\n        appointment={selectedAppointment}\n        onAssign={handleAssignStudent}\n      />\n    </div>\n  );\n};\n\nexport default Appointments;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,aAAa,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,gBAAgB;AACvG,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,MAAM,QAAQ,YAAY;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMoD,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmD,IAAI;IAAEC;EAAM,CAAC,GAAGlD,OAAO,CAAC,CAAC;;EAEjC;EACA,MAAMmD,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACJD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACV,IAAI,IAAI,CAACC,KAAK,EAAE;MACnBb,QAAQ,CAAC,qCAAqC,CAAC;MAC/CF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MAAA,IAAAyB,iBAAA;MACFrB,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMsB,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUb,KAAK;QAAG;MAAE,CAAC;MAChE,MAAMc,YAAY,GAAGf,IAAI,CAACgB,UAAU,MAAAL,iBAAA,GAAIX,IAAI,CAACiB,WAAW,cAAAN,iBAAA,uBAAhBA,iBAAA,CAAkBO,EAAE;;MAE5D;MACA;MACA,MAAMC,eAAe,GAAG,MAAMrE,KAAK,CAACsE,GAAG,CACrC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,yCAAyCC,kBAAkB,CAACT,YAAY,CAAC,EAAE,EAC3GH,MACF,CAAC;;MAED;MACA,IAAIO,eAAe,CAACM,IAAI,IAAIN,eAAe,CAACM,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3DC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAET,eAAe,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;;QAE/D;QACA,MAAMI,aAAa,GAAGV,eAAe,CAACM,IAAI,CAACK,IAAI,CAACC,IAAI,IAClDA,IAAI,CAACC,UAAU,IAAKD,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACE,OAAO,CAACD,UACnD,CAAC;QAEDL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEC,aAAa,CAAC;;QAE3D;QACAV,eAAe,CAACM,IAAI,CAACS,OAAO,CAAC,CAACH,IAAI,EAAEI,KAAK,KAAK;UAAA,IAAAC,aAAA;UAC5C,MAAMJ,UAAU,GAAGD,IAAI,CAACC,UAAU,MAAAI,aAAA,GAAIL,IAAI,CAACE,OAAO,cAAAG,aAAA,uBAAZA,aAAA,CAAcJ,UAAU;UAC9DL,OAAO,CAACC,GAAG,CAAC,eAAeO,KAAK,GAAG,CAAC,gBAAgB,EAAEH,UAAU,IAAI,KAAK,CAAC;QAC5E,CAAC,CAAC;MACJ;MAEAtD,eAAe,CAACyC,eAAe,CAACM,IAAI,IAAI,EAAE,CAAC;;MAE3C;MACA;MACA7C,WAAW,CAAC,EAAE,CAAC;MACfQ,QAAQ,CAAC,EAAE,CAAC;MAEZ,IAAI+B,eAAe,CAACM,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;QACrCtC,QAAQ,CAAC,4CAA4C,CAAC;MACxD;MAEAuC,OAAO,CAACC,GAAG,CAAC,WAAWT,eAAe,CAACM,IAAI,CAACC,MAAM,iCAAiCX,YAAY,EAAE,CAAC;IACpG,CAAC,CAAC,OAAOsB,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;MACZjB,OAAO,CAACxC,KAAK,CAAC,cAAc,EAAE,EAAAmD,aAAA,GAAAD,GAAG,CAACQ,QAAQ,cAAAP,aAAA,uBAAZA,aAAA,CAAcb,IAAI,KAAIY,GAAG,CAACS,OAAO,CAAC;MAChE,MAAMC,YAAY,GAChB,EAAAR,cAAA,GAAAF,GAAG,CAACQ,QAAQ,cAAAN,cAAA,uBAAZA,cAAA,CAAcS,MAAM,MAAK,GAAG,GACxB,kCAAkC,GAClC,EAAAR,cAAA,GAAAH,GAAG,CAACQ,QAAQ,cAAAL,cAAA,uBAAZA,cAAA,CAAcQ,MAAM,MAAK,GAAG,GAC5B,oCAAoC,GACpC,EAAAP,cAAA,GAAAJ,GAAG,CAACQ,QAAQ,cAAAJ,cAAA,uBAAZA,cAAA,CAAcO,MAAM,MAAK,GAAG,GAC5B,8DAA8D,GAC9D,EAAAN,cAAA,GAAAL,GAAG,CAACQ,QAAQ,cAAAH,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjB,IAAI,cAAAkB,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,KAAI,6BAA6B;MAClE1D,QAAQ,CAAC2D,YAAY,CAAC;MACtB,IAAI,EAAAH,cAAA,GAAAP,GAAG,CAACQ,QAAQ,cAAAD,cAAA,uBAAZA,cAAA,CAAcI,MAAM,MAAK,GAAG,EAAEjD,QAAQ,CAAC,QAAQ,CAAC;IACtD,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAM2D,aAAa,GAAGA,CAAA,KAAM;IAC1BvC,SAAS,CAAC,CAAC;EACb,CAAC;;EAED;EACA,MAAMwC,sBAAsB,GAAIC,WAAW,IAAK;IAC9C3D,sBAAsB,CAAC2D,WAAW,CAAC;IACnCzD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM0D,iBAAiB,GAAGA,CAACC,CAAC,EAAEF,WAAW,KAAK;IAC5CE,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB9D,sBAAsB,CAAC2D,WAAW,CAAC;IACnCvD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM2D,mBAAmB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,OAAO,KAAK;IAC5D,IAAI;MACF,MAAM7C,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUb,KAAK;QAAG;MAAE,CAAC;;MAEhE;MACA,MAAMyD,UAAU,GAAG;QACjBC,MAAM,EAAEF,OAAO,CAACG,SAAS;QACzBC,WAAW,EAAE;MACf,CAAC;;MAED;MACA,MAAMhB,QAAQ,GAAG,MAAM/F,KAAK,CAACgH,GAAG,CAC9B,GAAGzC,OAAO,CAACC,GAAG,CAACC,iBAAiB,qBAAqBiC,aAAa,SAAS,EAC3EE,UAAU,EACV9C,MACF,CAAC;;MAED;MACA,MAAMmD,kBAAkB,GAAGlB,QAAQ,CAACpB,IAAI;;MAExC;;MAEA;MACA,MAAMuC,mBAAmB,GAAGvF,YAAY,CAACwF,IAAI,CAAClC,IAAI,IAAIA,IAAI,CAACmC,GAAG,KAAKV,aAAa,CAAC;MAEjF,IAAIQ,mBAAmB,EAAE;QACvB;QACA,IAAI;UAAA,IAAAG,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UACF;UACA,IAAIrC,UAAU,GAAG,EAAE;UACnB,IAAIgC,mBAAmB,CAAChC,UAAU,IAAIgC,mBAAmB,CAAChC,UAAU,CAACsC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YAClFtC,UAAU,GAAGgC,mBAAmB,CAAChC,UAAU,CAACsC,IAAI,CAAC,CAAC;YAClD3C,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEI,UAAU,CAAC;UACpE,CAAC,MAAM,IAAIgC,mBAAmB,CAAC/B,OAAO,IAAI+B,mBAAmB,CAAC/B,OAAO,CAACD,UAAU,IAAIgC,mBAAmB,CAAC/B,OAAO,CAACD,UAAU,CAACsC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACxItC,UAAU,GAAGgC,mBAAmB,CAAC/B,OAAO,CAACD,UAAU,CAACsC,IAAI,CAAC,CAAC;YAC1D3C,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEI,UAAU,CAAC;UAChE,CAAC,MAAM;YACLL,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEoC,mBAAmB,CAAC;YAC7E;YACA;YACA,IAAI,CAACA,mBAAmB,CAAChC,UAAU,IAAIgC,mBAAmB,CAAChC,UAAU,CAACuC,UAAU,CAAC,OAAO,CAAC,EAAE;cACzFvC,UAAU,GAAG,QAAQwC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;cACjC9C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEI,UAAU,CAAC;;cAE1D;cACA,IAAI;gBACF;gBACA,MAAMlF,KAAK,CAACgH,GAAG,CACb,0CAA0CE,mBAAmB,CAACE,GAAG,EAAE,EACnE;kBAAElC;gBAAW,CAAC,EACd;kBAAEnB,OAAO,EAAE;oBAAEC,aAAa,EAAE,UAAUb,KAAK;kBAAG;gBAAE,CAClD,CAAC;gBACD0B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEI,UAAU,CAAC;cACjE,CAAC,CAAC,OAAOK,GAAG,EAAE;gBACZV,OAAO,CAACxC,KAAK,CAAC,6CAA6C,EAAEkD,GAAG,CAAC;cACnE;YACF,CAAC,MAAM;cACL;cACAL,UAAU,GAAGgC,mBAAmB,CAAChC,UAAU;cAC3CL,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEI,UAAU,CAAC;YACzE;UACF;;UAEA;UACAL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;YAC/BsC,GAAG,EAAEF,mBAAmB,CAACE,GAAG;YAC5BjC,OAAO,EAAE+B,mBAAmB,CAAC/B,OAAO;YACpCD,UAAU,EAAEA,UAAU;YACtB0C,QAAQ,EAAE,EAAAP,qBAAA,GAAAH,mBAAmB,CAAC/B,OAAO,cAAAkC,qBAAA,uBAA3BA,qBAAA,CAA6BO,QAAQ,KAAIV,mBAAmB,CAACU,QAAQ;YAC/EC,qBAAqB,EAAEX,mBAAmB,CAAChC,UAAU;YACrD4C,iBAAiB,GAAAR,sBAAA,GAAEJ,mBAAmB,CAAC/B,OAAO,cAAAmC,sBAAA,uBAA3BA,sBAAA,CAA6BpC;UAClD,CAAC,CAAC;;UAEF;UACA,MAAM6C,WAAW,GAAG,EAAAR,sBAAA,GAAAL,mBAAmB,CAAC/B,OAAO,cAAAoC,sBAAA,uBAA3BA,sBAAA,CAA6BK,QAAQ,KAAIV,mBAAmB,CAACU,QAAQ,IAAI,iBAAiB;;UAE9G;UACA,IAAII,aAAa,GAAG,KAAK;UACzB,IAAIC,iBAAiB,GAAG,IAAI;UAE5B,IAAI/C,UAAU,EAAE;YACd,IAAI;cACF;cACA,MAAMgD,gBAAgB,GAAG,MAAMlI,KAAK,CAACsE,GAAG,CACtC,8CAA8CqC,OAAO,CAACG,SAAS,EAAE,EACjEhD,MACF,CAAC;;cAED;cACA,MAAMqE,eAAe,GAAGD,gBAAgB,CAACvD,IAAI,CAACwC,IAAI,CAChDhC,OAAO,IAAIA,OAAO,CAACD,UAAU,KAAKA,UACpC,CAAC;cAED,IAAIiD,eAAe,EAAE;gBACnBH,aAAa,GAAG,IAAI;gBACpBC,iBAAiB,GAAGE,eAAe,CAACf,GAAG;gBACvCvC,OAAO,CAACC,GAAG,CAAC,0BAA0BqD,eAAe,CAACP,QAAQ,KAAK1C,UAAU,iBAAiByB,OAAO,CAACG,SAAS,EAAE,CAAC;;gBAElH;gBACAsB,MAAM,CAACC,oBAAoB,GAAG,KAAK;cACrC;YACF,CAAC,CAAC,OAAOC,QAAQ,EAAE;cACjBzD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEwD,QAAQ,CAACtC,OAAO,CAAC;cACjE;YACF;UACF;UAEA,IAAI,CAACgC,aAAa,EAAE;YAAA,IAAAO,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAClB;YACA,IAAI,CAACzD,UAAU,IAAIA,UAAU,CAACsC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAItC,UAAU,CAACuC,UAAU,CAAC,OAAO,CAAC,EAAE;cAC7E5C,OAAO,CAAC+D,IAAI,CAAC,oEAAoE,CAAC;cAClF1D,UAAU,GAAG,QAAQwC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;YACrC,CAAC,MAAM;cACL9C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEI,UAAU,CAAC;YACxD;;YAEA;YACA,MAAM2D,WAAW,GAAG;cAClBjB,QAAQ,EAAEG,WAAW;cACrB7C,UAAU,EAAEA,UAAU;cACtB4D,WAAW,EAAE,EAAAP,sBAAA,GAAArB,mBAAmB,CAAC/B,OAAO,cAAAoD,sBAAA,uBAA3BA,sBAAA,CAA6BO,WAAW,KAAI5B,mBAAmB,CAAC4B,WAAW,IAAI,cAAc;cAC1GC,MAAM,EAAE,EAAAP,sBAAA,GAAAtB,mBAAmB,CAAC/B,OAAO,cAAAqD,sBAAA,uBAA3BA,sBAAA,CAA6BO,MAAM,KAAI7B,mBAAmB,CAAC6B,MAAM,IAAI,OAAO;cACpFC,GAAG,EAAE,EAAAP,sBAAA,GAAAvB,mBAAmB,CAAC/B,OAAO,cAAAsD,sBAAA,uBAA3BA,sBAAA,CAA6BO,GAAG,KAAI9B,mBAAmB,CAAC8B,GAAG,IAAI,CAAC;cACrEC,OAAO,EAAE,EAAAP,sBAAA,GAAAxB,mBAAmB,CAAC/B,OAAO,cAAAuD,sBAAA,uBAA3BA,sBAAA,CAA6BO,OAAO,KAAI/B,mBAAmB,CAAC+B,OAAO,IAAI,EAAE;cAClFC,UAAU,EAAE,EAAAP,sBAAA,GAAAzB,mBAAmB,CAAC/B,OAAO,cAAAwD,sBAAA,uBAA3BA,sBAAA,CAA6BO,UAAU,KAAIhC,mBAAmB,CAACgC,UAAU,IAAI,EAAE;cAC3FC,IAAI,EAAExC,OAAO,CAACG,SAAS;cAAE;cACzBsC,WAAW,EAAE;gBACXC,cAAc,EAAEnC,mBAAmB,CAACmC,cAAc,IAAI,EAAE;gBACxDC,eAAe,EAAE,EAAE;gBACnBC,wBAAwB,EAAE,EAAE;gBAC5BC,kBAAkB,EAAE;cACtB;YACF,CAAC;YAED3E,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE6B,OAAO,CAACG,SAAS,CAAC;YAEzEjC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;cACvD8C,QAAQ,EAAEiB,WAAW,CAACjB,QAAQ;cAC9B1C,UAAU,EAAE2D,WAAW,CAAC3D,UAAU;cAClC4D,WAAW,EAAED,WAAW,CAACC,WAAW;cACpCC,MAAM,EAAEF,WAAW,CAACE,MAAM;cAC1BC,GAAG,EAAEH,WAAW,CAACG,GAAG;cACpBG,IAAI,EAAEN,WAAW,CAACM;YACpB,CAAC,CAAC;;YAEF;YACA,IAAI;cACFtE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+D,WAAW,CAAC;cACvD,MAAMY,cAAc,GAAG,MAAMzJ,KAAK,CAAC0J,IAAI,CACrC,GAAGnF,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAC/CoE,WAAW,EACX/E,MACF,CAAC;cAEDe,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE2E,cAAc,CAAC9E,IAAI,CAAC;cAE9D,IAAI8E,cAAc,CAAC9E,IAAI,CAACQ,OAAO,EAAE;gBAC/BN,OAAO,CAACC,GAAG,CAAC,8BAA8B+D,WAAW,CAACjB,QAAQ,wBAAwBjB,OAAO,CAACG,SAAS,EAAE,CAAC;gBAC1GsB,MAAM,CAACC,oBAAoB,GAAG,IAAI;;gBAElC;gBACAJ,iBAAiB,GAAGwB,cAAc,CAAC9E,IAAI,CAACQ,OAAO,CAACiC,GAAG;cACrD,CAAC,MAAM;gBACLvC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;gBAC3DsD,MAAM,CAACC,oBAAoB,GAAG,IAAI;cACpC;YACF,CAAC,CAAC,OAAOsB,SAAS,EAAE;cAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;cAClBlF,OAAO,CAACxC,KAAK,CAAC,yBAAyB,EAAE,EAAAuH,mBAAA,GAAAD,SAAS,CAAC5D,QAAQ,cAAA6D,mBAAA,uBAAlBA,mBAAA,CAAoBjF,IAAI,KAAIgF,SAAS,CAAC3D,OAAO,CAAC;;cAEvF;cACA,KAAA6D,oBAAA,GAAIF,SAAS,CAAC5D,QAAQ,cAAA8D,oBAAA,gBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBlF,IAAI,cAAAmF,qBAAA,gBAAAC,sBAAA,GAAxBD,qBAAA,CAA0B9D,OAAO,cAAA+D,sBAAA,eAAjCA,sBAAA,CAAmCC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;gBACjEnF,OAAO,CAACC,GAAG,CAAC,2BAA2BI,UAAU,iCAAiC,CAAC;gBACnFkD,MAAM,CAACC,oBAAoB,GAAG,KAAK;cACrC,CAAC,MAAM;gBACL;gBACAD,MAAM,CAAC6B,kBAAkB,GAAG,IAAI;gBAChC7B,MAAM,CAACC,oBAAoB,GAAG,KAAK;cACrC;YACF;UACF,CAAC,MAAM;YACL;YACAD,MAAM,CAACC,oBAAoB,GAAG,KAAK;YACnCxD,OAAO,CAACC,GAAG,CAAC,qCAAqCiD,WAAW,iBAAiBpB,OAAO,CAACG,SAAS,EAAE,CAAC;UACnG;;UAEA;UACA,IAAImB,iBAAiB,IAAIf,mBAAmB,CAAC/B,OAAO,EAAE;YACpD,IAAI;cACF;cACA,MAAMnF,KAAK,CAACgH,GAAG,CACb,0CAA0CE,mBAAmB,CAACE,GAAG,UAAU,EAC3E;gBAAE8C,SAAS,EAAEjC;cAAkB,CAAC,EAChCnE,MACF,CAAC;cAEDe,OAAO,CAACC,GAAG,CAAC,uBAAuBoC,mBAAmB,CAACE,GAAG,uBAAuBa,iBAAiB,EAAE,CAAC;YACvG,CAAC,CAAC,OAAOkC,SAAS,EAAE;cAClBtF,OAAO,CAACxC,KAAK,CAAC,0CAA0C,EAAE8H,SAAS,CAACnE,OAAO,CAAC;YAC9E;UACF;QACF,CAAC,CAAC,OAAOoE,UAAU,EAAE;UAAA,IAAAC,oBAAA;UACnBxF,OAAO,CAACxC,KAAK,CAAC,gCAAgC,EAAE,EAAAgI,oBAAA,GAAAD,UAAU,CAACrE,QAAQ,cAAAsE,oBAAA,uBAAnBA,oBAAA,CAAqB1F,IAAI,KAAIyF,UAAU,CAACpE,OAAO,CAAC;UAChG;UACAoC,MAAM,CAACC,oBAAoB,GAAG,KAAK;UACnCD,MAAM,CAAC6B,kBAAkB,GAAG,IAAI;UAChC;UACA;QACF;MACF;;MAEA;MACA,MAAMK,mBAAmB,GAAG3I,YAAY,CAAC4I,GAAG,CAACtF,IAAI,IAC/CA,IAAI,CAACmC,GAAG,KAAKV,aAAa,GACtB;QACE,GAAGzB,IAAI;QACP4B,MAAM,EAAEF,OAAO,CAACG,SAAS;QACzBC,WAAW,EAAE,SAAS;QACtByD,WAAW,EAAE7D,OAAO,CAAC8D,IAAI;QACzB3D,SAAS,EAAEH,OAAO,CAACG;MACrB,CAAC,GACD7B,IACN,CAAC;MAEDrD,eAAe,CAAC0I,mBAAmB,CAAC;;MAEpC;MACA,IAAI7H,mBAAmB,IAAIA,mBAAmB,CAAC2E,GAAG,KAAKV,aAAa,EAAE;QACpEhE,sBAAsB,CAAC;UACrB,GAAGD,mBAAmB;UACtBoE,MAAM,EAAEF,OAAO,CAACG,SAAS;UACzBC,WAAW,EAAE,SAAS;UACtByD,WAAW,EAAE7D,OAAO,CAAC8D,IAAI;UACzB3D,SAAS,EAAEH,OAAO,CAACG;QACrB,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI4D,UAAU,GAAG,wCAAwC/D,OAAO,CAAC8D,IAAI,EAAE;;MAEvE;MACA,IAAIvD,mBAAmB,EAAE;QAAA,IAAAyD,sBAAA;QACvB,MAAM5C,WAAW,GAAG,EAAA4C,sBAAA,GAAAzD,mBAAmB,CAAC/B,OAAO,cAAAwF,sBAAA,uBAA3BA,sBAAA,CAA6B/C,QAAQ,KAAIV,mBAAmB,CAACU,QAAQ,IAAI,iBAAiB;;QAE9G;QACA,IAAI,OAAOQ,MAAM,CAACC,oBAAoB,KAAK,WAAW,EAAE;UACtD,IAAID,MAAM,CAAC6B,kBAAkB,EAAE;YAC7BS,UAAU,IAAI,8EAA8E3C,WAAW,GAAG;UAC5G,CAAC,MAAM,IAAIK,MAAM,CAACC,oBAAoB,KAAK,IAAI,EAAE;YAC/CqC,UAAU,IAAI,uCAAuC3C,WAAW,EAAE;UACpE,CAAC,MAAM;YACL2C,UAAU,IAAI,uCAAuC3C,WAAW,GAAG;UACrE;UACA;UACA,OAAOK,MAAM,CAACC,oBAAoB;UAClC,OAAOD,MAAM,CAAC6B,kBAAkB;QAClC;MACF;MAEAjH,iBAAiB,CAAC0H,UAAU,CAAC;MAC7BE,UAAU,CAAC,MAAM5H,iBAAiB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;;MAE7C;MACA4H,UAAU,CAAC,MAAM;QACfhH,SAAS,CAAC,CAAC;MACb,CAAC,EAAE,IAAI,CAAC;MAER,OAAOqD,kBAAkB;IAC3B,CAAC,CAAC,OAAO1B,GAAG,EAAE;MAAA,IAAAsF,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZlG,OAAO,CAACxC,KAAK,CAAC,0BAA0B,EAAE,EAAAwI,cAAA,GAAAtF,GAAG,CAACQ,QAAQ,cAAA8E,cAAA,uBAAZA,cAAA,CAAclG,IAAI,KAAIY,GAAG,CAACS,OAAO,CAAC;MAC5E,MAAM,IAAIgF,KAAK,CAAC,EAAAF,cAAA,GAAAvF,GAAG,CAACQ,QAAQ,cAAA+E,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnG,IAAI,cAAAoG,mBAAA,uBAAlBA,mBAAA,CAAoB/E,OAAO,KAAI,0BAA0B,CAAC;IAC5E;EACF,CAAC;EAEDlG,SAAS,CAAC,MAAM;IACd8D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACV,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;EAE3B,MAAMgI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIC,oBAAoB,CAACtG,MAAM,KAAK,CAAC,EAAE;MACrCtC,QAAQ,CAAC,8BAA8B,CAAC;MACxC;IACF;IAEA,IAAI;MACF,MAAMyB,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,CAAC;MAC3F,MAAMY,IAAI,GAAGuG,oBAAoB,CAACX,GAAG,CAAEtF,IAAI;QAAA,IAAAkG,cAAA,EAAAC,kBAAA;QAAA,OAAK,CAC9C,IAAI1D,IAAI,CAACzC,IAAI,CAACoG,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,EAC/CrG,IAAI,CAACsG,IAAI,IAAI,KAAK,EAClB,EAAAJ,cAAA,GAAAlG,IAAI,CAACE,OAAO,cAAAgG,cAAA,uBAAZA,cAAA,CAAcvD,QAAQ,KAAI3C,IAAI,CAAC2C,QAAQ,IAAI,SAAS,EACpD3C,IAAI,CAACuG,SAAS,IAAIvG,IAAI,CAACwG,IAAI,IAAI,KAAK,EACpCxG,IAAI,CAACiB,MAAM,EACXjB,IAAI,CAACuF,WAAW,IAAIvF,IAAI,CAAC4B,MAAM,IAAI,KAAK,EACxC5B,IAAI,CAACf,UAAU,IAAIhB,IAAI,CAACgB,UAAU,MAAAkH,kBAAA,GAAIlI,IAAI,CAACiB,WAAW,cAAAiH,kBAAA,uBAAhBA,kBAAA,CAAkBhH,EAAE,KAAI,KAAK,CACpE;MAAA,EAAC;;MAEF;MACA,MAAMsH,UAAU,GAAG,CACjB3H,OAAO,CAAC4H,IAAI,CAAC,GAAG,CAAC,EACjB,GAAGhH,IAAI,CAAC4F,GAAG,CAACqB,GAAG,IAAIA,GAAG,CAACrB,GAAG,CAACsB,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAC3D,CAACA,IAAI,CAAC,IAAI,CAAC;;MAEZ;MACA,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;QAAED,IAAI,EAAE;MAAyB,CAAC,CAAC;MACvE3K,MAAM,CAACgL,IAAI,EAAE,gBAAgB,IAAIpE,IAAI,CAAC,CAAC,CAACsE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;;MAEzE;MACA,MAAMlJ,cAAc,GAAGmJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACpDpJ,cAAc,CAACqJ,SAAS,GAAG,+EAA+E;MAC1GrJ,cAAc,CAACsJ,WAAW,GAAG,qCAAqC;MAClEH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACxJ,cAAc,CAAC;;MAEzC;MACA6H,UAAU,CAAC,MAAM;QACfsB,QAAQ,CAACI,IAAI,CAACE,WAAW,CAACzJ,cAAc,CAAC;MAC3C,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOwC,GAAG,EAAE;MACZV,OAAO,CAACxC,KAAK,CAAC,iCAAiC,EAAEkD,GAAG,CAAC;MACrDjD,QAAQ,CAAC,oDAAoD,CAAC;IAChE;EACF,CAAC;EAED,MAAMmK,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIC,QAAQ,GAAG/K,YAAY;;IAE3B;IACA,IAAII,SAAS,KAAK,OAAO,EAAE;MACzB2K,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAEC,CAAC,IAAK;QAChC,MAAMC,QAAQ,GAAG,IAAInF,IAAI,CAACkF,CAAC,CAACvB,IAAI,CAAC;QACjC,MAAMyB,SAAS,GAAG,IAAIpF,IAAI,CAAC,CAAC;QAC5B,OAAOmF,QAAQ,CAACE,YAAY,CAAC,CAAC,KAAKD,SAAS,CAACC,YAAY,CAAC,CAAC;MAC7D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIhL,SAAS,KAAK,UAAU,EAAE;MACnC,MAAMiL,QAAQ,GAAG,IAAItF,IAAI,CAAC,CAAC;MAC3BsF,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACxCR,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAEC,CAAC,IAAK;QAChC,MAAMC,QAAQ,GAAG,IAAInF,IAAI,CAACkF,CAAC,CAACvB,IAAI,CAAC;QACjC,OAAOwB,QAAQ,CAACE,YAAY,CAAC,CAAC,KAAKC,QAAQ,CAACD,YAAY,CAAC,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIhL,SAAS,KAAK,MAAM,EAAE;MAC/B,MAAMoL,OAAO,GAAG,IAAIzF,IAAI,CAAC,CAAC;MAC1ByF,OAAO,CAACF,OAAO,CAACE,OAAO,CAACD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACtCR,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAEC,CAAC,IAAK;QAChC,MAAMC,QAAQ,GAAG,IAAInF,IAAI,CAACkF,CAAC,CAACvB,IAAI,CAAC;QACjC,OAAOwB,QAAQ,IAAI,IAAInF,IAAI,CAAC,CAAC,IAAImF,QAAQ,IAAIM,OAAO;MACtD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIlL,YAAY,EAAE;MAChByK,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC1G,MAAM,KAAKjE,YAAY,CAAC;IAC9D;;IAEA;IACA,OAAOyK,QAAQ,CAACU,IAAI,CAAC,CAACR,CAAC,EAAES,CAAC,KAAK;MAC7B,MAAMC,KAAK,GAAG,IAAI5F,IAAI,CAACkF,CAAC,CAACvB,IAAI,CAAC;MAC9B,MAAMkC,KAAK,GAAG,IAAI7F,IAAI,CAAC2F,CAAC,CAAChC,IAAI,CAAC;MAC9B,IAAIiC,KAAK,CAACE,OAAO,CAAC,CAAC,KAAKD,KAAK,CAACC,OAAO,CAAC,CAAC,EAAE,OAAOF,KAAK,GAAGC,KAAK;MAC7D,OAAO,CAACX,CAAC,CAACrB,IAAI,IAAI,EAAE,EAAEkC,aAAa,CAACJ,CAAC,CAAC9B,IAAI,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC;EACJ,CAAC;EAED,MAAML,oBAAoB,GAAGuB,kBAAkB,CAAC,CAAC;EAEjD,IAAItK,OAAO,EAAE,oBAAOnB,OAAA,CAACZ,MAAM;IAAAsN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAE9B,oBACE7M,OAAA;IAAKoL,SAAS,EAAC,0BAA0B;IAAA0B,QAAA,gBACvC9M,OAAA,CAACb,gBAAgB;MAAC4N,MAAM,EAAEtM,WAAY;MAACuM,SAAS,EAAEtM;IAAe;MAAAgM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpE7M,OAAA;MAAKoL,SAAS,EAAC,sCAAsC;MAAA0B,QAAA,gBACnD9M,OAAA,CAACd,MAAM;QAAC+N,aAAa,EAAEA,CAAA,KAAMvM,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAiM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7D7M,OAAA;QAAMoL,SAAS,EAAC,4BAA4B;QAAC8B,KAAK,EAAE;UAAE9M,UAAU,EAAE,oCAAoCH,mBAAmB,CAACC,OAAO,OAAOD,mBAAmB,CAACG,UAAU;QAAI,CAAE;QAAA0M,QAAA,eAC1K9M,OAAA;UAAKoL,SAAS,EAAC,mBAAmB;UAAA0B,QAAA,GAC/BzL,KAAK,iBACJrB,OAAA,CAACX,MAAM,CAAC8N,GAAG;YACTC,OAAO,EAAE;cAAE9K,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChC0K,OAAO,EAAE;cAAE/K,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9ByI,SAAS,EAAC,mEAAmE;YAAA0B,QAAA,eAE7E9M,OAAA;cAAKoL,SAAS,EAAC,mBAAmB;cAAA0B,QAAA,gBAChC9M,OAAA;gBAAKoL,SAAS,EAAC,2BAA2B;gBAACkC,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAChF9M,OAAA;kBACEwN,QAAQ,EAAC,SAAS;kBAClBC,CAAC,EAAC,mHAAmH;kBACrHC,QAAQ,EAAC;gBAAS;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7M,OAAA;gBAAGoL,SAAS,EAAC,cAAc;gBAAA0B,QAAA,EAAEzL;cAAK;gBAAAqL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAED7M,OAAA,CAACX,MAAM,CAAC8N,GAAG;YAACC,OAAO,EAAE;cAAE9K,OAAO,EAAE;YAAE,CAAE;YAAC+K,OAAO,EAAE;cAAE/K,OAAO,EAAE;YAAE,CAAE;YAACE,UAAU,EAAE;cAAEmL,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAC1F9M,OAAA;cAAKoL,SAAS,EAAC,4EAA4E;cAAA0B,QAAA,gBACzF9M,OAAA;gBAAA8M,QAAA,gBACE9M,OAAA;kBAAIoL,SAAS,EAAC,qCAAqC;kBAAC8B,KAAK,EAAE;oBAAEU,KAAK,EAAE3N,mBAAmB,CAACC;kBAAQ,CAAE;kBAAA4M,QAAA,EAAC;gBAAY;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpH7M,OAAA;kBAAGoL,SAAS,EAAC,eAAe;kBAAA0B,QAAA,EAAC;gBAAsC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACN7M,OAAA;gBAAKoL,SAAS,EAAC,yBAAyB;gBAAA0B,QAAA,gBACtC9M,OAAA;kBACE6N,OAAO,EAAE1I,aAAc;kBACvB2I,QAAQ,EAAEvM,UAAW;kBACrB6J,SAAS,EAAE,2GACT7J,UAAU,GAAG,+BAA+B,GAAG,EAAE,EAChD;kBACH2L,KAAK,EAAE;oBACLa,eAAe,EAAE9N,mBAAmB,CAACC;kBACvC,CAAE;kBAAA4M,QAAA,gBAEF9M,OAAA,CAACT,MAAM;oBAAC6L,SAAS,EAAE,gBAAgB7J,UAAU,GAAG,cAAc,GAAG,EAAE;kBAAG;oBAAAmL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzE7M,OAAA;oBAAA8M,QAAA,EAAOvL,UAAU,GAAG,eAAe,GAAG;kBAAS;oBAAAmL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACT7M,OAAA;kBACE6N,OAAO,EAAE5D,oBAAqB;kBAC9BmB,SAAS,EAAC,yGAAyG;kBACnH8B,KAAK,EAAE;oBACLa,eAAe,EAAE9N,mBAAmB,CAACK;kBACvC,CAAE;kBAAAwM,QAAA,gBAEF9M,OAAA,CAACR,UAAU;oBAAC4L,SAAS,EAAC;kBAAc;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvC7M,OAAA;oBAAA8M,QAAA,EAAM;kBAAU;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7M,OAAA,CAACX,MAAM,CAAC8N,GAAG;cACTa,QAAQ,EAAE5L,SAAU;cACpBgL,OAAO,EAAC,QAAQ;cAChBa,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB/C,SAAS,EAAC,kHAAkH;cAAA0B,QAAA,eAE5H9M,OAAA;gBAAKoL,SAAS,EAAC,KAAK;gBAAA0B,QAAA,gBAClB9M,OAAA;kBAAKoL,SAAS,EAAC,kFAAkF;kBAAA0B,QAAA,gBAC/F9M,OAAA;oBAAIoL,SAAS,EAAC,qCAAqC;oBAAC8B,KAAK,EAAE;sBAAEU,KAAK,EAAE3N,mBAAmB,CAACC;oBAAQ,CAAE;oBAAA4M,QAAA,gBAChG9M,OAAA,CAACV,aAAa;sBAAC8L,SAAS,EAAC,cAAc;sBAAC8B,KAAK,EAAE;wBAAEU,KAAK,EAAE3N,mBAAmB,CAACC;sBAAQ;oBAAE;sBAAAwM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,oBAE3F;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL7M,OAAA;oBAAKoL,SAAS,EAAC,mBAAmB;oBAAA0B,QAAA,gBAChC9M,OAAA,CAACP,WAAW;sBAAC2L,SAAS,EAAC,cAAc;sBAAC8B,KAAK,EAAE;wBAAEU,KAAK,EAAE3N,mBAAmB,CAACC;sBAAQ;oBAAE;sBAAAwM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvF7M,OAAA;sBAAMoL,SAAS,EAAC,aAAa;sBAAC8B,KAAK,EAAE;wBAAEU,KAAK,EAAE3N,mBAAmB,CAACC;sBAAQ,CAAE;sBAAA4M,QAAA,GAAC,SAAO,EAACnM,YAAY,CAACiD,MAAM;oBAAA;sBAAA8I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7M,OAAA;kBAAKoL,SAAS,EAAC,sCAAsC;kBAAA0B,QAAA,gBACnD9M,OAAA;oBACEoO,KAAK,EAAErN,SAAU;oBACjBsN,QAAQ,EAAG9I,CAAC,IAAKvE,YAAY,CAACuE,CAAC,CAAC+I,MAAM,CAACF,KAAK,CAAE;oBAC9ChD,SAAS,EAAC,8DAA8D;oBACxE8B,KAAK,EAAE;sBACLqB,OAAO,EAAE,MAAM;sBACfC,SAAS,EAAE,MAAM;sBACjBC,WAAW,EAAE,SAAS;sBACtB,QAAQ,EAAE;wBAAEA,WAAW,EAAExO,mBAAmB,CAACC;sBAAQ;oBACvD,CAAE;oBAAA4M,QAAA,gBAEF9M,OAAA;sBAAQoO,KAAK,EAAC,KAAK;sBAAAtB,QAAA,EAAC;oBAAS;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC7M,OAAA;sBAAQoO,KAAK,EAAC,OAAO;sBAAAtB,QAAA,EAAC;oBAAK;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpC7M,OAAA;sBAAQoO,KAAK,EAAC,UAAU;sBAAAtB,QAAA,EAAC;oBAAQ;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1C7M,OAAA;sBAAQoO,KAAK,EAAC,MAAM;sBAAAtB,QAAA,EAAC;oBAAS;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACT7M,OAAA;oBACEoO,KAAK,EAAEnN,YAAa;oBACpBoN,QAAQ,EAAG9I,CAAC,IAAKrE,eAAe,CAACqE,CAAC,CAAC+I,MAAM,CAACF,KAAK,CAAE;oBACjDhD,SAAS,EAAC,8DAA8D;oBACxE8B,KAAK,EAAE;sBACLqB,OAAO,EAAE,MAAM;sBACfC,SAAS,EAAE,MAAM;sBACjBC,WAAW,EAAE,SAAS;sBACtB,QAAQ,EAAE;wBAAEA,WAAW,EAAExO,mBAAmB,CAACC;sBAAQ;oBACvD,CAAE;oBAAA4M,QAAA,gBAEF9M,OAAA;sBAAQoO,KAAK,EAAC,EAAE;sBAAAtB,QAAA,EAAC;oBAAY;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC7M,OAAA;sBAAQoO,KAAK,EAAC,SAAS;sBAAAtB,QAAA,EAAC;oBAAO;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC7M,OAAA;sBAAQoO,KAAK,EAAC,WAAW;sBAAAtB,QAAA,EAAC;oBAAS;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C7M,OAAA;sBAAQoO,KAAK,EAAC,WAAW;sBAAAtB,QAAA,EAAC;oBAAS;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN7M,OAAA;kBAAKoL,SAAS,EAAC,iBAAiB;kBAAA0B,QAAA,eAC9B9M,OAAA;oBAAOoL,SAAS,EAAC,qCAAqC;oBAAA0B,QAAA,gBACpD9M,OAAA;sBAAOoL,SAAS,EAAC,YAAY;sBAAA0B,QAAA,eAC3B9M,OAAA;wBAAA8M,QAAA,gBACE9M,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAA0B,QAAA,EAAC;wBAAI;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACxG7M,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAA0B,QAAA,EAAC;wBAAI;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACxG7M,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAA0B,QAAA,EAAC;wBAAO;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3G7M,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAA0B,QAAA,EAAC;wBAAS;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC7G7M,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAA0B,QAAA,EAAC;wBAAM;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1G7M,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAA0B,QAAA,EAAC;wBAAO;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3G7M,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAA0B,QAAA,EAAC;wBAAO;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACR7M,OAAA;sBAAOoL,SAAS,EAAC,mCAAmC;sBAAA0B,QAAA,EACjD5C,oBAAoB,CAACtG,MAAM,KAAK,CAAC,gBAChC5D,OAAA;wBAAA8M,QAAA,eACE9M,OAAA;0BAAI0O,OAAO,EAAC,GAAG;0BAACtD,SAAS,EAAC,uBAAuB;0BAAA0B,QAAA,eAC/C9M,OAAA;4BAAKoL,SAAS,EAAC,2CAA2C;4BAAA0B,QAAA,gBACxD9M,OAAA;8BAAKoL,SAAS,EAAC,uBAAuB;8BAAC8B,KAAK,EAAE;gCAAEa,eAAe,EAAE,GAAG9N,mBAAmB,CAACC,OAAO;8BAAK,CAAE;8BAAA4M,QAAA,eACpG9M,OAAA,CAACV,aAAa;gCAAC8L,SAAS,EAAC,WAAW;gCAAC8B,KAAK,EAAE;kCAAEU,KAAK,EAAE,GAAG3N,mBAAmB,CAACC,OAAO;gCAAK;8BAAE;gCAAAwM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1F,CAAC,eACN7M,OAAA;8BAAIoL,SAAS,EAAC,mCAAmC;8BAAA0B,QAAA,EAAC;4BAAqB;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC5E7M,OAAA;8BAAGoL,SAAS,EAAC,oBAAoB;8BAAA0B,QAAA,EAAC;4BAA6D;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC,eACnG7M,OAAA;8BAAGoL,SAAS,EAAC,cAAc;8BAAC8B,KAAK,EAAE;gCAAEU,KAAK,EAAE3N,mBAAmB,CAACC;8BAAQ,CAAE;8BAAA4M,QAAA,EAAC;4BAA6C;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAEL3C,oBAAoB,CAACX,GAAG,CAAEtF,IAAI;wBAAA,IAAA0K,cAAA,EAAAC,cAAA;wBAAA,oBAC5B5O,OAAA,CAACX,MAAM,CAACwP,EAAE;0BAERb,QAAQ,EAAEtL,IAAK;0BACf0I,SAAS,EAAC,iCAAiC;0BAC3CyC,OAAO,EAAEA,CAAA,KAAMzI,sBAAsB,CAACnB,IAAI,CAAE;0BAAA6I,QAAA,gBAE5C9M,OAAA;4BAAIoL,SAAS,EAAC,+DAA+D;4BAAA0B,QAAA,EAC1E,IAAIpG,IAAI,CAACzC,IAAI,CAACoG,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;8BAC/CwE,OAAO,EAAE,OAAO;8BAChBC,KAAK,EAAE,OAAO;8BACdC,GAAG,EAAE;4BACP,CAAC;0BAAC;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA,CAAC,eACL7M,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAA0B,QAAA,EAAE7I,IAAI,CAACsG,IAAI,IAAI;0BAAK;4BAAAmC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC3F7M,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAA0B,QAAA,GAC9D,EAAA6B,cAAA,GAAA1K,IAAI,CAACE,OAAO,cAAAwK,cAAA,uBAAZA,cAAA,CAAc/H,QAAQ,KAAI3C,IAAI,CAAC2C,QAAQ,IAAI,SAAS,eACrD5G,OAAA;8BAAKoL,SAAS,EAAC,uBAAuB;8BAAA0B,QAAA,GAAC,MACjC,EAAC7I,IAAI,CAACC,UAAU,MAAA0K,cAAA,GAAI3K,IAAI,CAACE,OAAO,cAAAyK,cAAA,uBAAZA,cAAA,CAAc1K,UAAU,KAAI,KAAK;4BAAA;8BAAAwI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACL7M,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAA0B,QAAA,GAC9D7I,IAAI,CAACuG,SAAS,IAAIvG,IAAI,CAACwG,IAAI,IAAI,KAAK,EACpCxG,IAAI,CAACoE,cAAc,iBAAIrI,OAAA;8BAAKoL,SAAS,EAAC,4BAA4B;8BAAA0B,QAAA,GAAC,mBAAiB,EAAC7I,IAAI,CAACoE,cAAc;4BAAA;8BAAAqE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9G,CAAC,eACL7M,OAAA;4BAAIoL,SAAS,EAAC,6BAA6B;4BAAA0B,QAAA,eACzC9M,OAAA;8BACEoL,SAAS,EAAE,sEACTnH,IAAI,CAACiB,MAAM,KAAK,WAAW,GACvB,6BAA6B,GAC7BjB,IAAI,CAACiB,MAAM,KAAK,WAAW,GAC3B,yBAAyB,GACzB,+BAA+B,EAClC;8BAAA4H,QAAA,EAEF7I,IAAI,CAACiB;4BAAM;8BAAAwH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,eACL7M,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAA0B,QAAA,GAC9D7I,IAAI,CAACuF,WAAW,IAAIvF,IAAI,CAAC4B,MAAM,IAAI,KAAK,EACxC5B,IAAI,CAAC6B,SAAS,iBAAI9F,OAAA;8BAAKoL,SAAS,EAAC,uBAAuB;8BAAA0B,QAAA,GAAC,MAAI,EAAC7I,IAAI,CAAC6B,SAAS;4BAAA;8BAAA4G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EACnF5I,IAAI,CAAC8B,WAAW,iBAAI/F,OAAA;8BAAKoL,SAAS,EAAC,uBAAuB;8BAAA0B,QAAA,GAAC,QAAM,EAAC7I,IAAI,CAAC8B,WAAW;4BAAA;8BAAA2G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxF,CAAC,eACL7M,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAA0B,QAAA,eAC/D9M,OAAA;8BAAKoL,SAAS,EAAC,gBAAgB;8BAAA0B,QAAA,gBAC7B9M,OAAA;gCACE6N,OAAO,EAAGtI,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAEtB,IAAI,CAAE;gCAC3CmH,SAAS,EAAC,qDAAqD;gCAC/D8B,KAAK,EAAE;kCACLU,KAAK,EAAE3N,mBAAmB,CAACC;gCAC7B,CAAE;gCACF+O,KAAK,EAAC,mBAAmB;gCAAAnC,QAAA,eAEzB9M,OAAA,CAACN,QAAQ;kCAAC0L,SAAS,EAAC;gCAAS;kCAAAsB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1B,CAAC,eACT7M,OAAA;gCACE6N,OAAO,EAAGtI,CAAC,IAAK;kCACdA,CAAC,CAACC,eAAe,CAAC,CAAC;kCACnBJ,sBAAsB,CAACnB,IAAI,CAAC;gCAC9B,CAAE;gCACFmH,SAAS,EAAC,sDAAsD;gCAChE8B,KAAK,EAAE;kCACLU,KAAK,EAAE;gCACT,CAAE;gCACFqB,KAAK,EAAC,cAAc;gCAAAnC,QAAA,eAEpB9M,OAAA,CAACL,YAAY;kCAACyL,SAAS,EAAC;gCAAS;kCAAAsB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA,GAnEA5I,IAAI,CAACmC,GAAG;0BAAAsG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAoEJ,CAAC;sBAAA,CACb;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGL9K,cAAc,iBACb/B,OAAA;MAAKoL,SAAS,EAAC,2FAA2F;MACxG8B,KAAK,EAAE;QAAEa,eAAe,EAAE9N,mBAAmB,CAACK;MAAO,CAAE;MAAAwM,QAAA,EACtD/K;IAAc;MAAA2K,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACN,eAGD7M,OAAA,CAACJ,uBAAuB;MACtBmN,MAAM,EAAEpL,gBAAiB;MACzBuN,OAAO,EAAEA,CAAA,KAAMtN,mBAAmB,CAAC,KAAK,CAAE;MAC1CyD,WAAW,EAAE5D;IAAoB;MAAAiL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,eAGF7M,OAAA,CAACH,kBAAkB;MACjBkN,MAAM,EAAElL,eAAgB;MACxBqN,OAAO,EAAEA,CAAA,KAAMpN,kBAAkB,CAAC,KAAK,CAAE;MACzCuD,WAAW,EAAE5D,mBAAoB;MACjC0N,QAAQ,EAAE1J;IAAoB;MAAAiH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACrM,EAAA,CA1tBID,YAAY;EAAA,QAaCxB,WAAW,EACJE,OAAO;AAAA;AAAAmQ,EAAA,GAd3B7O,YAAY;AA4tBlB,eAAeA,YAAY;AAAC,IAAA6O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}