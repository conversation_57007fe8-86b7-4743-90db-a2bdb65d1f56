{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\PatientProfile.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport PatientNav from './PatientNav';\nimport { useAuth } from '../context/AuthContext';\nimport { motion } from 'framer-motion';\nimport { FaUser, FaFileMedical, FaProcedures } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst chronicDiseases = ['Diabetes Mellitus', 'Hypertension', 'Cardiovascular Disease', 'Coagulopathy / Bleeding Disorders', 'Thyroid Disorders', 'Pregnancy', 'Lactation', 'Hepatic Diseases', 'Renal Diseases'];\nconst PatientProfile = () => {\n  _s();\n  var _patientData$medicalI, _patientData$medicalI2, _patientData$medicalI3, _patientData$medicalI4, _patientData$medicalI5;\n  const {\n    user,\n    token\n  } = useAuth();\n  const {\n    nationalId\n  } = useParams();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [isEditing, setIsEditing] = useState(false);\n  const [selectedSheetType, setSelectedSheetType] = useState('');\n  const [formData, setFormData] = useState({\n    nationalId: '',\n    fullName: '',\n    phoneNumber: '',\n    gender: '',\n    age: '',\n    address: '',\n    occupation: '',\n    medicalInfo: {\n      chronicDiseases: [],\n      recentSurgicalProcedures: '',\n      currentMedications: '',\n      chiefComplaint: ''\n    }\n  });\n  useEffect(() => {\n    const fetchPatientData = async () => {\n      if (!token) {\n        setError('Please log in to view patient data.');\n        setLoading(false);\n        return;\n      }\n      try {\n        var _patient$medicalInfo, _patient$medicalInfo2, _patient$medicalInfo3, _patient$medicalInfo4;\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const apiUrl = `${baseUrl}/api/patients/public/${nationalId}`;\n        const response = await axios.get(apiUrl);\n        const patient = response.data;\n        setPatientData(patient);\n        setFormData({\n          nationalId: patient.nationalId,\n          fullName: patient.fullName,\n          phoneNumber: patient.phoneNumber,\n          gender: patient.gender,\n          age: patient.age,\n          address: patient.address || '',\n          occupation: patient.occupation || '',\n          medicalInfo: {\n            chronicDiseases: ((_patient$medicalInfo = patient.medicalInfo) === null || _patient$medicalInfo === void 0 ? void 0 : _patient$medicalInfo.chronicDiseases) || [],\n            recentSurgicalProcedures: ((_patient$medicalInfo2 = patient.medicalInfo) === null || _patient$medicalInfo2 === void 0 ? void 0 : _patient$medicalInfo2.recentSurgicalProcedures) || '',\n            currentMedications: ((_patient$medicalInfo3 = patient.medicalInfo) === null || _patient$medicalInfo3 === void 0 ? void 0 : _patient$medicalInfo3.currentMedications) || '',\n            chiefComplaint: ((_patient$medicalInfo4 = patient.medicalInfo) === null || _patient$medicalInfo4 === void 0 ? void 0 : _patient$medicalInfo4.chiefComplaint) || ''\n          }\n        });\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4;\n        console.error('Fetch error:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status, (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data);\n        let message;\n        if (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401) {\n          message = 'Unauthorized: Please log in again.';\n        } else if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 404) {\n          message = `Patient with National ID \"${nationalId}\" not found. Please check the National ID and try again.`;\n        } else {\n          var _err$response5, _err$response5$data;\n          message = ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.message) || 'Failed to load patient data';\n        }\n        setError(message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPatientData();\n  }, [nationalId, token]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === 'chiefComplaint') {\n      setFormData(prev => ({\n        ...prev,\n        medicalInfo: {\n          ...prev.medicalInfo,\n          chiefComplaint: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n  const handleMedicalInfoChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      medicalInfo: {\n        ...prev.medicalInfo,\n        [name]: value\n      }\n    }));\n  };\n  const handleChronicDiseaseChange = disease => {\n    setFormData(prev => {\n      const diseases = prev.medicalInfo.chronicDiseases.includes(disease) ? prev.medicalInfo.chronicDiseases.filter(d => d !== disease) : [...prev.medicalInfo.chronicDiseases, disease];\n      return {\n        ...prev,\n        medicalInfo: {\n          ...prev.medicalInfo,\n          chronicDiseases: diseases\n        }\n      };\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const patientData = {\n        ...formData,\n        drId: user.id // Include drId as required by patientSchema\n      };\n      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.put(`${baseUrl}/api/patients/${nationalId}`, patientData, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data && response.data.patient) {\n        setPatientData(response.data.patient);\n        setIsEditing(false);\n      } else {\n        throw new Error('Invalid patient data received from server');\n      }\n    } catch (err) {\n      var _err$response6, _err$response7, _err$response8, _err$response8$data;\n      console.error('Update error:', (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : _err$response6.status, (_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : _err$response7.data);\n      setError(((_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.message) || 'Failed to update patient data');\n    }\n  };\n  const handleEditToggle = () => {\n    setIsEditing(!isEditing);\n    setError(''); // Clear any previous errors when toggling edit mode\n  };\n\n  // Animation variants\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PatientNav, {\n          selectedSheetType: selectedSheetType,\n          setSelectedSheetType: setSelectedSheetType\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              className: \"text-center\",\n              children: \"Loading patient data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !patientData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PatientNav, {\n          selectedSheetType: selectedSheetType,\n          setSelectedSheetType: setSelectedSheetType\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.9,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              className: \"text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-[#0077B6]/10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0077B6] mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-12 w-12 mx-auto\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-bold text-gray-900 mb-2\",\n                children: \"Patient Not Found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: error || 'Patient not found'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), error && error.includes('not found') && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-semibold text-blue-800 mb-2\",\n                  children: \"Available Test Patients:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"text-sm text-blue-700 space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: [\"\\u2022 National ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                      className: \"bg-blue-100 px-1 rounded\",\n                      children: \"12345678901234\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 42\n                    }, this), \" - Mohamed Ahmed Ali\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: [\"\\u2022 National ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                      className: \"bg-blue-100 px-1 rounded\",\n                      children: \"987654321\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 42\n                    }, this), \" - Sarah Lee\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: [\"\\u2022 National ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                      className: \"bg-blue-100 px-1 rounded\",\n                      children: \"123456789\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 42\n                    }, this), \" - Michael Johnson\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: [\"\\u2022 National ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                      className: \"bg-blue-100 px-1 rounded\",\n                      children: \"0122\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 42\n                    }, this), \" - sousannah\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => window.location.reload(),\n                className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md\",\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PatientNav, {\n        selectedSheetType: selectedSheetType,\n        setSelectedSheetType: setSelectedSheetType\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Patient Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: patientData.fullName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/student/dashboard\",\n                className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-4 md:px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-5 w-5 mr-2\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), \"Back to Dashboard\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              className: \"mb-6 p-4 bg-red-100 text-[#333333] rounded-lg\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), isEditing ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.9,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              className: \"bg-white rounded-xl p-6 shadow-sm border border-[#0077B6]/10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-[#0077B6]\",\n                  children: \"Edit Patient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleEditToggle,\n                  className: \"text-gray-500 hover:text-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M6 18L18 6M6 6l12 12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-[#0077B6] mb-4 border-b pb-2\",\n                  children: \"Personal Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                      children: \"Full Name*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: \"fullName\",\n                      value: formData.fullName,\n                      onChange: handleInputChange,\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                      children: \"National ID*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: \"nationalId\",\n                      value: formData.nationalId,\n                      onChange: handleInputChange,\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true,\n                      disabled: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                      children: \"Phone Number*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"tel\",\n                      name: \"phoneNumber\",\n                      value: formData.phoneNumber,\n                      onChange: handleInputChange,\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                      children: \"Gender*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      name: \"gender\",\n                      value: formData.gender,\n                      onChange: handleInputChange,\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 399,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"male\",\n                        children: \"Male\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 400,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"female\",\n                        children: \"Female\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 401,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"other\",\n                        children: \"Other\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 402,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                      children: \"Age*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      name: \"age\",\n                      value: formData.age,\n                      onChange: handleInputChange,\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                      children: \"Address*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: \"address\",\n                      value: formData.address,\n                      onChange: handleInputChange,\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                      children: \"Occupation*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: \"occupation\",\n                      value: formData.occupation,\n                      onChange: handleInputChange,\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b pb-2\",\n                  children: \"Medical History\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Chronic Diseases\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 sm:grid-cols-3 gap-3 bg-gray-50 p-4 rounded-lg border border-gray-200\",\n                    children: chronicDiseases.map(disease => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"relative flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"checkbox\",\n                          id: `disease-${disease}`,\n                          checked: formData.medicalInfo.chronicDiseases.includes(disease),\n                          onChange: () => handleChronicDiseaseChange(disease),\n                          className: \"h-5 w-5 text-[#0077B6] focus:ring-2 focus:ring-[#0077B6] focus:ring-offset-2 border-2 border-gray-300 rounded cursor-pointer\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 454,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                          htmlFor: `disease-${disease}`,\n                          className: \"ml-2 text-sm font-medium text-gray-700 cursor-pointer hover:text-[#0077B6] transition-colors\",\n                          children: disease\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 461,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 29\n                      }, this)\n                    }, disease, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Recent Surgical Procedures (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      name: \"recentSurgicalProcedures\",\n                      value: formData.medicalInfo.recentSurgicalProcedures,\n                      onChange: handleMedicalInfoChange,\n                      rows: \"2\",\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 477,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Current Medications (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      name: \"currentMedications\",\n                      value: formData.medicalInfo.currentMedications,\n                      onChange: handleMedicalInfoChange,\n                      rows: \"2\",\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b pb-2\",\n                  children: \"Chief Complaint\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"chiefComplaint\",\n                    value: formData.medicalInfo.chiefComplaint,\n                    onChange: handleInputChange,\n                    rows: \"3\",\n                    placeholder: \"Describe the patient's main complaint or reason for visit\",\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-end space-x-4 pt-4\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                    type: \"button\",\n                    onClick: handleEditToggle,\n                    className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                    type: \"submit\",\n                    className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    children: \"Update Patient\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              animate: \"show\",\n              className: \"bg-white rounded-xl shadow-sm p-6 border border-[#0077B6]/10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-end mb-6\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: handleEditToggle,\n                  className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg\",\n                  children: \"Edit Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 25\n                    }, this), \"Personal Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Full Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 557,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: patientData.fullName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 558,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"National ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 561,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: patientData.nationalId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 562,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Phone Number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 565,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: patientData.phoneNumber\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 566,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Gender\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 569,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: patientData.gender\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 570,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Age\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 573,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: patientData.age\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 574,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 577,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: patientData.address || 'Not provided'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 578,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Occupation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 581,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: patientData.occupation || 'Not provided'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 582,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaFileMedical, {\n                      className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 25\n                    }, this), \"Medical History\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Chronic Diseases\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 593,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: ((_patientData$medicalI = patientData.medicalInfo) === null || _patientData$medicalI === void 0 ? void 0 : (_patientData$medicalI2 = _patientData$medicalI.chronicDiseases) === null || _patientData$medicalI2 === void 0 ? void 0 : _patientData$medicalI2.length) > 0 ? patientData.medicalInfo.chronicDiseases.join(', ') : 'None'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 594,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Recent Surgical Procedures\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: ((_patientData$medicalI3 = patientData.medicalInfo) === null || _patientData$medicalI3 === void 0 ? void 0 : _patientData$medicalI3.recentSurgicalProcedures) || 'None'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 602,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Current Medications\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 607,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-900\",\n                        children: ((_patientData$medicalI4 = patientData.medicalInfo) === null || _patientData$medicalI4 === void 0 ? void 0 : _patientData$medicalI4.currentMedications) || 'None'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 606,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaProcedures, {\n                      className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 25\n                    }, this), \"Chief Complaint\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900\",\n                    children: ((_patientData$medicalI5 = patientData.medicalInfo) === null || _patientData$medicalI5 === void 0 ? void 0 : _patientData$medicalI5.chiefComplaint) || 'None'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientProfile, \"oEfeEjqutgi3hccjQ+hJPsOoKvg=\", false, function () {\n  return [useAuth, useParams];\n});\n_c = PatientProfile;\nexport default PatientProfile;\nvar _c;\n$RefreshReg$(_c, \"PatientProfile\");", "map": {"version": 3, "names": ["useState", "useEffect", "Link", "useParams", "axios", "<PERSON><PERSON><PERSON>", "Sidebar", "PatientNav", "useAuth", "motion", "FaUser", "FaFileMedical", "FaProcedures", "jsxDEV", "_jsxDEV", "chronicDiseases", "PatientProfile", "_s", "_patientData$medicalI", "_patientData$medicalI2", "_patientData$medicalI3", "_patientData$medicalI4", "_patientData$medicalI5", "user", "token", "nationalId", "sidebarOpen", "setSidebarOpen", "patientData", "setPatientData", "loading", "setLoading", "error", "setError", "isEditing", "setIsEditing", "selectedSheetType", "setSelectedSheetType", "formData", "setFormData", "fullName", "phoneNumber", "gender", "age", "address", "occupation", "medicalInfo", "recentSurgicalProcedures", "currentMedications", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchPatientData", "_patient$medicalInfo", "_patient$medicalInfo2", "_patient$medicalInfo3", "_patient$medicalInfo4", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "response", "get", "patient", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "console", "status", "message", "_err$response5", "_err$response5$data", "handleInputChange", "e", "name", "value", "target", "prev", "handleMedicalInfoChange", "handleChronicDiseaseChange", "disease", "diseases", "includes", "filter", "d", "handleSubmit", "preventDefault", "drId", "id", "put", "headers", "Authorization", "Error", "_err$response6", "_err$response7", "_err$response8", "_err$response8$data", "handleEditToggle", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "className", "children", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "div", "initial", "animate", "scale", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "button", "whileHover", "whileTap", "onClick", "window", "location", "reload", "duration", "to", "onSubmit", "type", "onChange", "required", "disabled", "map", "checked", "htmlFor", "rows", "placeholder", "variants", "length", "join", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/PatientProfile.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { Link, useParams } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport Navbar from './Navbar';\r\nimport Sidebar from './Sidebar';\r\nimport PatientNav from './PatientNav';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport { motion } from 'framer-motion';\r\nimport { FaUser, FaFileMedical, FaProcedures } from 'react-icons/fa';\r\n\r\nconst chronicDiseases = [\r\n  'Diabetes Mellitus',\r\n  'Hypertension',\r\n  'Cardiovascular Disease',\r\n  'Coagulopathy / Bleeding Disorders',\r\n  'Thyroid Disorders',\r\n  'Pregnancy',\r\n  'Lactation',\r\n  'Hepatic Diseases',\r\n  'Renal Diseases'\r\n];\r\n\r\nconst PatientProfile = () => {\r\n  const { user, token } = useAuth();\r\n  const { nationalId } = useParams();\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [patientData, setPatientData] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [selectedSheetType, setSelectedSheetType] = useState('');\r\n\r\n  const [formData, setFormData] = useState({\r\n    nationalId: '',\r\n    fullName: '',\r\n    phoneNumber: '',\r\n    gender: '',\r\n    age: '',\r\n    address: '',\r\n    occupation: '',\r\n    medicalInfo: {\r\n      chronicDiseases: [],\r\n      recentSurgicalProcedures: '',\r\n      currentMedications: '',\r\n      chiefComplaint: '',\r\n    },\r\n  });\r\n\r\n  useEffect(() => {\r\n    const fetchPatientData = async () => {\r\n      if (!token) {\r\n        setError('Please log in to view patient data.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      try {\r\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n        const apiUrl = `${baseUrl}/api/patients/public/${nationalId}`;\r\n        const response = await axios.get(apiUrl);\r\n        const patient = response.data;\r\n        setPatientData(patient);\r\n        setFormData({\r\n          nationalId: patient.nationalId,\r\n          fullName: patient.fullName,\r\n          phoneNumber: patient.phoneNumber,\r\n          gender: patient.gender,\r\n          age: patient.age,\r\n          address: patient.address || '',\r\n          occupation: patient.occupation || '',\r\n          medicalInfo: {\r\n            chronicDiseases: patient.medicalInfo?.chronicDiseases || [],\r\n            recentSurgicalProcedures: patient.medicalInfo?.recentSurgicalProcedures || '',\r\n            currentMedications: patient.medicalInfo?.currentMedications || '',\r\n            chiefComplaint: patient.medicalInfo?.chiefComplaint || '',\r\n          },\r\n        });\r\n      } catch (err) {\r\n        console.error('Fetch error:', err.response?.status, err.response?.data);\r\n        let message;\r\n        if (err.response?.status === 401) {\r\n          message = 'Unauthorized: Please log in again.';\r\n        } else if (err.response?.status === 404) {\r\n          message = `Patient with National ID \"${nationalId}\" not found. Please check the National ID and try again.`;\r\n        } else {\r\n          message = err.response?.data?.message || 'Failed to load patient data';\r\n        }\r\n        setError(message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchPatientData();\r\n  }, [nationalId, token]);\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === 'chiefComplaint') {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        medicalInfo: { ...prev.medicalInfo, chiefComplaint: value },\r\n      }));\r\n    } else {\r\n      setFormData(prev => ({ ...prev, [name]: value }));\r\n    }\r\n  };\r\n\r\n  const handleMedicalInfoChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      medicalInfo: { ...prev.medicalInfo, [name]: value },\r\n    }));\r\n  };\r\n\r\n  const handleChronicDiseaseChange = (disease) => {\r\n    setFormData(prev => {\r\n      const diseases = prev.medicalInfo.chronicDiseases.includes(disease)\r\n        ? prev.medicalInfo.chronicDiseases.filter(d => d !== disease)\r\n        : [...prev.medicalInfo.chronicDiseases, disease];\r\n      return {\r\n        ...prev,\r\n        medicalInfo: { ...prev.medicalInfo, chronicDiseases: diseases },\r\n      };\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const patientData = {\r\n        ...formData,\r\n        drId: user.id, // Include drId as required by patientSchema\r\n      };\r\n      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      const response = await axios.put(\r\n        `${baseUrl}/api/patients/${nationalId}`,\r\n        patientData,\r\n        { headers: { Authorization: `Bearer ${token}` } }\r\n      );\r\n      if (response.data && response.data.patient) {\r\n        setPatientData(response.data.patient);\r\n        setIsEditing(false);\r\n      } else {\r\n        throw new Error('Invalid patient data received from server');\r\n      }\r\n    } catch (err) {\r\n      console.error('Update error:', err.response?.status, err.response?.data);\r\n      setError(err.response?.data?.message || 'Failed to update patient data');\r\n    }\r\n  };\r\n\r\n  const handleEditToggle = () => {\r\n    setIsEditing(!isEditing);\r\n    setError(''); // Clear any previous errors when toggling edit mode\r\n  };\r\n\r\n  // Animation variants\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <PatientNav\r\n            selectedSheetType={selectedSheetType}\r\n            setSelectedSheetType={setSelectedSheetType}\r\n          />\r\n          <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\r\n            <div className=\"max-w-7xl mx-auto\">\r\n              <motion.div\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                className=\"text-center\"\r\n              >\r\n                Loading patient data...\r\n              </motion.div>\r\n            </div>\r\n          </main>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !patientData) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <PatientNav\r\n            selectedSheetType={selectedSheetType}\r\n            setSelectedSheetType={setSelectedSheetType}\r\n          />\r\n          <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\r\n            <div className=\"max-w-7xl mx-auto\">\r\n              <motion.div\r\n                initial={{ scale: 0.9, opacity: 0 }}\r\n                animate={{ scale: 1, opacity: 1 }}\r\n                className=\"text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-[#0077B6]/10\"\r\n              >\r\n                <div className=\"text-[#0077B6] mb-4\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"h-12 w-12 mx-auto\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                <h3 className=\"text-lg font-bold text-gray-900 mb-2\">Patient Not Found</h3>\r\n                <p className=\"text-gray-600 mb-4\">{error || 'Patient not found'}</p>\r\n                {error && error.includes('not found') && (\r\n                  <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\">\r\n                    <h4 className=\"text-sm font-semibold text-blue-800 mb-2\">Available Test Patients:</h4>\r\n                    <ul className=\"text-sm text-blue-700 space-y-1\">\r\n                      <li>• National ID: <code className=\"bg-blue-100 px-1 rounded\">12345678901234</code> - Mohamed Ahmed Ali</li>\r\n                      <li>• National ID: <code className=\"bg-blue-100 px-1 rounded\">987654321</code> - Sarah Lee</li>\r\n                      <li>• National ID: <code className=\"bg-blue-100 px-1 rounded\">123456789</code> - Michael Johnson</li>\r\n                      <li>• National ID: <code className=\"bg-blue-100 px-1 rounded\">0122</code> - sousannah</li>\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n                <motion.button\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={() => window.location.reload()}\r\n                  className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md\"\r\n                >\r\n                  Try Again\r\n                </motion.button>\r\n              </motion.div>\r\n            </div>\r\n          </main>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n        <PatientNav\r\n          selectedSheetType={selectedSheetType}\r\n          setSelectedSheetType={setSelectedSheetType}\r\n        />\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Patient Profile\r\n                  </h1>\r\n                  <p className=\"text-gray-600\">{patientData.fullName}</p>\r\n                </div>\r\n                <Link\r\n                  to=\"/student/dashboard\"\r\n                  className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-4 md:px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\r\n                >\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"h-5 w-5 mr-2\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"\r\n                    />\r\n                  </svg>\r\n                  Back to Dashboard\r\n                </Link>\r\n              </div>\r\n\r\n              {error && (\r\n                <motion.div\r\n                  initial={{ opacity: 0 }}\r\n                  animate={{ opacity: 1 }}\r\n                  className=\"mb-6 p-4 bg-red-100 text-[#333333] rounded-lg\"\r\n                >\r\n                  {error}\r\n                </motion.div>\r\n              )}\r\n\r\n              {isEditing ? (\r\n                <motion.div\r\n                  initial={{ scale: 0.9, opacity: 0 }}\r\n                  animate={{ scale: 1, opacity: 1 }}\r\n                  className=\"bg-white rounded-xl p-6 shadow-sm border border-[#0077B6]/10\"\r\n                >\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                    <h2 className=\"text-2xl font-bold text-[#0077B6]\">Edit Patient</h2>\r\n                    <button\r\n                      onClick={handleEditToggle}\r\n                      className=\"text-gray-500 hover:text-gray-700\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        className=\"h-6 w-6\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        stroke=\"currentColor\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M6 18L18 6M6 6l12 12\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                  <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                    <h3 className=\"text-xl font-semibold text-[#0077B6] mb-4 border-b pb-2\">Personal Information</h3>\r\n                    <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n                          Full Name*\r\n                        </label>\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"fullName\"\r\n                          value={formData.fullName}\r\n                          onChange={handleInputChange}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n                          National ID*\r\n                        </label>\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"nationalId\"\r\n                          value={formData.nationalId}\r\n                          onChange={handleInputChange}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                          disabled\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n                          Phone Number*\r\n                        </label>\r\n                        <input\r\n                          type=\"tel\"\r\n                          name=\"phoneNumber\"\r\n                          value={formData.phoneNumber}\r\n                          onChange={handleInputChange}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n                          Gender*\r\n                        </label>\r\n                        <select\r\n                          name=\"gender\"\r\n                          value={formData.gender}\r\n                          onChange={handleInputChange}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                        >\r\n                          <option value=\"\">Select</option>\r\n                          <option value=\"male\">Male</option>\r\n                          <option value=\"female\">Female</option>\r\n                          <option value=\"other\">Other</option>\r\n                        </select>\r\n                      </div>\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n                          Age*\r\n                        </label>\r\n                        <input\r\n                          type=\"number\"\r\n                          name=\"age\"\r\n                          value={formData.age}\r\n                          onChange={handleInputChange}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n                          Address*\r\n                        </label>\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"address\"\r\n                          value={formData.address}\r\n                          onChange={handleInputChange}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n                          Occupation*\r\n                        </label>\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"occupation\"\r\n                          value={formData.occupation}\r\n                          onChange={handleInputChange}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                    <h3 className=\"text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b pb-2\">Medical History</h3>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Chronic Diseases\r\n                      </label>\r\n                      <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-3 bg-gray-50 p-4 rounded-lg border border-gray-200\">\r\n                        {chronicDiseases.map((disease) => (\r\n                          <div key={disease} className=\"flex items-center\">\r\n                            <div className=\"relative flex items-center\">\r\n                              <input\r\n                                type=\"checkbox\"\r\n                                id={`disease-${disease}`}\r\n                                checked={formData.medicalInfo.chronicDiseases.includes(disease)}\r\n                                onChange={() => handleChronicDiseaseChange(disease)}\r\n                                className=\"h-5 w-5 text-[#0077B6] focus:ring-2 focus:ring-[#0077B6] focus:ring-offset-2 border-2 border-gray-300 rounded cursor-pointer\"\r\n                              />\r\n                              <label\r\n                                htmlFor={`disease-${disease}`}\r\n                                className=\"ml-2 text-sm font-medium text-gray-700 cursor-pointer hover:text-[#0077B6] transition-colors\"\r\n                              >\r\n                                {disease}\r\n                              </label>\r\n                            </div>\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                          Recent Surgical Procedures (Optional)\r\n                        </label>\r\n                        <textarea\r\n                          name=\"recentSurgicalProcedures\"\r\n                          value={formData.medicalInfo.recentSurgicalProcedures}\r\n                          onChange={handleMedicalInfoChange}\r\n                          rows=\"2\"\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                          Current Medications (Optional)\r\n                        </label>\r\n                        <textarea\r\n                          name=\"currentMedications\"\r\n                          value={formData.medicalInfo.currentMedications}\r\n                          onChange={handleMedicalInfoChange}\r\n                          rows=\"2\"\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n\r\n                    <h3 className=\"text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b pb-2\">Chief Complaint</h3>\r\n                    <div>\r\n                      <textarea\r\n                        name=\"chiefComplaint\"\r\n                        value={formData.medicalInfo.chiefComplaint}\r\n                        onChange={handleInputChange}\r\n                        rows=\"3\"\r\n                        placeholder=\"Describe the patient's main complaint or reason for visit\"\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-end space-x-4 pt-4\">\r\n                      <motion.button\r\n                        type=\"button\"\r\n                        onClick={handleEditToggle}\r\n                        className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors\"\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        Cancel\r\n                      </motion.button>\r\n                      <motion.button\r\n                        type=\"submit\"\r\n                        className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg\"\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        Update Patient\r\n                      </motion.button>\r\n                    </div>\r\n                  </form>\r\n                </motion.div>\r\n              ) : (\r\n                <motion.div\r\n                  variants={container}\r\n                  initial=\"hidden\"\r\n                  animate=\"show\"\r\n                  className=\"bg-white rounded-xl shadow-sm p-6 border border-[#0077B6]/10\"\r\n                >\r\n                  <div className=\"flex justify-end mb-6\">\r\n                    <motion.button\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                      onClick={handleEditToggle}\r\n                      className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg\"\r\n                    >\r\n                      Edit Profile\r\n                    </motion.button>\r\n                  </div>\r\n                  <motion.div variants={item} className=\"space-y-6\">\r\n                    <div className=\"bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10\">\r\n                      <h2 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\r\n                        <FaUser className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\r\n                        Personal Information\r\n                      </h2>\r\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Full Name</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">{patientData.fullName}</p>\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">National ID</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">{patientData.nationalId}</p>\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Phone Number</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">{patientData.phoneNumber}</p>\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Gender</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">{patientData.gender}</p>\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Age</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">{patientData.age}</p>\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Address</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">{patientData.address || 'Not provided'}</p>\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Occupation</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">{patientData.occupation || 'Not provided'}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10\">\r\n                      <h2 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\r\n                        <FaFileMedical className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\r\n                        Medical History\r\n                      </h2>\r\n                      <div className=\"space-y-4\">\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Chronic Diseases</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">\r\n                            {patientData.medicalInfo?.chronicDiseases?.length > 0\r\n                              ? patientData.medicalInfo.chronicDiseases.join(', ')\r\n                              : 'None'}\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Recent Surgical Procedures</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">\r\n                            {patientData.medicalInfo?.recentSurgicalProcedures || 'None'}\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Current Medications</h3>\r\n                          <p className=\"mt-1 text-sm text-gray-900\">\r\n                            {patientData.medicalInfo?.currentMedications || 'None'}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10\">\r\n                      <h2 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\r\n                        <FaProcedures className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\r\n                        Chief Complaint\r\n                      </h2>\r\n                      <p className=\"text-sm text-gray-900\">\r\n                        {patientData.medicalInfo?.chiefComplaint || 'None'}\r\n                      </p>\r\n                    </div>\r\n                  </motion.div>\r\n                </motion.div>\r\n              )}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PatientProfile;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,SAAS,QAAQ,kBAAkB;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,eAAe,GAAG,CACtB,mBAAmB,EACnB,cAAc,EACd,wBAAwB,EACxB,mCAAmC,EACnC,mBAAmB,EACnB,WAAW,EACX,WAAW,EACX,kBAAkB,EAClB,gBAAgB,CACjB;AAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC3B,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGhB,OAAO,CAAC,CAAC;EACjC,MAAM;IAAEiB;EAAW,CAAC,GAAGtB,SAAS,CAAC,CAAC;EAClC,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC;IACvCyB,UAAU,EAAE,EAAE;IACde,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE,EAAE;IACPC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE;MACX/B,eAAe,EAAE,EAAE;MACnBgC,wBAAwB,EAAE,EAAE;MAC5BC,kBAAkB,EAAE,EAAE;MACtBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFhD,SAAS,CAAC,MAAM;IACd,MAAMiD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI,CAAC1B,KAAK,EAAE;QACVS,QAAQ,CAAC,qCAAqC,CAAC;QAC/CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACA,IAAI;QAAA,IAAAoB,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QACF,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACxE,MAAMC,MAAM,GAAG,GAAGJ,OAAO,wBAAwB9B,UAAU,EAAE;QAC7D,MAAMmC,QAAQ,GAAG,MAAMxD,KAAK,CAACyD,GAAG,CAACF,MAAM,CAAC;QACxC,MAAMG,OAAO,GAAGF,QAAQ,CAACG,IAAI;QAC7BlC,cAAc,CAACiC,OAAO,CAAC;QACvBvB,WAAW,CAAC;UACVd,UAAU,EAAEqC,OAAO,CAACrC,UAAU;UAC9Be,QAAQ,EAAEsB,OAAO,CAACtB,QAAQ;UAC1BC,WAAW,EAAEqB,OAAO,CAACrB,WAAW;UAChCC,MAAM,EAAEoB,OAAO,CAACpB,MAAM;UACtBC,GAAG,EAAEmB,OAAO,CAACnB,GAAG;UAChBC,OAAO,EAAEkB,OAAO,CAAClB,OAAO,IAAI,EAAE;UAC9BC,UAAU,EAAEiB,OAAO,CAACjB,UAAU,IAAI,EAAE;UACpCC,WAAW,EAAE;YACX/B,eAAe,EAAE,EAAAoC,oBAAA,GAAAW,OAAO,CAAChB,WAAW,cAAAK,oBAAA,uBAAnBA,oBAAA,CAAqBpC,eAAe,KAAI,EAAE;YAC3DgC,wBAAwB,EAAE,EAAAK,qBAAA,GAAAU,OAAO,CAAChB,WAAW,cAAAM,qBAAA,uBAAnBA,qBAAA,CAAqBL,wBAAwB,KAAI,EAAE;YAC7EC,kBAAkB,EAAE,EAAAK,qBAAA,GAAAS,OAAO,CAAChB,WAAW,cAAAO,qBAAA,uBAAnBA,qBAAA,CAAqBL,kBAAkB,KAAI,EAAE;YACjEC,cAAc,EAAE,EAAAK,qBAAA,GAAAQ,OAAO,CAAChB,WAAW,cAAAQ,qBAAA,uBAAnBA,qBAAA,CAAqBL,cAAc,KAAI;UACzD;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOe,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;QACZC,OAAO,CAACrC,KAAK,CAAC,cAAc,GAAAiC,aAAA,GAAED,GAAG,CAACJ,QAAQ,cAAAK,aAAA,uBAAZA,aAAA,CAAcK,MAAM,GAAAJ,cAAA,GAAEF,GAAG,CAACJ,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcH,IAAI,CAAC;QACvE,IAAIQ,OAAO;QACX,IAAI,EAAAJ,cAAA,GAAAH,GAAG,CAACJ,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcG,MAAM,MAAK,GAAG,EAAE;UAChCC,OAAO,GAAG,oCAAoC;QAChD,CAAC,MAAM,IAAI,EAAAH,cAAA,GAAAJ,GAAG,CAACJ,QAAQ,cAAAQ,cAAA,uBAAZA,cAAA,CAAcE,MAAM,MAAK,GAAG,EAAE;UACvCC,OAAO,GAAG,6BAA6B9C,UAAU,0DAA0D;QAC7G,CAAC,MAAM;UAAA,IAAA+C,cAAA,EAAAC,mBAAA;UACLF,OAAO,GAAG,EAAAC,cAAA,GAAAR,GAAG,CAACJ,QAAQ,cAAAY,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcT,IAAI,cAAAU,mBAAA,uBAAlBA,mBAAA,CAAoBF,OAAO,KAAI,6BAA6B;QACxE;QACAtC,QAAQ,CAACsC,OAAO,CAAC;MACnB,CAAC,SAAS;QACRxC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDmB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACzB,UAAU,EAAED,KAAK,CAAC,CAAC;EAEvB,MAAMkD,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC,IAAIF,IAAI,KAAK,gBAAgB,EAAE;MAC7BrC,WAAW,CAACwC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPjC,WAAW,EAAE;UAAE,GAAGiC,IAAI,CAACjC,WAAW;UAAEG,cAAc,EAAE4B;QAAM;MAC5D,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLtC,WAAW,CAACwC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAGC;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMG,uBAAuB,GAAIL,CAAC,IAAK;IACrC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCvC,WAAW,CAACwC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPjC,WAAW,EAAE;QAAE,GAAGiC,IAAI,CAACjC,WAAW;QAAE,CAAC8B,IAAI,GAAGC;MAAM;IACpD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,0BAA0B,GAAIC,OAAO,IAAK;IAC9C3C,WAAW,CAACwC,IAAI,IAAI;MAClB,MAAMI,QAAQ,GAAGJ,IAAI,CAACjC,WAAW,CAAC/B,eAAe,CAACqE,QAAQ,CAACF,OAAO,CAAC,GAC/DH,IAAI,CAACjC,WAAW,CAAC/B,eAAe,CAACsE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC,GAC3D,CAAC,GAAGH,IAAI,CAACjC,WAAW,CAAC/B,eAAe,EAAEmE,OAAO,CAAC;MAClD,OAAO;QACL,GAAGH,IAAI;QACPjC,WAAW,EAAE;UAAE,GAAGiC,IAAI,CAACjC,WAAW;UAAE/B,eAAe,EAAEoE;QAAS;MAChE,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAM5D,WAAW,GAAG;QAClB,GAAGU,QAAQ;QACXmD,IAAI,EAAElE,IAAI,CAACmE,EAAE,CAAE;MACjB,CAAC;MACD,MAAMnC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACxE,MAAME,QAAQ,GAAG,MAAMxD,KAAK,CAACuF,GAAG,CAC9B,GAAGpC,OAAO,iBAAiB9B,UAAU,EAAE,EACvCG,WAAW,EACX;QAAEgE,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUrE,KAAK;QAAG;MAAE,CAClD,CAAC;MACD,IAAIoC,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACD,OAAO,EAAE;QAC1CjC,cAAc,CAAC+B,QAAQ,CAACG,IAAI,CAACD,OAAO,CAAC;QACrC3B,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,MAAM;QACL,MAAM,IAAI2D,KAAK,CAAC,2CAA2C,CAAC;MAC9D;IACF,CAAC,CAAC,OAAO9B,GAAG,EAAE;MAAA,IAAA+B,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ7B,OAAO,CAACrC,KAAK,CAAC,eAAe,GAAA+D,cAAA,GAAE/B,GAAG,CAACJ,QAAQ,cAAAmC,cAAA,uBAAZA,cAAA,CAAczB,MAAM,GAAA0B,cAAA,GAAEhC,GAAG,CAACJ,QAAQ,cAAAoC,cAAA,uBAAZA,cAAA,CAAcjC,IAAI,CAAC;MACxE9B,QAAQ,CAAC,EAAAgE,cAAA,GAAAjC,GAAG,CAACJ,QAAQ,cAAAqC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAclC,IAAI,cAAAmC,mBAAA,uBAAlBA,mBAAA,CAAoB3B,OAAO,KAAI,+BAA+B,CAAC;IAC1E;EACF,CAAC;EAED,MAAM4B,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhE,YAAY,CAAC,CAACD,SAAS,CAAC;IACxBD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMmE,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACJD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI7E,OAAO,EAAE;IACX,oBACEhB,OAAA;MAAK8F,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC/F,OAAA,CAACR,OAAO;QAACwG,MAAM,EAAEpF,WAAY;QAACqF,SAAS,EAAEpF;MAAe;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DrG,OAAA;QAAK8F,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD/F,OAAA,CAACT,MAAM;UAAC+G,aAAa,EAAEA,CAAA,KAAMzF,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DrG,OAAA,CAACP,UAAU;UACT6B,iBAAiB,EAAEA,iBAAkB;UACrCC,oBAAoB,EAAEA;QAAqB;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACFrG,OAAA;UAAM8F,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC7F/F,OAAA;YAAK8F,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC/F,OAAA,CAACL,MAAM,CAAC4G,GAAG;cACTC,OAAO,EAAE;gBAAEhB,OAAO,EAAE;cAAE,CAAE;cACxBiB,OAAO,EAAE;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cACxBM,SAAS,EAAC,aAAa;cAAAC,QAAA,EACxB;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAInF,KAAK,IAAI,CAACJ,WAAW,EAAE;IACzB,oBACEd,OAAA;MAAK8F,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC/F,OAAA,CAACR,OAAO;QAACwG,MAAM,EAAEpF,WAAY;QAACqF,SAAS,EAAEpF;MAAe;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DrG,OAAA;QAAK8F,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD/F,OAAA,CAACT,MAAM;UAAC+G,aAAa,EAAEA,CAAA,KAAMzF,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DrG,OAAA,CAACP,UAAU;UACT6B,iBAAiB,EAAEA,iBAAkB;UACrCC,oBAAoB,EAAEA;QAAqB;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACFrG,OAAA;UAAM8F,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC7F/F,OAAA;YAAK8F,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC/F,OAAA,CAACL,MAAM,CAAC4G,GAAG;cACTC,OAAO,EAAE;gBAAEE,KAAK,EAAE,GAAG;gBAAElB,OAAO,EAAE;cAAE,CAAE;cACpCiB,OAAO,EAAE;gBAAEC,KAAK,EAAE,CAAC;gBAAElB,OAAO,EAAE;cAAE,CAAE;cAClCM,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAE7F/F,OAAA;gBAAK8F,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eAClC/F,OAAA;kBACE2G,KAAK,EAAC,4BAA4B;kBAClCb,SAAS,EAAC,mBAAmB;kBAC7Bc,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnBC,MAAM,EAAC,cAAc;kBAAAf,QAAA,eAErB/F,OAAA;oBACE+G,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfzC,CAAC,EAAC;kBAAsI;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrG,OAAA;gBAAI8F,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ErG,OAAA;gBAAG8F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE7E,KAAK,IAAI;cAAmB;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACnEnF,KAAK,IAAIA,KAAK,CAACoD,QAAQ,CAAC,WAAW,CAAC,iBACnCtE,OAAA;gBAAK8F,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACpE/F,OAAA;kBAAI8F,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtFrG,OAAA;kBAAI8F,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7C/F,OAAA;oBAAA+F,QAAA,GAAI,sBAAe,eAAA/F,OAAA;sBAAM8F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,wBAAoB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5GrG,OAAA;oBAAA+F,QAAA,GAAI,sBAAe,eAAA/F,OAAA;sBAAM8F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAAY;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/FrG,OAAA;oBAAA+F,QAAA,GAAI,sBAAe,eAAA/F,OAAA;sBAAM8F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,sBAAkB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrGrG,OAAA;oBAAA+F,QAAA,GAAI,sBAAe,eAAA/F,OAAA;sBAAM8F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAAY;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN,eACDrG,OAAA,CAACL,MAAM,CAACuH,MAAM;gBACZC,UAAU,EAAE;kBAAET,KAAK,EAAE;gBAAK,CAAE;gBAC5BU,QAAQ,EAAE;kBAAEV,KAAK,EAAE;gBAAK,CAAE;gBAC1BW,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;gBACxC1B,SAAS,EAAC,oJAAoJ;gBAAAC,QAAA,EAC/J;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErG,OAAA;IAAK8F,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC/F,OAAA,CAACR,OAAO;MAACwG,MAAM,EAAEpF,WAAY;MAACqF,SAAS,EAAEpF;IAAe;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3DrG,OAAA;MAAK8F,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD/F,OAAA,CAACT,MAAM;QAAC+G,aAAa,EAAEA,CAAA,KAAMzF,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DrG,OAAA,CAACP,UAAU;QACT6B,iBAAiB,EAAEA,iBAAkB;QACrCC,oBAAoB,EAAEA;MAAqB;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACFrG,OAAA;QAAM8F,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC7F/F,OAAA;UAAK8F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC/F,OAAA,CAACL,MAAM,CAAC4G,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAE+B,QAAQ,EAAE;YAAI,CAAE;YAAA1B,QAAA,gBAE9B/F,OAAA;cAAK8F,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/F/F,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBAAI8F,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLrG,OAAA;kBAAG8F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEjF,WAAW,CAACY;gBAAQ;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNrG,OAAA,CAACZ,IAAI;gBACHsI,EAAE,EAAC,oBAAoB;gBACvB5B,SAAS,EAAC,4PAA4P;gBAAAC,QAAA,gBAEtQ/F,OAAA;kBACE2G,KAAK,EAAC,4BAA4B;kBAClCb,SAAS,EAAC,cAAc;kBACxBc,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnBC,MAAM,EAAC,cAAc;kBAAAf,QAAA,eAErB/F,OAAA;oBACE+G,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfzC,CAAC,EAAC;kBAA6B;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,qBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELnF,KAAK,iBACJlB,OAAA,CAACL,MAAM,CAAC4G,GAAG;cACTC,OAAO,EAAE;gBAAEhB,OAAO,EAAE;cAAE,CAAE;cACxBiB,OAAO,EAAE;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cACxBM,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAExD7E;YAAK;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAEAjF,SAAS,gBACRpB,OAAA,CAACL,MAAM,CAAC4G,GAAG;cACTC,OAAO,EAAE;gBAAEE,KAAK,EAAE,GAAG;gBAAElB,OAAO,EAAE;cAAE,CAAE;cACpCiB,OAAO,EAAE;gBAAEC,KAAK,EAAE,CAAC;gBAAElB,OAAO,EAAE;cAAE,CAAE;cAClCM,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAExE/F,OAAA;gBAAK8F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD/F,OAAA;kBAAI8F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnErG,OAAA;kBACEqH,OAAO,EAAEhC,gBAAiB;kBAC1BS,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,eAE7C/F,OAAA;oBACE2G,KAAK,EAAC,4BAA4B;oBAClCb,SAAS,EAAC,SAAS;oBACnBc,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAf,QAAA,eAErB/F,OAAA;sBACE+G,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfzC,CAAC,EAAC;oBAAsB;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNrG,OAAA;gBAAM2H,QAAQ,EAAElD,YAAa;gBAACqB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACjD/F,OAAA;kBAAI8F,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjGrG,OAAA;kBAAK8F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD/F,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAElE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE4H,IAAI,EAAC,MAAM;sBACX9D,IAAI,EAAC,UAAU;sBACfC,KAAK,EAAEvC,QAAQ,CAACE,QAAS;sBACzBmG,QAAQ,EAAEjE,iBAAkB;sBAC5BkC,SAAS,EAAC,6GAA6G;sBACvHgC,QAAQ;oBAAA;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrG,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAElE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE4H,IAAI,EAAC,MAAM;sBACX9D,IAAI,EAAC,YAAY;sBACjBC,KAAK,EAAEvC,QAAQ,CAACb,UAAW;sBAC3BkH,QAAQ,EAAEjE,iBAAkB;sBAC5BkC,SAAS,EAAC,6GAA6G;sBACvHgC,QAAQ;sBACRC,QAAQ;oBAAA;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrG,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAElE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE4H,IAAI,EAAC,KAAK;sBACV9D,IAAI,EAAC,aAAa;sBAClBC,KAAK,EAAEvC,QAAQ,CAACG,WAAY;sBAC5BkG,QAAQ,EAAEjE,iBAAkB;sBAC5BkC,SAAS,EAAC,6GAA6G;sBACvHgC,QAAQ;oBAAA;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrG,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAElE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE8D,IAAI,EAAC,QAAQ;sBACbC,KAAK,EAAEvC,QAAQ,CAACI,MAAO;sBACvBiG,QAAQ,EAAEjE,iBAAkB;sBAC5BkC,SAAS,EAAC,6GAA6G;sBACvHgC,QAAQ;sBAAA/B,QAAA,gBAER/F,OAAA;wBAAQ+D,KAAK,EAAC,EAAE;wBAAAgC,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAChCrG,OAAA;wBAAQ+D,KAAK,EAAC,MAAM;wBAAAgC,QAAA,EAAC;sBAAI;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAClCrG,OAAA;wBAAQ+D,KAAK,EAAC,QAAQ;wBAAAgC,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACtCrG,OAAA;wBAAQ+D,KAAK,EAAC,OAAO;wBAAAgC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNrG,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAElE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE4H,IAAI,EAAC,QAAQ;sBACb9D,IAAI,EAAC,KAAK;sBACVC,KAAK,EAAEvC,QAAQ,CAACK,GAAI;sBACpBgG,QAAQ,EAAEjE,iBAAkB;sBAC5BkC,SAAS,EAAC,6GAA6G;sBACvHgC,QAAQ;oBAAA;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrG,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAElE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE4H,IAAI,EAAC,MAAM;sBACX9D,IAAI,EAAC,SAAS;sBACdC,KAAK,EAAEvC,QAAQ,CAACM,OAAQ;sBACxB+F,QAAQ,EAAEjE,iBAAkB;sBAC5BkC,SAAS,EAAC,6GAA6G;sBACvHgC,QAAQ;oBAAA;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrG,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,EAAC;oBAElE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE4H,IAAI,EAAC,MAAM;sBACX9D,IAAI,EAAC,YAAY;sBACjBC,KAAK,EAAEvC,QAAQ,CAACO,UAAW;sBAC3B8F,QAAQ,EAAEjE,iBAAkB;sBAC5BkC,SAAS,EAAC,6GAA6G;sBACvHgC,QAAQ;oBAAA;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrG,OAAA;kBAAI8F,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjGrG,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAO8F,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrG,OAAA;oBAAK8F,SAAS,EAAC,wFAAwF;oBAAAC,QAAA,EACpG9F,eAAe,CAAC+H,GAAG,CAAE5D,OAAO,iBAC3BpE,OAAA;sBAAmB8F,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,eAC9C/F,OAAA;wBAAK8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACzC/F,OAAA;0BACE4H,IAAI,EAAC,UAAU;0BACfhD,EAAE,EAAE,WAAWR,OAAO,EAAG;0BACzB6D,OAAO,EAAEzG,QAAQ,CAACQ,WAAW,CAAC/B,eAAe,CAACqE,QAAQ,CAACF,OAAO,CAAE;0BAChEyD,QAAQ,EAAEA,CAAA,KAAM1D,0BAA0B,CAACC,OAAO,CAAE;0BACpD0B,SAAS,EAAC;wBAA8H;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzI,CAAC,eACFrG,OAAA;0BACEkI,OAAO,EAAE,WAAW9D,OAAO,EAAG;0BAC9B0B,SAAS,EAAC,8FAA8F;0BAAAC,QAAA,EAEvG3B;wBAAO;0BAAA8B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC,GAfEjC,OAAO;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgBZ,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrG,OAAA;kBAAK8F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD/F,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE8D,IAAI,EAAC,0BAA0B;sBAC/BC,KAAK,EAAEvC,QAAQ,CAACQ,WAAW,CAACC,wBAAyB;sBACrD4F,QAAQ,EAAE3D,uBAAwB;sBAClCiE,IAAI,EAAC,GAAG;sBACRrC,SAAS,EAAC;oBAA6G;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrG,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAO8F,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrG,OAAA;sBACE8D,IAAI,EAAC,oBAAoB;sBACzBC,KAAK,EAAEvC,QAAQ,CAACQ,WAAW,CAACE,kBAAmB;sBAC/C2F,QAAQ,EAAE3D,uBAAwB;sBAClCiE,IAAI,EAAC,GAAG;sBACRrC,SAAS,EAAC;oBAA6G;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrG,OAAA;kBAAI8F,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjGrG,OAAA;kBAAA+F,QAAA,eACE/F,OAAA;oBACE8D,IAAI,EAAC,gBAAgB;oBACrBC,KAAK,EAAEvC,QAAQ,CAACQ,WAAW,CAACG,cAAe;oBAC3C0F,QAAQ,EAAEjE,iBAAkB;oBAC5BuE,IAAI,EAAC,GAAG;oBACRC,WAAW,EAAC,2DAA2D;oBACvEtC,SAAS,EAAC,6GAA6G;oBACvHgC,QAAQ;kBAAA;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrG,OAAA;kBAAK8F,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC9C/F,OAAA,CAACL,MAAM,CAACuH,MAAM;oBACZU,IAAI,EAAC,QAAQ;oBACbP,OAAO,EAAEhC,gBAAiB;oBAC1BS,SAAS,EAAC,4GAA4G;oBACtHqB,UAAU,EAAE;sBAAET,KAAK,EAAE;oBAAK,CAAE;oBAC5BU,QAAQ,EAAE;sBAAEV,KAAK,EAAE;oBAAK,CAAE;oBAAAX,QAAA,EAC3B;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC,eAChBrG,OAAA,CAACL,MAAM,CAACuH,MAAM;oBACZU,IAAI,EAAC,QAAQ;oBACb9B,SAAS,EAAC,sLAAsL;oBAChMqB,UAAU,EAAE;sBAAET,KAAK,EAAE;oBAAK,CAAE;oBAC5BU,QAAQ,EAAE;sBAAEV,KAAK,EAAE;oBAAK,CAAE;oBAAAX,QAAA,EAC3B;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,gBAEbrG,OAAA,CAACL,MAAM,CAAC4G,GAAG;cACT8B,QAAQ,EAAE/C,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBC,OAAO,EAAC,MAAM;cACdX,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAExE/F,OAAA;gBAAK8F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpC/F,OAAA,CAACL,MAAM,CAACuH,MAAM;kBACZC,UAAU,EAAE;oBAAET,KAAK,EAAE;kBAAK,CAAE;kBAC5BU,QAAQ,EAAE;oBAAEV,KAAK,EAAE;kBAAK,CAAE;kBAC1BW,OAAO,EAAEhC,gBAAiB;kBAC1BS,SAAS,EAAC,sLAAsL;kBAAAC,QAAA,EACjM;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACNrG,OAAA,CAACL,MAAM,CAAC4G,GAAG;gBAAC8B,QAAQ,EAAEzC,IAAK;gBAACE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBAC/C/F,OAAA;kBAAK8F,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvE/F,OAAA;oBAAI8F,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,gBACzE/F,OAAA,CAACJ,MAAM;sBAACkG,SAAS,EAAC;oBAA6B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,wBAEpD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLrG,OAAA;oBAAK8F,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD/F,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChErG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEjF,WAAW,CAACY;sBAAQ;wBAAAwE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNrG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClErG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEjF,WAAW,CAACH;sBAAU;wBAAAuF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC,eACNrG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnErG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEjF,WAAW,CAACa;sBAAW;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC,eACNrG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7DrG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEjF,WAAW,CAACc;sBAAM;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC,eACNrG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAG;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1DrG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEjF,WAAW,CAACe;sBAAG;wBAAAqE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACNrG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9DrG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEjF,WAAW,CAACgB,OAAO,IAAI;sBAAc;wBAAAoE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC,eACNrG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjErG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAEjF,WAAW,CAACiB,UAAU,IAAI;sBAAc;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrG,OAAA;kBAAK8F,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvE/F,OAAA;oBAAI8F,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,gBACzE/F,OAAA,CAACH,aAAa;sBAACiG,SAAS,EAAC;oBAA6B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAE3D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLrG,OAAA;oBAAK8F,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxB/F,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvErG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EACtC,EAAA3F,qBAAA,GAAAU,WAAW,CAACkB,WAAW,cAAA5B,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAAyBH,eAAe,cAAAI,sBAAA,uBAAxCA,sBAAA,CAA0CiI,MAAM,IAAG,CAAC,GACjDxH,WAAW,CAACkB,WAAW,CAAC/B,eAAe,CAACsI,IAAI,CAAC,IAAI,CAAC,GAClD;sBAAM;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACNrG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAA0B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjFrG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EACtC,EAAAzF,sBAAA,GAAAQ,WAAW,CAACkB,WAAW,cAAA1B,sBAAA,uBAAvBA,sBAAA,CAAyB2B,wBAAwB,KAAI;sBAAM;wBAAAiE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACNrG,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAI8F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAmB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1ErG,OAAA;wBAAG8F,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EACtC,EAAAxF,sBAAA,GAAAO,WAAW,CAACkB,WAAW,cAAAzB,sBAAA,uBAAvBA,sBAAA,CAAyB2B,kBAAkB,KAAI;sBAAM;wBAAAgE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrG,OAAA;kBAAK8F,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvE/F,OAAA;oBAAI8F,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,gBACzE/F,OAAA,CAACF,YAAY;sBAACgG,SAAS,EAAC;oBAA6B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAE1D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLrG,OAAA;oBAAG8F,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjC,EAAAvF,sBAAA,GAAAM,WAAW,CAACkB,WAAW,cAAAxB,sBAAA,uBAAvBA,sBAAA,CAAyB2B,cAAc,KAAI;kBAAM;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClG,EAAA,CAjmBID,cAAc;EAAA,QACMR,OAAO,EACRL,SAAS;AAAA;AAAAmJ,EAAA,GAF5BtI,cAAc;AAmmBpB,eAAeA,cAAc;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}