{"ast": null, "code": "import { useTranslation } from './useTranslation.js';\nexport function Translation(props) {\n  const {\n    ns,\n    children,\n    ...options\n  } = props;\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n}", "map": {"version": 3, "names": ["useTranslation", "Translation", "props", "ns", "children", "options", "t", "i18n", "ready", "lng", "language"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/node_modules/react-i18next/dist/es/Translation.js"], "sourcesContent": ["import { useTranslation } from './useTranslation.js';\nexport function Translation(props) {\n  const {\n    ns,\n    children,\n    ...options\n  } = props;\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n}"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,MAAM;IACJC,EAAE;IACFC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGH,KAAK;EACT,MAAM,CAACI,CAAC,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGR,cAAc,CAACG,EAAE,EAAEE,OAAO,CAAC;EACpD,OAAOD,QAAQ,CAACE,CAAC,EAAE;IACjBC,IAAI;IACJE,GAAG,EAAEF,IAAI,CAACG;EACZ,CAAC,EAAEF,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}