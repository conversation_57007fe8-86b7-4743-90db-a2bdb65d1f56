const Joi = require('joi');
const { commonSchemas, COLLECTIONS } = require('./index');

// Student validation schema for Firebase
const studentSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  studentId: Joi.string().required(),
  email: commonSchemas.email,
  password: Joi.string().required(),
  plainPassword: Joi.string().optional(), // Store unhashed password if needed
  name: Joi.string().required(),
  role: Joi.string().default('student'),
  patients: Joi.array().items(Joi.string()).default([]), // Array of patient IDs
  reviews: Joi.array().items(Joi.string()).default([]), // Array of review IDs
  appointments: Joi.array().items(Joi.string()).default([]), // Array of appointment IDs
  university: Joi.string().required(),
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// Student creation schema (without ID)
const createStudentSchema = studentSchema.fork(['id'], (schema) => schema.forbidden());

// Student update schema (partial)
const updateStudentSchema = studentSchema.fork(
  ['studentId', 'email', 'name', 'university'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Student login schema
const loginStudentSchema = Joi.object({
  email: commonSchemas.email,
  password: Joi.string().required()
});

// Helper functions for Student operations
const StudentHelpers = {
  // Validate student data
  validateCreate: (data) => createStudentSchema.validate(data),
  validateUpdate: (data) => updateStudentSchema.validate(data),
  validateLogin: (data) => loginStudentSchema.validate(data),
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    // Convert array ObjectIds to strings
    ['patients', 'reviews', 'appointments'].forEach(field => {
      if (transformed[field]) {
        transformed[field] = transformed[field].map(id => 
          typeof id === 'object' ? id.toString() : id
        );
      }
    });
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    if (prepared.createdAt && typeof prepared.createdAt === 'string') {
      prepared.createdAt = new Date(prepared.createdAt);
    }
    if (prepared.updatedAt && typeof prepared.updatedAt === 'string') {
      prepared.updatedAt = new Date(prepared.updatedAt);
    }
    
    return prepared;
  },
  
  // Remove sensitive data for client response
  sanitizeForResponse: (studentData) => {
    const sanitized = { ...studentData };
    delete sanitized.password;
    delete sanitized.plainPassword;
    return sanitized;
  }
};

module.exports = {
  studentSchema,
  createStudentSchema,
  updateStudentSchema,
  loginStudentSchema,
  StudentHelpers,
  COLLECTION_NAME: COLLECTIONS.STUDENTS
};
