const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { login, getCurrentUser, getProfile } = require('../controllers/authController');
const auth = require('../middleware/auth');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

router.post('/login', login);

// Logout endpoint
router.post('/logout', auth, async (req, res) => {
  try {
    // Log logout activity
    await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
      userId: req.user.id,
      userName: req.user.name || req.user.email || 'Unknown',
      userRole: req.user.role,
      action: 'User logged out',
      details: `Email: ${req.user.email}`,
      ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
      timestamp: new Date()
    });

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get current user
router.get('/me', auth, getCurrentUser);

router.get('/profile', auth, getProfile);

// Reset all passwords to a standard value (only accessible to superadmin)
router.post('/reset-all-passwords', auth, async (req, res) => {
  try {
    // Check if user is superadmin
    if (req.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { defaultPassword } = req.body;

    if (!defaultPassword || defaultPassword.length < 6) {
      return res.status(400).json({ message: 'Password must be at least 6 characters' });
    }

    const results = {
      students: 0,
      supervisors: 0,
      admins: 0,
      superadmins: 0,
      assistants: 0,
      dentists: 0
    };

    // Reset student passwords
    const students = await Student.find();
    for (const student of students) {
      student.password = defaultPassword;
      await student.save();
      results.students++;
    }

    // Reset supervisor passwords
    const supervisors = await Supervisor.find();
    for (const supervisor of supervisors) {
      supervisor.password = defaultPassword;
      await supervisor.save();
      results.supervisors++;
    }

    // Reset admin passwords
    const admins = await Admin.find();
    for (const admin of admins) {
      admin.password = defaultPassword;
      await admin.save();
      results.admins++;
    }

    // Reset superadmin passwords
    const superadmins = await Superadmin.find();
    for (const superadmin of superadmins) {
      superadmin.password = defaultPassword;
      await superadmin.save();
      results.superadmins++;
    }

    // Reset assistant passwords
    const assistants = await Assistant.find();
    for (const assistant of assistants) {
      assistant.password = defaultPassword;
      await assistant.save();
      results.assistants++;
    }

    // Reset dentist passwords
    const dentists = await Dentist.find();
    for (const dentist of dentists) {
      dentist.password = defaultPassword;
      await dentist.save();
      results.dentists++;
    }

    res.json({
      message: 'All passwords reset successfully',
      results
    });
  } catch (error) {
    console.error('Error resetting all passwords:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;