const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { login, getCurrentUser, getProfile } = require('../controllers/authController');
const auth = require('../middleware/auth');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

router.post('/login', login);

// Logout endpoint
router.post('/logout', auth, async (req, res) => {
  try {
    // Log logout activity
    await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
      userId: req.user.id,
      userName: req.user.name || req.user.email || 'Unknown',
      userRole: req.user.role,
      action: 'User logged out',
      details: `Email: ${req.user.email}`,
      ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
      timestamp: new Date()
    });

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get current user
router.get('/me', auth, getCurrentUser);

router.get('/profile', auth, getProfile);

// Reset all passwords to a standard value (only accessible to superadmin)
router.post('/reset-all-passwords', auth, async (req, res) => {
  try {
    // Check if user is superadmin
    if (req.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { defaultPassword } = req.body;

    if (!defaultPassword || defaultPassword.length < 6) {
      return res.status(400).json({ message: 'Password must be at least 6 characters' });
    }

    const results = {
      students: 0,
      supervisors: 0,
      admins: 0,
      superadmins: 0,
      assistants: 0,
      dentists: 0
    };

    // Reset student passwords
    const students = await FirestoreHelpers.find(COLLECTIONS.STUDENTS);
    for (const student of students) {
      const hashedPassword = await bcrypt.hash(defaultPassword, 10);
      await FirestoreHelpers.update(COLLECTIONS.STUDENTS, student.id, {
        password: hashedPassword,
        plainPassword: defaultPassword,
        updatedAt: new Date()
      });
      results.students++;
    }

    // Reset supervisor passwords
    const supervisors = await FirestoreHelpers.find(COLLECTIONS.SUPERVISORS);
    for (const supervisor of supervisors) {
      const hashedPassword = await bcrypt.hash(defaultPassword, 10);
      await FirestoreHelpers.update(COLLECTIONS.SUPERVISORS, supervisor.id, {
        password: hashedPassword,
        plainPassword: defaultPassword,
        updatedAt: new Date()
      });
      results.supervisors++;
    }

    // Reset admin passwords
    const admins = await FirestoreHelpers.find(COLLECTIONS.ADMINS);
    for (const admin of admins) {
      const hashedPassword = await bcrypt.hash(defaultPassword, 10);
      await FirestoreHelpers.update(COLLECTIONS.ADMINS, admin.id, {
        password: hashedPassword,
        plainPassword: defaultPassword,
        updatedAt: new Date()
      });
      results.admins++;
    }

    // Reset superadmin passwords
    const superadmins = await FirestoreHelpers.find(COLLECTIONS.CONFIGS);
    for (const superadmin of superadmins) {
      if (superadmin.role === 'superadmin') {
        const hashedPassword = await bcrypt.hash(defaultPassword, 10);
        await FirestoreHelpers.update(COLLECTIONS.CONFIGS, superadmin.id, {
          password: hashedPassword,
          plainPassword: defaultPassword,
          updatedAt: new Date()
        });
        results.superadmins++;
      }
    }

    // Reset assistant passwords
    const assistants = await FirestoreHelpers.find(COLLECTIONS.ASSISTANTS);
    for (const assistant of assistants) {
      const hashedPassword = await bcrypt.hash(defaultPassword, 10);
      await FirestoreHelpers.update(COLLECTIONS.ASSISTANTS, assistant.id, {
        password: hashedPassword,
        plainPassword: defaultPassword,
        updatedAt: new Date()
      });
      results.assistants++;
    }

    // Note: Dentist functionality removed as per university focus

    res.json({
      message: 'All passwords reset successfully',
      results
    });
  } catch (error) {
    console.error('Error resetting all passwords:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;