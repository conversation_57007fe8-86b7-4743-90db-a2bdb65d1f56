const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
dotenv.config();

const connectDB = require('./config/db');
const Student = require('./models/Student');
const Supervisor = require('./models/Supervisor');
const Admin = require('./models/Admin');
const Superadmin = require('./models/Superadmin');

// Connect to MongoDB
connectDB();

async function createUsers() {
  try {
    const users = [
      {
        model: Student,
        email: '<EMAIL>',
        password: 'student123',
        name: 'Test Student',
        studentId: 'STU001', // Required field!
      },
      {
        model: Supervisor,
        email: '<EMAIL>',
        password: 'supervisor123',
        name: 'Test Supervisor',
      },
      {
        model: Admin,
        email: '<EMAIL>',
        password: 'admin123',
        name: 'Test Admin',
      },
      {
        model: Superadmin,
        email: '<EMAIL>',
        password: 'superadmin123',
        name: 'Test Superadmin',
      },
    ];

    for (const { model, ...data } of users) {
      const exists = await model.findOne({ email: data.email });
      if (exists) {
        console.log(`⚠️  ${data.email} already exists.`);
        continue;
      }

      const hashedPassword = await bcrypt.hash(data.password, 10);
      const user = new model({ ...data, password: hashedPassword });
      await user.save();
      console.log(`✅ Created user: ${data.email}`);
    }

    console.log('🎉 All test users created!');
    process.exit();
  } catch (error) {
    console.error('🔥 Error creating users:', error);
    process.exit(1);
  }
}

// Call the function
createUsers();
