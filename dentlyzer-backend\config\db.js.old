require('dotenv').config();
const mongoose = require('mongoose');
const config = require('./config');

const connectDB = async () => {
  try {
    await mongoose.connect(config.MONGO_URI, {
      dbName: 'dentlyzer', // Database name
    });
    console.log(`✅ Connected to MongoDB - Database: dentlyzer`);
    console.log(`🌐 Environment: ${config.NODE_ENV}`);
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    process.exit(1);
  }
};

module.exports = connectDB;
