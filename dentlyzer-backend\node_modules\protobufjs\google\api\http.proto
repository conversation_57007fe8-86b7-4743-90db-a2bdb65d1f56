syntax = "proto3";

package google.api;

message Http {

    repeated HttpRule rules = 1;
}

message HttpRule {

    oneof pattern {

        string get = 2;
        string put = 3;
        string post = 4;
        string delete = 5;
        string patch = 6;
        CustomHttpPattern custom = 8;
    }

    string selector = 1;
    string body = 7;
    repeated HttpRule additional_bindings = 11;
}

message CustomHttpPattern {

    string kind = 1;
    string path = 2;
}