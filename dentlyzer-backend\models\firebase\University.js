const Joi = require('joi');
const { 
  commonSchemas, 
  bilingualStringSchema, 
  timeSlotSchema, 
  addressSchema, 
  contactInfoSchema,
  COLLECTIONS 
} = require('./index');

// University validation schema for Firebase
const universitySchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  universityId: Joi.string().required(),
  name: bilingualStringSchema.required(),
  description: bilingualStringSchema.required(),
  dentistryInfo: bilingualStringSchema.required(),
  facilities: bilingualStringSchema.required(),
  program: bilingualStringSchema.required(),
  dentistryServices: Joi.array().items(bilingualStringSchema).default([]),
  address: addressSchema.required(),
  contactInfo: contactInfoSchema.required(),
  image: Joi.string().allow(''),
  logo: Joi.string().allow(''),
  mapUrl: Joi.string().allow(''),
  timeSlots: Joi.array().items(timeSlotSchema).default([]),
  slotBeginDate: Joi.date().required(),
  slotEndDate: Joi.date().required(),
  holidays: Joi.array().items(
    Joi.string().valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')
  ).default([]),
  students: Joi.array().items(Joi.string()).default([]), // Array of student IDs
  supervisors: Joi.array().items(Joi.string()).default([]), // Array of supervisor IDs
  admins: Joi.array().items(Joi.string()).default([]), // Array of admin IDs
  assistants: Joi.array().items(Joi.string()).default([]), // Array of assistant IDs
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// University creation schema (without ID)
const createUniversitySchema = universitySchema.fork(['id'], (schema) => schema.forbidden());

// University update schema (partial)
const updateUniversitySchema = universitySchema.fork(
  ['universityId', 'name', 'description', 'dentistryInfo', 'facilities', 'program', 'address', 'contactInfo', 'slotBeginDate', 'slotEndDate'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Helper functions for University operations
const UniversityHelpers = {
  // Validate university data
  validateCreate: (data) => createUniversitySchema.validate(data),
  validateUpdate: (data) => updateUniversitySchema.validate(data),
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    // Convert array ObjectIds to strings
    ['students', 'supervisors', 'admins', 'assistants'].forEach(field => {
      if (transformed[field]) {
        transformed[field] = transformed[field].map(id => 
          typeof id === 'object' ? id.toString() : id
        );
      }
    });
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    ['createdAt', 'updatedAt', 'slotBeginDate', 'slotEndDate'].forEach(field => {
      if (prepared[field] && typeof prepared[field] === 'string') {
        prepared[field] = new Date(prepared[field]);
      }
    });
    
    // Handle time slot dates
    if (prepared.timeSlots) {
      prepared.timeSlots = prepared.timeSlots.map(slot => ({
        ...slot,
        date: typeof slot.date === 'string' ? new Date(slot.date) : slot.date
      }));
    }
    
    return prepared;
  },
  
  // Add user to university
  addUserToUniversity: (universityData, userId, userType) => {
    const validTypes = ['students', 'supervisors', 'admins', 'assistants'];
    if (!validTypes.includes(userType)) {
      throw new Error(`Invalid user type: ${userType}`);
    }
    
    const updated = { ...universityData };
    if (!updated[userType]) {
      updated[userType] = [];
    }
    
    if (!updated[userType].includes(userId)) {
      updated[userType].push(userId);
    }
    
    updated.updatedAt = new Date();
    return updated;
  },
  
  // Remove user from university
  removeUserFromUniversity: (universityData, userId, userType) => {
    const validTypes = ['students', 'supervisors', 'admins', 'assistants'];
    if (!validTypes.includes(userType)) {
      throw new Error(`Invalid user type: ${userType}`);
    }
    
    const updated = { ...universityData };
    if (updated[userType]) {
      updated[userType] = updated[userType].filter(id => id !== userId);
    }
    
    updated.updatedAt = new Date();
    return updated;
  }
};

module.exports = {
  universitySchema,
  createUniversitySchema,
  updateUniversitySchema,
  UniversityHelpers,
  COLLECTION_NAME: COLLECTIONS.UNIVERSITIES
};
