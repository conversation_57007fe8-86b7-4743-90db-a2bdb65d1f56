{"name": "dentlyzer-backend", "version": "1.0.0", "description": "Backend API for ODenta dental management system", "main": "server.js", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for Node.js backend'", "test": "echo 'No tests specified'", "seed": "node seed.js", "seed:universities": "node seedUniversity.js", "seed:supervisors": "node seedSupervisor.js", "migrate:firebase": "node scripts/migrateToFirebase.js"}, "keywords": ["dental", "management", "api", "nodejs", "express", "firebase"], "author": "ODenta Team", "license": "ISC", "dependencies": {"axios": "^1.7.7", "bcryptjs": "^2.4.3", "cloudinary": "^2.5.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "firebase-admin": "^12.7.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.7"}}