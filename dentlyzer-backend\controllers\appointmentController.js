const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { AppointmentHelpers, createAppointmentSchema } = require('../models/firebase/Appointment');

const appointmentSchema = Joi.object({
  date: Joi.date().required(),
  time: Joi.string().required(),
  type: Joi.string().required(),
  notes: Joi.string().allow(''),
  status: Joi.string().valid('pending', 'completed', 'cancelled').default('pending'),
  treatment: Joi.string().allow(''),
  duration: Joi.number().default(60),
  patient: Joi.string().required(), // nationalId
  doctor: Joi.string().when('isPatientInitiated', { is: false, then: Joi.required(), otherwise: Joi.allow(null) }),
  doctorModel: Joi.string().valid('Student', 'Dentist').when('isPatientInitiated', { is: false, then: Joi.required(), otherwise: Joi.allow(null) }),
  university: Joi.string().when('isPatientInitiated', { is: true, then: Joi.required(), otherwise: Joi.allow(null) }),
  isPatientInitiated: Joi.boolean().default(false),
  fullName: Joi.string(),
  phoneNumber: Joi.string(),
  age: Joi.number(),
  chiefComplaint: Joi.string().required(),
  occupation: Joi.string().allow(''),
  address: Joi.string().allow(''),
});

const patientAppointmentSchema = Joi.object({
  date: Joi.date().required(),
  time: Joi.string().required(),
  type: Joi.string().required(),
  patient: Joi.string().required(), // nationalId
  fullName: Joi.string().required(),
  phoneNumber: Joi.string().required(),
  age: Joi.number().required(),
  chiefComplaint: Joi.string().required(),
  duration: Joi.number().default(60),
  universityClinic: Joi.string().required(),
  isPatientInitiated: Joi.boolean().default(true),
  occupation: Joi.string().allow(''),
  address: Joi.string().allow(''),
});

const createAppointment = async (req, res) => {
  try {
    const { error } = appointmentSchema.validate(req.body);
    if (error) {
      console.log('Validation error:', error.details);
      return res.status(400).json({ message: error.details[0].message });
    }

    const { date, time, type, notes, status, patient, doctor, doctorModel, chiefComplaint, occupation, address } = req.body;

    const patientDoc = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: patient }
    );
    if (!patientDoc) {
      console.log(`No patient found with nationalId: ${patient}`);
      return res.status(404).json({ message: 'Patient not found' });
    }

    let doctorDoc;
    if (doctorModel === 'Student') {
      doctorDoc = await FirestoreHelpers.findOne(
        COLLECTIONS.STUDENTS,
        { field: 'studentId', operator: '==', value: doctor }
      );
      if (!doctorDoc) {
        console.log(`No student found with ID: ${doctor}`);
        return res.status(404).json({ message: 'Student not found' });
      }
    } else {
      console.log('Invalid doctorModel:', doctorModel);
      return res.status(400).json({ message: 'Invalid doctorModel' });
    }

    // Check for existing appointments
    const allAppointments = await FirestoreHelpers.find(COLLECTIONS.APPOINTMENTS);
    const existingAppointment = allAppointments.find(apt =>
      apt.doctor === doctor &&
      apt.doctorModel === doctorModel &&
      new Date(apt.date).toDateString() === new Date(date).toDateString() &&
      apt.time === time &&
      apt.status !== 'cancelled'
    );

    if (existingAppointment) {
      console.log(`Slot already booked: ${date} ${time}`);
      return res.status(400).json({ message: 'This time slot is already booked' });
    }

    const appointmentData = {
      date: new Date(date),
      time,
      type,
      notes,
      status: status || 'pending',
      patient: patientDoc.id,
      doctor: doctorDoc.studentId || doctor,
      doctorModel,
      chiefComplaint,
      occupation,
      address,
      nationalId: patientDoc.nationalId,
      fullName: patientDoc.fullName,
      phoneNumber: patientDoc.phoneNumber,
      age: patientDoc.age,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const savedAppointment = await FirestoreHelpers.create(COLLECTIONS.APPOINTMENTS, appointmentData);
    console.log('Appointment saved:', savedAppointment);

    // Update patient with appointment reference
    const updatedPatient = await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patientDoc.id,
      {
        appointments: [...(patientDoc.appointments || []), savedAppointment.id],
        updatedAt: new Date()
      }
    );

    res.status(201).json(savedAppointment);
  } catch (error) {
    console.error('Error creating appointment:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const createPatientAppointment = async (req, res) => {
  try {
    const { error: validationError } = patientAppointmentSchema.validate(req.body);
    if (validationError) {
      console.log('Validation error:', validationError.details);
      return res.status(400).json({ message: validationError.details[0].message });
    }

    const {
      date,
      time,
      type,
      patient,
      fullName,
      phoneNumber,
      age,
      chiefComplaint,
      duration,
      universityClinic,
      isPatientInitiated,
      occupation,
      address,
    } = req.body;

    const patientDoc = await Patient.findOne({ nationalId: patient });
    const university = await University.findOne({ universityId: universityClinic });
    if (!university) {
      console.log(`No university found with universityId: ${universityClinic}`);
      return res.status(404).json({ message: `University '${universityClinic}' not found` });
    }

    const slotDate = new Date(date);
    const slot = university.timeSlots.find(
      (s) =>
        s.date.toISOString().slice(0, 10) === slotDate.toISOString().slice(0, 10) &&
        s.time === time &&
        s.isAvailable
    );

    if (!slot) {
      console.log(`Slot not available: ${date} ${time} at ${universityClinic}`);
      return res.status(400).json({ message: 'Selected slot is not available' });
    }

    slot.isAvailable = false;
    await university.save();

    const appointment = new Appointment({
      date: slotDate,
      time,
      type,
      patient: patientDoc ? patientDoc._id : undefined,
      fullName: patientDoc ? patientDoc.fullName : fullName,
      phoneNumber: patientDoc ? patientDoc.phoneNumber : phoneNumber,
      age: patientDoc ? patientDoc.age : age,
      chiefComplaint,
      duration,
      university: universityClinic,
      isPatientInitiated,
      status: 'pending',
      occupation,
      address,
      nationalId: patientDoc ? patientDoc.nationalId : patient, // Add nationalId (use patient param which is the nationalId)
      gender: patientDoc ? patientDoc.gender : 'other', // Add gender
    });

    const savedAppointment = await appointment.save();
    console.log('Patient-initiated appointment saved:', savedAppointment);

    if (patientDoc) {
      await Patient.findByIdAndUpdate(patientDoc._id, {
        $push: { appointments: savedAppointment._id },
      });
    }

    const populatedAppointment = await Appointment.findById(savedAppointment._id)
      .populate('patient', 'nationalId fullName');

    res.status(201).json(populatedAppointment);
  } catch (error) {
    console.error('Error creating patient appointment:', error.message, error.stack);
    res.status(500).json({ message: `Server error: ${error.message}` });
  }
};

const getAppointmentsByDoctor = async (req, res) => {
  try {
    // Get the doctor ID from the user object
    const doctorId = req.user.studentId || req.user.dentistId;
    const doctorModel = req.user.role === 'dentist' ? 'Dentist' : 'Student';

    console.log(`Fetching appointments for ${doctorModel} with ID: ${doctorId}`);

    // Find all appointments for this doctor, including both doctor-initiated and patient-initiated
    const appointments = await Appointment.find({
      doctor: doctorId,
      doctorModel: doctorModel,
    })
      .populate('patient', 'nationalId fullName')
      .sort({ date: -1, time: 1 });

    console.log(`Found ${appointments.length} appointments for ${req.user.role} ${doctorId}`);
    res.json(appointments);
  } catch (error) {
    console.error('Error fetching appointments:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const getAppointmentsByPatient = async (req, res) => {
  try {
    const { nationalId } = req.params;

    const patient = await Patient.findOne({ nationalId });
    let appointments;
    if (patient) {
      appointments = await Appointment.find({ patient: patient._id })
        .populate('patient', 'nationalId fullName')
        .sort({ date: -1, time: 1 });
    } else {
      appointments = await Appointment.find({
        patient: { $exists: false },
        nationalId,
        isPatientInitiated: true,
      })
        .sort({ date: -1, time: 1 });
    }

    console.log(`Appointments for patient ${nationalId}:`, appointments.length);
    res.status(200).json(appointments);
  } catch (error) {
    console.error('Error fetching appointments:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getAvailableSlots = async (req, res) => {
  try {
    const { doctorId, doctorModel, date } = req.query;

    if (!doctorId || !doctorModel || !date) {
      console.log('Missing query parameters:', { doctorId, doctorModel, date });
      return res.status(400).json({ message: 'doctorId, doctorModel, and date are required' });
    }

    if (!['Student', 'Dentist'].includes(doctorModel)) {
      console.log('Invalid doctorModel:', doctorModel);
      return res.status(400).json({ message: 'Invalid doctorModel. Must be Student or Dentist' });
    }

    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      console.log('Invalid date format:', date);
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
    }

    let doctor;
    if (doctorModel === 'Student') {
      doctor = await Student.findOne({ studentId: doctorId });
    } else {
      doctor = await Dentist.findOne({ dentistId: doctorId });
    }

    if (!doctor) {
      console.log(`No ${doctorModel} found with ID: ${doctorId}`);
      return res.status(404).json({ message: `No ${doctorModel} found with ID ${doctorId}` });
    }

    const targetDate = new Date(date);
    if (isNaN(targetDate)) {
      console.log('Invalid date:', date);
      return res.status(400).json({ message: 'Invalid date' });
    }
    targetDate.setHours(0, 0, 0, 0);

    const slots = [
      '9:00 - 11:00',
      '11:30 - 13:30',
      '14:00 - 16:00',
    ];

    const startOfDay = new Date(targetDate);
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const bookedAppointments = await Appointment.find({
      doctor: doctorId,
      doctorModel,
      date: { $gte: startOfDay, $lte: endOfDay },
      status: { $ne: 'cancelled' },
    }).select('time');

    console.log('Booked appointments:', bookedAppointments);

    const bookedTimes = bookedAppointments.map((appt) => appt.time);

    const availableSlots = slots.filter((slot) => {
      const slotStartTime = slot.split(' - ')[0];
      return !bookedTimes.includes(slotStartTime);
    });

    console.log(`Available slots for ${doctorModel} ${doctorId} on ${date}:`, availableSlots);
    res.status(200).json(availableSlots);
  } catch (error) {
    console.error('Error in getAvailableSlots:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getAllAppointments = async (req, res) => {
  try {
    if (!['superadmin', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { university } = req.query;
    const query = university ? { university } : {};

    const appointments = await Appointment.find(query)
      .populate('patient', 'nationalId fullName phoneNumber gender age address occupation')
      .sort({ date: -1, time: 1 });

    console.log(`Fetched ${appointments.length} appointments${university ? ` for university: ${university}` : ''}`);
    res.json(appointments);
  } catch (error) {
    console.error('Error fetching all appointments:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const deleteAppointment = async (req, res) => {
  try {
    const appointment = await Appointment.findById(req.params.id);
    if (!appointment) return res.status(404).json({ message: 'Appointment not found' });
    if (appointment.isPatientInitiated && !['superadmin', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    if (appointment.doctorModel === 'Dentist') {
      await Dentist.updateOne(
        { _id: appointment.doctor, 'timeSlots.date': appointment.date, 'timeSlots.time': appointment.time },
        { $set: { 'timeSlots.$.isAvailable': true } }
      );
    }
    if (appointment.patient) {
      await Patient.findByIdAndUpdate(appointment.patient, {
        $pull: { appointments: appointment._id },
      });
    }
    await Appointment.findByIdAndDelete(req.params.id);
    res.json({ message: 'Appointment deleted' });
  } catch (error) {
    console.error('Error deleting appointment:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const downloadSchedule = async (req, res) => {
  try {
    // Check if university query parameter is provided
    const { university } = req.query;

    if (university) {
      // If university is provided, fetch appointments for that university
      const appointments = await Appointment.find({
        university: university,
        status: { $ne: 'cancelled' },
      })
        .populate('patient', 'nationalId fullName phoneNumber gender age address occupation')
        .sort({ date: -1, time: 1 })
        .lean();

      console.log(`Fetched ${appointments.length} appointments for university: ${university}`);
      return res.json(appointments);
    }

    // If no university provided, use the assistant's affiliation
    const { assistantId, dentistId } = req.user;
    const assistant = await Assistant.findOne({ assistantId: assistantId || dentistId });
    if (!assistant) return res.status(404).json({ message: 'Assistant not found' });

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    let appointments;
    if (assistant.affiliation?.type === 'dentist') {
      const dentist = await Dentist.findOne({ dentistId: assistant.affiliation.id });
      if (!dentist) return res.status(404).json({ message: 'Dentist not found' });
      appointments = await Appointment.find({
        doctor: dentist.dentistId,
        doctorModel: 'Dentist',
        date: { $gte: today, $lt: tomorrow },
        status: { $ne: 'cancelled' },
      })
        .populate('patient', 'nationalId fullName phoneNumber gender age address occupation')
        .lean();
    } else {
      appointments = await Appointment.find({
        university: assistant.affiliation?.id || assistant.university,
        status: { $ne: 'cancelled' },
      })
        .populate('patient', 'nationalId fullName phoneNumber gender age address occupation')
        .lean();

      // Log the first appointment to see its structure
      if (appointments.length > 0) {
        console.log('First appointment data from backend:', {
          _id: appointments[0]._id,
          patient: appointments[0].patient,
          nationalId: appointments[0].nationalId || (appointments[0].patient && appointments[0].patient.nationalId),
          fullName: appointments[0].fullName || (appointments[0].patient && appointments[0].patient.fullName)
        });
      }
    }

    res.json(appointments);
  } catch (error) {
    console.error('Error downloading schedule:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const remindPatients = async (req, res) => {
  try {
    const { assistantId } = req.user;
    const assistant = await Assistant.findOne({ assistantId });
    if (!assistant) return res.status(404).json({ message: 'Assistant not found' });

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    const dayAfter = new Date(tomorrow);
    dayAfter.setDate(dayAfter.getDate() + 1);

    let appointments;
    if (assistant.affiliation.type === 'dentist') {
      const dentist = await Dentist.findOne({ dentistId: assistant.affiliation.id });
      if (!dentist) return res.status(404).json({ message: 'Dentist not found' });
      appointments = await Appointment.find({
        doctor: dentist.dentistId,
        doctorModel: 'Dentist',
        date: { $gte: tomorrow, $lt: dayAfter },
        status: { $ne: 'cancelled' },
      })
        .populate('patient', 'nationalId fullName phoneNumber')
        .lean();
    } else {
      appointments = await Appointment.find({
        university: assistant.affiliation.id,
        date: { $gte: tomorrow, $lt: dayAfter },
        status: { $ne: 'cancelled' },
      })
        .populate('patient', 'nationalId fullName phoneNumber')
        .lean();
    }

    const reminders = appointments.map((appt) => ({
      patient: appt.patient ? appt.patient.fullName : appt.fullName,
      phoneNumber: appt.patient ? appt.patient.phoneNumber : appt.phoneNumber,
      date: appt.date,
      time: appt.time,
    }));

    res.json({ message: 'Reminders queued', reminders });
  } catch (error) {
    console.error('Error reminding patients:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const getUniversityAvailableSlots = async (req, res) => {
  try {
    const { university, date } = req.query;
    if (!university || !date) {
      console.log('Missing query parameters:', { university, date });
      return res.status(400).json({ message: 'University and date are required' });
    }

    const parsedDate = new Date(date);
    if (isNaN(parsedDate)) {
      console.log('Invalid date format:', date);
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
    }

    const uni = await University.findOne({ universityId: university });
    if (!uni) {
      console.log(`No university found with universityId: ${university}`);
      return res.status(404).json({ message: `University '${university}' not found` });
    }

    if (!uni.timeSlots || !Array.isArray(uni.timeSlots)) {
      console.log(`No time slots defined for university: ${university}`);
      return res.status(400).json({ message: `No time slots defined for university '${university}'` });
    }

    const startOfDay = new Date(parsedDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(parsedDate);
    endOfDay.setHours(23, 59, 59, 999);

    const availableSlots = uni.timeSlots
      .filter((slot) => {
        const slotDate = new Date(slot.date);
        return slot.isAvailable && slotDate >= startOfDay && slotDate <= endOfDay;
      })
      .map((slot) => {
        const endTime = new Date(`1970-01-01T${slot.time}:00Z`);
        endTime.setMinutes(endTime.getMinutes() + (slot.duration || 60));
        const endTimeStr = endTime.toISOString().slice(11, 16);
        return `${slot.time} - ${endTimeStr}`;
      });

    const uniqueSlots = [...new Set(availableSlots)];
    console.log(`Available slots for ${university} on ${date}:`, uniqueSlots);

    res.json(uniqueSlots);
  } catch (error) {
    console.error('Error fetching university available slots:', error.message, error.stack);
    res.status(500).json({ message: `Server error: ${error.message}` });
  }
};

// Assign a student to an appointment
const assignAppointment = async (req, res) => {
  try {
    const { id } = req.params;
    const { doctor, doctorModel } = req.body;

    if (!doctor || !doctorModel) {
      return res.status(400).json({ message: 'Doctor ID and doctor model are required' });
    }

    // Validate that the doctor exists
    let doctorDoc;
    if (doctorModel === 'Student') {
      doctorDoc = await Student.findOne({ studentId: doctor });
      if (!doctorDoc) {
        return res.status(404).json({ message: 'Student not found' });
      }
    } else if (doctorModel === 'Dentist') {
      doctorDoc = await Dentist.findOne({ dentistId: doctor });
      if (!doctorDoc) {
        return res.status(404).json({ message: 'Dentist not found' });
      }
    } else {
      return res.status(400).json({ message: 'Invalid doctor model' });
    }

    // Find and update the appointment
    const appointment = await Appointment.findById(id);
    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Check if the student is already assigned to another appointment at the same time
    if (doctorModel === 'Student') {
      const existingAppointment = await Appointment.findOne({
        doctor: doctor,
        doctorModel: 'Student',
        date: appointment.date,
        time: appointment.time,
        status: { $ne: 'cancelled' },
        _id: { $ne: id } // Exclude the current appointment
      });

      if (existingAppointment) {
        return res.status(400).json({
          message: 'Student already has an appointment at this time',
          conflict: true
        });
      }
    }

    // Update the appointment
    appointment.doctor = doctor;
    appointment.doctorModel = doctorModel;

    // If assigning to a student, add student name information
    if (doctorModel === 'Student' && doctorDoc) {
      appointment.studentName = doctorDoc.name;
      appointment.studentId = doctorDoc.studentId;
    }

    // If the appointment has a patient, make sure the nationalId is set
    if (appointment.patient) {
      const patientDoc = await Patient.findById(appointment.patient);
      if (patientDoc && patientDoc.nationalId) {
        appointment.nationalId = patientDoc.nationalId;
        console.log(`Setting nationalId ${patientDoc.nationalId} on appointment ${id}`);
      }
    }

    const updatedAppointment = await appointment.save();

    // If this is a student assignment, update the student's appointments list if needed
    if (doctorModel === 'Student') {
      // Check if the student has an appointments array
      const student = await Student.findOne({ studentId: doctor });
      if (student) {
        // Check if the appointment is already in the student's list
        if (!student.appointments.includes(updatedAppointment._id)) {
          await Student.findOneAndUpdate(
            { studentId: doctor },
            { $push: { appointments: updatedAppointment._id } }
          );
          console.log(`Added appointment ${updatedAppointment._id} to student ${doctor}'s appointments list`);
        }

        // If the appointment has a patient, add the patient to the student's patients list
        if (appointment.patient) {
          // Get the patient document
          const patientDoc = await Patient.findById(appointment.patient);
          if (patientDoc) {
            // Check if the patient is already in the student's patients list
            if (!student.patients.includes(patientDoc._id)) {
              await Student.findOneAndUpdate(
                { studentId: doctor },
                { $push: { patients: patientDoc._id } }
              );
              console.log(`Added patient ${patientDoc._id} to student ${doctor}'s patients list`);
            }
          }
        }
      }
    }

    // Return the updated appointment
    const populatedAppointment = await Appointment.findById(updatedAppointment._id)
      .populate('patient', 'nationalId fullName phoneNumber gender age address occupation');

    res.json(populatedAppointment);
  } catch (error) {
    console.error('Error assigning appointment:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update an appointment with a patient ID
const updateAppointmentPatient = async (req, res) => {
  try {
    const { id } = req.params;
    const { patientId } = req.body;

    if (!patientId) {
      return res.status(400).json({ message: 'Patient ID is required' });
    }

    // Find the appointment
    const appointment = await Appointment.findById(id);
    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Validate that the patient exists
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Update the appointment
    appointment.patient = patientId;

    // Update appointment with patient details
    appointment.nationalId = patient.nationalId;
    appointment.fullName = patient.fullName;
    appointment.phoneNumber = patient.phoneNumber;
    appointment.age = patient.age;
    appointment.gender = patient.gender;
    appointment.address = patient.address || appointment.address;
    appointment.occupation = patient.occupation || appointment.occupation;

    console.log(`Updating appointment ${appointment._id} with patient details:`, {
      nationalId: patient.nationalId,
      fullName: patient.fullName,
      phoneNumber: patient.phoneNumber
    });

    const updatedAppointment = await appointment.save();

    // Add the appointment to the patient's appointments list if not already there
    if (!patient.appointments.includes(updatedAppointment._id)) {
      await Patient.findByIdAndUpdate(patientId, {
        $push: { appointments: updatedAppointment._id }
      });
      console.log(`Added appointment ${updatedAppointment._id} to patient ${patientId}'s appointments list`);
    }

    // Return the updated appointment
    const populatedAppointment = await Appointment.findById(updatedAppointment._id)
      .populate('patient', 'nationalId fullName phoneNumber gender age address occupation');

    res.json(populatedAppointment);
  } catch (error) {
    console.error('Error updating appointment patient:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update an appointment
const updateAppointment = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Find the appointment
    const appointment = await Appointment.findById(id);
    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Check if nationalId is being updated
    if (updates.nationalId) {
      console.log(`Updating nationalId for appointment ${id} to ${updates.nationalId}`);
    }

    // Check if doctor is being updated to a student
    if (updates.doctor && updates.doctorModel === 'Student') {
      // Find the student to get their name - doctor field contains the studentId
      const student = await Student.findOne({ studentId: updates.doctor });
      if (student) {
        updates.studentName = student.name;
        updates.studentId = student.studentId;
        console.log(`Setting student information for appointment ${id}: ${student.name} (${student.studentId})`);
      } else {
        console.log(`No student found with studentId ${updates.doctor} for appointment ${id}`);
      }
    }

    // Apply updates
    Object.keys(updates).forEach(key => {
      appointment[key] = updates[key];
    });

    const updatedAppointment = await appointment.save();
    console.log(`Appointment ${id} updated successfully`);

    // Return the updated appointment
    const populatedAppointment = await Appointment.findById(updatedAppointment._id)
      .populate('patient', 'nationalId fullName phoneNumber gender age address occupation');

    res.json(populatedAppointment);
  } catch (error) {
    console.error('Error updating appointment:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  createAppointment,
  getAvailableSlots,
  getAppointmentsByDoctor,
  getAppointmentsByPatient,
  getAllAppointments,
  deleteAppointment,
  downloadSchedule,
  remindPatients,
  createPatientAppointment,
  getUniversityAvailableSlots,
  assignAppointment,
  updateAppointmentPatient,
  updateAppointment,
};