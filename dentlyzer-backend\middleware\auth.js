const jwt = require('jsonwebtoken');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const config = require('../config/config');

module.exports = async (req, res, next) => {
  try {
    // Get token from header
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    // Verify token
    const decoded = jwt.verify(token, config.JWT_SECRET);
    if (!decoded.id || !decoded.role) {
      return res.status(401).json({ message: 'Invalid token payload' });
    }

    // Fetch user based on role
    let user;
    if (decoded.role === 'student') {
      user = await FirestoreHelpers.findById(COLLECTIONS.STUDENTS, decoded.id);
    } else if (decoded.role === 'supervisor') {
      user = await FirestoreHelpers.findById(COLLECTIONS.SUPERVISORS, decoded.id);
    } else if (decoded.role === 'admin') {
      user = await FirestoreHelpers.findById(COLLECTIONS.ADMINS, decoded.id);
    } else if (decoded.role === 'superadmin') {
      user = await FirestoreHelpers.findById(COLLECTIONS.CONFIGS, decoded.id);
    } else if (decoded.role === 'assistant') {
      user = await FirestoreHelpers.findById(COLLECTIONS.ASSISTANTS, decoded.id);
    }

    if (!user) {
      console.log(`User not found for id: ${decoded.id}, role: ${decoded.role}`);
      return res.status(401).json({ message: 'User not found' });
    }

    // Set req.user with consistent fields
    req.user = {
      id: user.id,
      role: decoded.role,
      name: user.name || decoded.name || 'Unknown',
      email: user.email || decoded.email,
      university: user.university || decoded.university || '',
      studentId: decoded.role === 'student' ? user.studentId || user.id : undefined,
      dentistId: decoded.role === 'assistant' ? user.dentistId : undefined,
      affiliation: decoded.role === 'assistant' ? user.affiliation : undefined
    };

    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({ message: 'Token is not valid' });
  }
};