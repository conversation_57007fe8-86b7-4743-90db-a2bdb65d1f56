/*! firebase-admin v13.4.0 */
/*!
 * Copyright 2021 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { App } from '../app/index';
/**
 * The `Installations` service for the current app.
 */
export declare class Installations {
    private app_;
    private requestHandler;
    /**
     * Deletes the specified installation ID and the associated data from Firebase.
     *
     * @param fid - The Firebase installation ID to be deleted.
     *
     * @returns A promise fulfilled when the installation ID is deleted.
     */
    deleteInstallation(fid: string): Promise<void>;
    /**
     * Returns the app associated with this Installations instance.
     *
     * @returns The app associated with this Installations instance.
     */
    get app(): App;
}
