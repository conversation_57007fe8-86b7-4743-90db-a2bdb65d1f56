const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const connectDB = require('./config/db');
const Student = require('./models/Student');
const Supervisor = require('./models/Supervisor');
const Admin = require('./models/Admin');
const Superadmin = require('./models/Superadmin');
const Assistant = require('./models/Assistant');
const Dentist = require('./models/Dentist');
const Patient = require('./models/Patient');
const Appointment = require('./models/Appointment');
const TeethChart = require('./models/TeethChart');
const Review = require('./models/Review');
const ProcedureRequest = require('./models/ProcedureRequest');
const LabRequest = require('./models/LabRequest');
const News = require('./models/News');

// Connect to MongoDB
connectDB();

console.log('🌱 ODenta Final Seed - Comprehensive Database Seeding');
console.log('='.repeat(60));

const seedData = async () => {
  try {
    // Clear existing data
    await Student.deleteMany({});
    await Supervisor.deleteMany({});
    await Admin.deleteMany({});
    await Superadmin.deleteMany({});
    await Assistant.deleteMany({});
    await Dentist.deleteMany({});
    await Patient.deleteMany({});
    await Appointment.deleteMany({});
    await TeethChart.deleteMany({});
    await Review.deleteMany({});
    await ProcedureRequest.deleteMany({});
    await LabRequest.deleteMany({});
    await News.deleteMany({});
    console.log('Database cleared');

    // Seed Students
    const students = [
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('STaiu-2025', 10),
        name: 'Ahmed Hassan',
        studentId: 'AIU001',
        role: 'student',
        university: 'AIU',
        patients: [],
        reviews: [],
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('STaiu-2025', 10),
        name: 'Sara Mohamed',
        studentId: 'AIU002',
        role: 'student',
        university: 'AIU',
        patients: [],
        reviews: [],
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('STaiu-2025', 10),
        name: 'Omar Ali',
        studentId: 'AIU003',
        role: 'student',
        university: 'AIU',
        patients: [],
        reviews: [],
      }
    ];
    const savedStudents = await Student.insertMany(students);
    console.log('Students seeded');

    // Seed Supervisors
    const supervisors = [
      {
        email: '<EMAIL>',
        password: 'SVaiu-2025',
        name: 'Dr. Mahmoud Farouk',
        role: 'supervisor',
        university: 'AIU',
        pendingReviews: [],
        completedReviews: [],
      },
      {
        email: '<EMAIL>',
        password: 'SVaiu-2025',
        name: 'Dr. Fatma Abdel Rahman',
        role: 'supervisor',
        university: 'AIU',
        pendingReviews: [],
        completedReviews: [],
      }
    ];
    const savedSupervisors = await Supervisor.insertMany(supervisors);
    console.log('Supervisors seeded');

    // Seed Admins
    const admins = [
      {
        email: '<EMAIL>',
        password: 'Aaiu-2025',
        name: 'Dr. Khaled Ibrahim',
        role: 'admin',
        university: 'AIU',
      },
      {
        email: '<EMAIL>',
        password: 'Aaiu-2025',
        name: 'Dr. Nadia Saleh',
        role: 'admin',
        university: 'AIU',
      }
    ];
    const savedAdmins = await Admin.insertMany(admins);
    console.log('Admins seeded');

    // Seed Superadmin
    const superadmin = new Superadmin({
      email: '<EMAIL>',
      password: 'super-2025',
      name: 'Super Admin',
      role: 'superadmin',
    });
    await superadmin.save();
    console.log('Superadmin seeded');

    // Seed Assistants
    const assistants = [
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('ASTaiu-2025', 10),
        name: 'Mona Youssef',
        role: 'assistant',
        university: 'AIU',
        affiliation: {
          type: 'university',
          id: 'AIU'
        }
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('ASTaiu-2025', 10),
        name: 'Heba Mostafa',
        role: 'assistant',
        university: 'AIU',
        affiliation: {
          type: 'university',
          id: 'AIU'
        }
      }
    ];
    const savedAssistants = await Assistant.insertMany(assistants);
    console.log('Assistants seeded');

    // Seed Patients
    const patients = [
      {
        nationalId: '12345678901234',
        drId: 'AIU001',
        fullName: 'Mohamed Ahmed Ali',
        age: 28,
        phoneNumber: '+201234567890',
        gender: 'male',
        address: '123 Nile Street, Cairo, Egypt',
        occupation: 'Engineer',
        medicalInfo: {
          chronicDiseases: ['Diabetes'],
          recentSurgicalProcedures: 'None',
          currentMedications: 'Metformin',
          chiefComplaint: 'Tooth pain in upper right molar'
        },
        xrays: [],
        galleryImages: []
      },
      {
        nationalId: '23456789012345',
        drId: 'AIU001',
        fullName: 'Fatma Hassan Mohamed',
        age: 35,
        phoneNumber: '+201234567891',
        gender: 'female',
        address: '456 Alexandria Road, Giza, Egypt',
        occupation: 'Teacher',
        medicalInfo: {
          chronicDiseases: [],
          recentSurgicalProcedures: 'Appendectomy 2 years ago',
          currentMedications: 'None',
          chiefComplaint: 'Routine dental checkup'
        },
        xrays: [],
        galleryImages: []
      },
      {
        nationalId: '34567890123456',
        drId: 'AIU002',
        fullName: 'Ahmed Mahmoud Youssef',
        age: 42,
        phoneNumber: '+201234567892',
        gender: 'male',
        address: '789 Tahrir Square, Cairo, Egypt',
        occupation: 'Doctor',
        medicalInfo: {
          chronicDiseases: ['Hypertension'],
          recentSurgicalProcedures: 'None',
          currentMedications: 'Lisinopril',
          chiefComplaint: 'Gum bleeding and sensitivity'
        },
        xrays: [],
        galleryImages: []
      },
      {
        nationalId: '45678901234567',
        drId: 'AIU002',
        fullName: 'Nour Abdel Rahman',
        age: 25,
        phoneNumber: '+201234567893',
        gender: 'female',
        address: '321 Maadi Street, Cairo, Egypt',
        occupation: 'Student',
        medicalInfo: {
          chronicDiseases: [],
          recentSurgicalProcedures: 'None',
          currentMedications: 'None',
          chiefComplaint: 'Wisdom tooth extraction needed'
        },
        xrays: [],
        galleryImages: []
      },
      {
        nationalId: '56789012345678',
        drId: 'AIU003',
        fullName: 'Khaled Mostafa Ibrahim',
        age: 38,
        phoneNumber: '+201234567894',
        gender: 'male',
        address: '654 Heliopolis Avenue, Cairo, Egypt',
        occupation: 'Businessman',
        medicalInfo: {
          chronicDiseases: [],
          recentSurgicalProcedures: 'None',
          currentMedications: 'None',
          chiefComplaint: 'Dental implant consultation'
        },
        xrays: [],
        galleryImages: []
      },
      {
        nationalId: '67890123456789',
        drId: 'AIU003',
        fullName: 'Yasmin Farouk Said',
        age: 30,
        phoneNumber: '+201234567895',
        gender: 'female',
        address: '987 Zamalek Street, Cairo, Egypt',
        occupation: 'Lawyer',
        medicalInfo: {
          chronicDiseases: [],
          recentSurgicalProcedures: 'None',
          currentMedications: 'Birth control pills',
          chiefComplaint: 'Teeth whitening and cleaning'
        },
        xrays: [],
        galleryImages: []
      }
    ];
    const savedPatients = await Patient.insertMany(patients);
    console.log('Patients seeded');

    // Seed Appointments
    const appointments = [
      {
        date: new Date('2025-01-15T10:00:00Z'),
        time: '10:00',
        type: 'Consultation',
        notes: 'Initial consultation for tooth pain',
        status: 'pending',
        patient: savedPatients[0]._id,
        doctor: 'AIU001',
        doctorModel: 'Student',
        chiefComplaint: 'Tooth pain in upper right molar',
        treatment: '',
        duration: 60,
        university: 'AIU',
        fullName: 'Mohamed Ahmed Ali',
        phoneNumber: '+201234567890',
        age: 28,
        nationalId: '12345678901234',
        occupation: 'Engineer',
        address: '123 Nile Street, Cairo, Egypt',
        studentName: 'Ahmed Hassan',
        studentId: 'AIU001'
      },
      {
        date: new Date('2025-01-16T14:00:00Z'),
        time: '14:00',
        type: 'Cleaning',
        notes: 'Routine dental cleaning',
        status: 'completed',
        patient: savedPatients[1]._id,
        doctor: 'AIU001',
        doctorModel: 'Student',
        chiefComplaint: 'Routine dental checkup',
        treatment: 'Professional cleaning completed',
        duration: 45,
        university: 'AIU',
        fullName: 'Fatma Hassan Mohamed',
        phoneNumber: '+201234567891',
        age: 35,
        nationalId: '23456789012345',
        occupation: 'Teacher',
        address: '456 Alexandria Road, Giza, Egypt',
        studentName: 'Ahmed Hassan',
        studentId: 'AIU001'
      },
      {
        date: new Date('2025-01-17T09:00:00Z'),
        time: '09:00',
        type: 'Treatment',
        notes: 'Periodontal treatment',
        status: 'pending',
        patient: savedPatients[2]._id,
        doctor: 'AIU002',
        doctorModel: 'Student',
        chiefComplaint: 'Gum bleeding and sensitivity',
        treatment: '',
        duration: 90,
        university: 'AIU',
        fullName: 'Ahmed Mahmoud Youssef',
        phoneNumber: '+201234567892',
        age: 42,
        nationalId: '34567890123456',
        occupation: 'Doctor',
        address: '789 Tahrir Square, Cairo, Egypt',
        studentName: 'Sara Mohamed',
        studentId: 'AIU002'
      },
      {
        date: new Date('2025-01-18T11:00:00Z'),
        time: '11:00',
        type: 'Surgery',
        notes: 'Wisdom tooth extraction',
        status: 'pending',
        patient: savedPatients[3]._id,
        doctor: 'AIU002',
        doctorModel: 'Student',
        chiefComplaint: 'Wisdom tooth extraction needed',
        treatment: '',
        duration: 120,
        university: 'AIU',
        fullName: 'Nour Abdel Rahman',
        phoneNumber: '+201234567893',
        age: 25,
        nationalId: '45678901234567',
        occupation: 'Student',
        address: '321 Maadi Street, Cairo, Egypt',
        studentName: 'Sara Mohamed',
        studentId: 'AIU002'
      },
      {
        date: new Date('2025-01-19T15:00:00Z'),
        time: '15:00',
        type: 'Consultation',
        notes: 'Implant consultation',
        status: 'completed',
        patient: savedPatients[4]._id,
        doctor: 'AIU003',
        doctorModel: 'Student',
        chiefComplaint: 'Dental implant consultation',
        treatment: 'Consultation completed, treatment plan discussed',
        duration: 60,
        university: 'AIU',
        fullName: 'Khaled Mostafa Ibrahim',
        phoneNumber: '+201234567894',
        age: 38,
        nationalId: '56789012345678',
        occupation: 'Businessman',
        address: '654 Heliopolis Avenue, Cairo, Egypt',
        studentName: 'Omar Ali',
        studentId: 'AIU003'
      }
    ];
    const savedAppointments = await Appointment.insertMany(appointments);
    console.log('Appointments seeded');

    // Seed Procedure Requests
    const procedureRequests = [
      {
        studentId: 'AIU001',
        studentName: 'Ahmed Hassan',
        procedureType: 'Periodontics',
        patientNationalId: '12345678901234',
        patientName: 'Mohamed Ahmed Ali',
        notes: 'Patient requires periodontal treatment for gum disease',
        status: 'pending',
        requestDate: new Date('2025-01-10T08:00:00Z')
      },
      {
        studentId: 'AIU002',
        studentName: 'Sara Mohamed',
        procedureType: 'Oral Surgery',
        patientNationalId: '45678901234567',
        patientName: 'Nour Abdel Rahman',
        notes: 'Wisdom tooth extraction required',
        status: 'approved',
        requestDate: new Date('2025-01-12T09:00:00Z'),
        responseDate: new Date('2025-01-13T10:00:00Z'),
        responseNotes: 'Approved for extraction. Schedule surgery appointment.',
        responderId: '<EMAIL>',
        responderName: 'Mona Youssef'
      },
      {
        studentId: 'AIU003',
        studentName: 'Omar Ali',
        procedureType: 'Fixed Prosthodontics',
        patientNationalId: '56789012345678',
        patientName: 'Khaled Mostafa Ibrahim',
        notes: 'Patient needs crown placement on tooth #14',
        status: 'pending',
        requestDate: new Date('2025-01-14T11:00:00Z')
      },
      {
        studentId: 'AIU001',
        studentName: 'Ahmed Hassan',
        procedureType: 'Endodontics',
        patientNationalId: '23456789012345',
        patientName: 'Fatma Hassan Mohamed',
        notes: 'Root canal treatment needed for tooth #16',
        status: 'rejected',
        requestDate: new Date('2025-01-11T14:00:00Z'),
        responseDate: new Date('2025-01-12T15:00:00Z'),
        responseNotes: 'Patient needs further evaluation before endodontic treatment',
        responderId: '<EMAIL>',
        responderName: 'Heba Mostafa'
      }
    ];
    const savedProcedureRequests = await ProcedureRequest.insertMany(procedureRequests);
    console.log('Procedure Requests seeded');

    // Seed Lab Requests
    const labRequests = [
      {
        studentId: 'AIU001',
        studentName: 'Ahmed Hassan',
        university: 'AIU',
        patientId: '12345678901234',
        patientName: 'Mohamed Ahmed Ali',
        labType: 'university',
        status: 'pending',
        submitDate: new Date('2025-01-13T10:00:00Z'),
        notes: 'Crown fabrication for tooth #14'
      },
      {
        studentId: 'AIU002',
        studentName: 'Sara Mohamed',
        university: 'AIU',
        patientId: '34567890123456',
        patientName: 'Ahmed Mahmoud Youssef',
        labType: 'outside',
        status: 'approved',
        submitDate: new Date('2025-01-12T14:00:00Z'),
        responseDate: new Date('2025-01-13T09:00:00Z'),
        responseNotes: 'Approved for external lab processing',
        responderId: '<EMAIL>',
        responderName: 'Mona Youssef',
        notes: 'Partial denture fabrication'
      },
      {
        studentId: 'AIU003',
        studentName: 'Omar Ali',
        university: 'AIU',
        patientId: '67890123456789',
        patientName: 'Yasmin Farouk Said',
        labType: 'university',
        status: 'completed',
        submitDate: new Date('2025-01-10T11:00:00Z'),
        responseDate: new Date('2025-01-11T16:00:00Z'),
        responseNotes: 'Lab work completed successfully',
        responderId: '<EMAIL>',
        responderName: 'Heba Mostafa',
        notes: 'Teeth whitening trays'
      }
    ];
    const savedLabRequests = await LabRequest.insertMany(labRequests);
    console.log('Lab Requests seeded');

    // Seed Reviews
    const reviews = [
      {
        patientId: {
          nationalId: '12345678901234',
          fullName: 'Mohamed Ahmed Ali'
        },
        studentId: savedStudents[0]._id,
        studentName: 'Ahmed Hassan',
        procedureType: 'Periodontics',
        status: 'pending',
        comment: 'Student completed initial periodontal assessment',
        note: 'Good technique demonstrated, needs supervisor review',
        reviewSteps: [
          {
            stepName: 'Initial Assessment',
            description: 'Patient examination and diagnosis',
            isCompleted: true,
            completedDate: new Date('2025-01-15T10:30:00Z')
          },
          {
            stepName: 'Treatment Planning',
            description: 'Develop comprehensive treatment plan',
            isCompleted: true,
            completedDate: new Date('2025-01-15T11:00:00Z')
          },
          {
            stepName: 'Procedure Execution',
            description: 'Perform periodontal treatment',
            isCompleted: false
          }
        ],
        submittedDate: new Date('2025-01-15T12:00:00Z')
      },
      {
        patientId: {
          nationalId: '23456789012345',
          fullName: 'Fatma Hassan Mohamed'
        },
        studentId: savedStudents[0]._id,
        studentName: 'Ahmed Hassan',
        supervisorId: savedSupervisors[0]._id,
        supervisorName: 'Dr. Mahmoud Farouk',
        procedureType: 'Operative',
        procedureQuality: 4,
        patientInteraction: 5,
        status: 'accepted',
        comment: 'Excellent work on dental cleaning procedure',
        note: 'Student showed good clinical skills and patient communication',
        reviewSteps: [
          {
            stepName: 'Patient Preparation',
            description: 'Prepare patient for cleaning procedure',
            isCompleted: true,
            completedDate: new Date('2025-01-16T13:30:00Z')
          },
          {
            stepName: 'Cleaning Procedure',
            description: 'Perform professional dental cleaning',
            isCompleted: true,
            completedDate: new Date('2025-01-16T14:30:00Z')
          },
          {
            stepName: 'Post-procedure Care',
            description: 'Provide post-cleaning instructions',
            isCompleted: true,
            completedDate: new Date('2025-01-16T14:45:00Z')
          }
        ],
        submittedDate: new Date('2025-01-16T15:00:00Z'),
        reviewedDate: new Date('2025-01-17T09:00:00Z')
      },
      {
        patientId: {
          nationalId: '34567890123456',
          fullName: 'Ahmed Mahmoud Youssef'
        },
        studentId: savedStudents[1]._id,
        studentName: 'Sara Mohamed',
        procedureType: 'Periodontics',
        status: 'pending',
        comment: 'Periodontal treatment in progress',
        note: 'Student needs to complete scaling and root planing',
        reviewSteps: [
          {
            stepName: 'Periodontal Assessment',
            description: 'Comprehensive periodontal examination',
            isCompleted: true,
            completedDate: new Date('2025-01-17T09:15:00Z')
          },
          {
            stepName: 'Scaling and Root Planing',
            description: 'Deep cleaning procedure',
            isCompleted: false
          }
        ],
        submittedDate: new Date('2025-01-17T10:00:00Z')
      }
    ];
    const savedReviews = await Review.insertMany(reviews);
    console.log('Reviews seeded');

    // Seed News
    const news = [
      {
        title: {
          en: 'New Dental Equipment Arrival',
          ar: 'وصول معدات طب أسنان جديدة'
        },
        content: {
          en: 'We are excited to announce the arrival of new state-of-the-art dental equipment at our university clinic. This includes advanced digital X-ray machines and modern dental chairs that will enhance the learning experience for our students.',
          ar: 'نحن متحمسون للإعلان عن وصول معدات طب الأسنان الحديثة إلى عيادة الجامعة. تشمل هذه المعدات أجهزة الأشعة السينية الرقمية المتقدمة وكراسي الأسنان الحديثة التي ستعزز تجربة التعلم لطلابنا.'
        },
        university: 'AIU',
        isGlobal: false,
        recipientType: 'university',
        recipientId: 'AIU'
      },
      {
        title: {
          en: 'Upcoming Dental Conference 2025',
          ar: 'مؤتمر طب الأسنان القادم 2025'
        },
        content: {
          en: 'Join us for the Annual Dental Conference 2025 featuring renowned speakers from around the world. Topics will include latest advances in dental technology, AI in dentistry, and modern treatment techniques.',
          ar: 'انضم إلينا في المؤتمر السنوي لطب الأسنان 2025 والذي يضم متحدثين مشهورين من جميع أنحاء العالم. ستشمل المواضيع أحدث التطورات في تكنولوجيا طب الأسنان والذكاء الاصطناعي في طب الأسنان وتقنيات العلاج الحديثة.'
        },
        university: null,
        isGlobal: true,
        recipientType: null,
        recipientId: null
      },
      {
        title: {
          en: 'Student Achievement Recognition',
          ar: 'تقدير إنجازات الطلاب'
        },
        content: {
          en: 'Congratulations to our outstanding dental students who have shown exceptional performance in their clinical rotations. Their dedication to patient care and academic excellence is truly commendable.',
          ar: 'تهانينا لطلاب طب الأسنان المتميزين الذين أظهروا أداءً استثنائياً في دوراتهم السريرية. إن تفانيهم في رعاية المرضى والتميز الأكاديمي جدير بالثناء حقاً.'
        },
        university: 'AIU',
        isGlobal: false,
        recipientType: 'university',
        recipientId: 'AIU'
      },
      {
        title: {
          en: 'New AI Diagnostic System Implementation',
          ar: 'تطبيق نظام التشخيص بالذكاء الاصطناعي الجديد'
        },
        content: {
          en: 'We are proud to introduce our new AI-powered diagnostic system that will assist students and supervisors in accurate dental diagnosis. This cutting-edge technology represents our commitment to modern dental education.',
          ar: 'نحن فخورون بتقديم نظام التشخيص الجديد المدعوم بالذكاء الاصطناعي والذي سيساعد الطلاب والمشرفين في التشخيص الدقيق للأسنان. تمثل هذه التكنولوجيا المتطورة التزامنا بالتعليم الحديث لطب الأسنان.'
        },
        university: 'AIU',
        isGlobal: false,
        recipientType: 'university',
        recipientId: 'AIU'
      }
    ];
    const savedNews = await News.insertMany(news);
    console.log('News seeded');

    console.log('Database seeded successfully with comprehensive data!');
    console.log('='.repeat(50));
    console.log('SEEDED ACCOUNTS:');
    console.log('='.repeat(50));
    console.log('Students:');
    console.log('  - <EMAIL> / STaiu-2025 (Ahmed Hassan - AIU001)');
    console.log('  - <EMAIL> / STaiu-2025 (Sara Mohamed - AIU002)');
    console.log('  - <EMAIL> / STaiu-2025 (Omar Ali - AIU003)');
    console.log('');
    console.log('Supervisors:');
    console.log('  - <EMAIL> / SVaiu-2025 (Dr. Mahmoud Farouk)');
    console.log('  - <EMAIL> / SVaiu-2025 (Dr. Fatma Abdel Rahman)');
    console.log('');
    console.log('Admins:');
    console.log('  - <EMAIL> / Aaiu-2025 (Dr. Khaled Ibrahim)');
    console.log('  - <EMAIL> / Aaiu-2025 (Dr. Nadia Saleh)');
    console.log('');
    console.log('Assistants:');
    console.log('  - <EMAIL> / ASTaiu-2025 (Mona Youssef)');
    console.log('  - <EMAIL> / ASTaiu-2025 (Heba Mostafa)');
    console.log('');
    console.log('Superadmin:');
    console.log('  - <EMAIL> / super-2025 (Super Admin)');
    console.log('='.repeat(50));
    console.log(`Total seeded: ${savedStudents.length} students, ${savedSupervisors.length} supervisors, ${savedAdmins.length} admins, ${savedAssistants.length} assistants, ${savedPatients.length} patients, ${savedAppointments.length} appointments, ${savedProcedureRequests.length} procedure requests, ${savedLabRequests.length} lab requests, ${savedReviews.length} reviews, ${savedNews.length} news items`);
    console.log('='.repeat(50));

    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedData();