const Joi = require('joi');
const { commonSchemas, COLLECTIONS } = require('./index');

// Assistant validation schema for Firebase
const assistantSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  email: commonSchemas.email,
  password: Joi.string().required(),
  plainPassword: Joi.string().optional(), // Store unhashed password if needed
  name: Joi.string().required(),
  role: Joi.string().default('assistant'),
  university: Joi.string().required(),
  dentistId: Joi.string().optional(),
  affiliation: Joi.object({
    id: Joi.string(),
    name: Joi.string()
  }).optional(),
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// Assistant creation schema (without ID)
const createAssistantSchema = assistantSchema.fork(['id'], (schema) => schema.forbidden());

// Assistant update schema (partial)
const updateAssistantSchema = assistantSchema.fork(
  ['email', 'name', 'university'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Assistant login schema
const loginAssistantSchema = Joi.object({
  email: commonSchemas.email,
  password: Joi.string().required()
});

// Helper functions for Assistant operations
const AssistantHelpers = {
  // Validate assistant data
  validateCreate: (data) => createAssistantSchema.validate(data),
  validateUpdate: (data) => updateAssistantSchema.validate(data),
  validateLogin: (data) => loginAssistantSchema.validate(data),
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    if (prepared.createdAt && typeof prepared.createdAt === 'string') {
      prepared.createdAt = new Date(prepared.createdAt);
    }
    if (prepared.updatedAt && typeof prepared.updatedAt === 'string') {
      prepared.updatedAt = new Date(prepared.updatedAt);
    }
    
    return prepared;
  },
  
  // Remove sensitive data for client response
  sanitizeForResponse: (assistantData) => {
    const sanitized = { ...assistantData };
    delete sanitized.password;
    delete sanitized.plainPassword;
    return sanitized;
  }
};

module.exports = {
  assistantSchema,
  createAssistantSchema,
  updateAssistantSchema,
  loginAssistantSchema,
  AssistantHelpers,
  COLLECTION_NAME: COLLECTIONS.ASSISTANTS
};
