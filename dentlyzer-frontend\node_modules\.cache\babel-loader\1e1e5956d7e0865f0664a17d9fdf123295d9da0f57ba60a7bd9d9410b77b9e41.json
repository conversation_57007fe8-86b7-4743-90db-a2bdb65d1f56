{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport React, { isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nfunction hasChildren(node, checkLength) {\n  if (!node) return false;\n  const base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n}\nfunction getChildren(node) {\n  if (!node) return [];\n  const children = node.props ? node.props.children : node.children;\n  return node.props && node.props.i18nIsDynamicList ? getAsArray(children) : children;\n}\nfunction hasValidReactChildren(children) {\n  if (Object.prototype.toString.call(children) !== '[object Array]') return false;\n  return children.every(child => isValidElement(child));\n}\nfunction getAsArray(data) {\n  return Array.isArray(data) ? data : [data];\n}\nfunction mergeProps(source, target) {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n}\nexport function nodesToString(children, i18nOptions) {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (typeof child === 'string') {\n      stringNode += `${child}`;\n    } else if (isValidElement(child)) {\n      const childPropsCount = Object.keys(child.props).length;\n      const shouldKeepChild = keepArray.indexOf(child.type) > -1;\n      const childChildren = child.props.children;\n      if (!childChildren && shouldKeepChild && childPropsCount === 0) {\n        stringNode += `<${child.type}/>`;\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount !== 0)) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (child.props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (shouldKeepChild && childPropsCount === 1 && typeof childChildren === 'string') {\n        stringNode += `<${child.type}>${childChildren}</${child.type}>`;\n      } else {\n        const content = nodesToString(childChildren, i18nOptions);\n        stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      }\n    } else if (child === null) {\n      warn(`Trans: the passed in value is invalid - seems you passed in a null child.`);\n    } else if (typeof child === 'object') {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n      } else {\n        warn(`react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.`, child);\n      }\n    } else {\n      warn(`Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.`, child);\n    }\n  });\n  return stringNode;\n}\nfunction renderNodes(children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  function getData(childs) {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (typeof child === 'string') return;\n      if (hasChildren(child)) getData(getChildren(child));else if (typeof child === 'object' && !isValidElement(child)) Object.assign(data, child);\n    });\n  }\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  function renderInner(child, node, rootReactNode) {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props && child.props.i18nIsDynamicList ? childs : mappedChildren;\n  }\n  function pushTranslatedJSX(child, inner, mem, i, isVoid) {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return React.createElement(c.type, _extends({}, props, {\n          key: i,\n          ref: c.ref\n        }, isVoid ? {} : {\n          children: inner\n        }));\n      }));\n    }\n  }\n  function mapAST(reactNode, astNode, rootReactNode) {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && typeof child === 'object' && child.dummy && !isElement;\n        const isKnownComponent = typeof children === 'object' && children !== null && Object.hasOwnProperty.call(children, node.name);\n        if (typeof child === 'string') {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (typeof child === 'object' && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  }\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n}\nexport function Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  if (context) tOptions.context = context;\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...(i18n.options && i18n.options.react)\n  };\n  let namespaces = ns || t.ns || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options && i18n.options.interpolation && i18n.options.interpolation.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  if (components) {\n    Object.keys(components).forEach(c => {\n      const comp = components[c];\n      if (typeof comp.type === 'function' || !comp.props || !comp.props.children || translation.indexOf(`${c}/>`) < 0 && translation.indexOf(`${c} />`) < 0) return;\n      function Componentized() {\n        return React.createElement(React.Fragment, null, comp);\n      }\n      components[c] = React.createElement(Componentized, null);\n    });\n  }\n  const content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}", "map": {"version": 3, "names": ["_extends", "React", "isValidElement", "cloneElement", "createElement", "Children", "HTML", "warn", "warnOnce", "getDefaults", "getI18n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "checkLength", "base", "props", "children", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18nIsDynamicList", "getAsArray", "hasValidReactChildren", "Object", "prototype", "toString", "call", "every", "child", "data", "Array", "isArray", "mergeProps", "source", "target", "newTarget", "assign", "nodesToString", "i18nOptions", "stringNode", "childrenA<PERSON>y", "keepArray", "transSupportBasicHtmlNodes", "transKeepBasicHtmlNodesFor", "for<PERSON>ach", "childIndex", "childPropsCount", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "format", "clone", "value", "renderNodes", "targetString", "i18n", "combinedTOpts", "shouldUnescape", "emptyChildrenButNeedsHandling", "RegExp", "map", "keep", "join", "test", "getData", "childs", "ast", "parse", "opts", "renderInner", "rootReactNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapAST", "pushTranslatedJSX", "inner", "mem", "i", "isVoid", "dummy", "push", "key", "undefined", "c", "ref", "reactNode", "astNode", "reactNodes", "astNodes", "reduce", "translationContent", "services", "interpolator", "interpolate", "language", "tmp", "parseInt", "name", "attrs", "isElement", "isValidTranslationWithChildren", "voidElement", "isEmptyTransWithHTML", "isKnownComponent", "hasOwnProperty", "Number", "isNaN", "parseFloat", "wrapTextNodes", "transWrapTextNodes", "unescape", "result", "Trans", "_ref", "count", "parent", "i18nKey", "context", "tOptions", "values", "defaults", "components", "ns", "i18nFromProps", "t", "tFromProps", "additionalProps", "bind", "k", "reactI18nextOptions", "options", "react", "namespaces", "defaultNS", "nodeAsString", "defaultValue", "transEmptyNodeValue", "hashTransKey", "interpolation", "defaultVariables", "interpolationOverride", "prefix", "suffix", "translation", "comp", "Componentized", "Fragment", "useAsParent", "defaultTransParent"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/node_modules/react-i18next/dist/es/TransWithoutContext.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport React, { isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nfunction hasChildren(node, checkLength) {\n  if (!node) return false;\n  const base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n}\nfunction getChildren(node) {\n  if (!node) return [];\n  const children = node.props ? node.props.children : node.children;\n  return node.props && node.props.i18nIsDynamicList ? getAsArray(children) : children;\n}\nfunction hasValidReactChildren(children) {\n  if (Object.prototype.toString.call(children) !== '[object Array]') return false;\n  return children.every(child => isValidElement(child));\n}\nfunction getAsArray(data) {\n  return Array.isArray(data) ? data : [data];\n}\nfunction mergeProps(source, target) {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n}\nexport function nodesToString(children, i18nOptions) {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (typeof child === 'string') {\n      stringNode += `${child}`;\n    } else if (isValidElement(child)) {\n      const childPropsCount = Object.keys(child.props).length;\n      const shouldKeepChild = keepArray.indexOf(child.type) > -1;\n      const childChildren = child.props.children;\n      if (!childChildren && shouldKeepChild && childPropsCount === 0) {\n        stringNode += `<${child.type}/>`;\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount !== 0)) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (child.props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (shouldKeepChild && childPropsCount === 1 && typeof childChildren === 'string') {\n        stringNode += `<${child.type}>${childChildren}</${child.type}>`;\n      } else {\n        const content = nodesToString(childChildren, i18nOptions);\n        stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      }\n    } else if (child === null) {\n      warn(`Trans: the passed in value is invalid - seems you passed in a null child.`);\n    } else if (typeof child === 'object') {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n      } else {\n        warn(`react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.`, child);\n      }\n    } else {\n      warn(`Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.`, child);\n    }\n  });\n  return stringNode;\n}\nfunction renderNodes(children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  function getData(childs) {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (typeof child === 'string') return;\n      if (hasChildren(child)) getData(getChildren(child));else if (typeof child === 'object' && !isValidElement(child)) Object.assign(data, child);\n    });\n  }\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  function renderInner(child, node, rootReactNode) {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props && child.props.i18nIsDynamicList ? childs : mappedChildren;\n  }\n  function pushTranslatedJSX(child, inner, mem, i, isVoid) {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return React.createElement(c.type, _extends({}, props, {\n          key: i,\n          ref: c.ref\n        }, isVoid ? {} : {\n          children: inner\n        }));\n      }));\n    }\n  }\n  function mapAST(reactNode, astNode, rootReactNode) {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && typeof child === 'object' && child.dummy && !isElement;\n        const isKnownComponent = typeof children === 'object' && children !== null && Object.hasOwnProperty.call(children, node.name);\n        if (typeof child === 'string') {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (typeof child === 'object' && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  }\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n}\nexport function Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  if (context) tOptions.context = context;\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...(i18n.options && i18n.options.react)\n  };\n  let namespaces = ns || t.ns || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options && i18n.options.interpolation && i18n.options.interpolation.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  if (components) {\n    Object.keys(components).forEach(c => {\n      const comp = components[c];\n      if (typeof comp.type === 'function' || !comp.props || !comp.props.children || translation.indexOf(`${c}/>`) < 0 && translation.indexOf(`${c} />`) < 0) return;\n      function Componentized() {\n        return React.createElement(React.Fragment, null, comp);\n      }\n      components[c] = React.createElement(Componentized, null);\n    });\n  }\n  const content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,KAAK,IAAIC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACpF,OAAOC,IAAI,MAAM,sBAAsB;AACvC,SAASC,IAAI,EAAEC,QAAQ,QAAQ,YAAY;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,WAAWA,CAACC,IAAI,EAAEC,WAAW,EAAE;EACtC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EACvB,MAAME,IAAI,GAAGF,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC7D,IAAIH,WAAW,EAAE,OAAOC,IAAI,CAACG,MAAM,GAAG,CAAC;EACvC,OAAO,CAAC,CAACH,IAAI;AACf;AACA,SAASI,WAAWA,CAACN,IAAI,EAAE;EACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,MAAMI,QAAQ,GAAGJ,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EACjE,OAAOJ,IAAI,CAACG,KAAK,IAAIH,IAAI,CAACG,KAAK,CAACI,iBAAiB,GAAGC,UAAU,CAACJ,QAAQ,CAAC,GAAGA,QAAQ;AACrF;AACA,SAASK,qBAAqBA,CAACL,QAAQ,EAAE;EACvC,IAAIM,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACT,QAAQ,CAAC,KAAK,gBAAgB,EAAE,OAAO,KAAK;EAC/E,OAAOA,QAAQ,CAACU,KAAK,CAACC,KAAK,IAAIzB,cAAc,CAACyB,KAAK,CAAC,CAAC;AACvD;AACA,SAASP,UAAUA,CAACQ,IAAI,EAAE;EACxB,OAAOC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;AAC5C;AACA,SAASG,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAClC,MAAMC,SAAS,GAAG;IAChB,GAAGD;EACL,CAAC;EACDC,SAAS,CAACnB,KAAK,GAAGO,MAAM,CAACa,MAAM,CAACH,MAAM,CAACjB,KAAK,EAAEkB,MAAM,CAAClB,KAAK,CAAC;EAC3D,OAAOmB,SAAS;AAClB;AACA,OAAO,SAASE,aAAaA,CAACpB,QAAQ,EAAEqB,WAAW,EAAE;EACnD,IAAI,CAACrB,QAAQ,EAAE,OAAO,EAAE;EACxB,IAAIsB,UAAU,GAAG,EAAE;EACnB,MAAMC,aAAa,GAAGnB,UAAU,CAACJ,QAAQ,CAAC;EAC1C,MAAMwB,SAAS,GAAGH,WAAW,CAACI,0BAA0B,IAAIJ,WAAW,CAACK,0BAA0B,GAAGL,WAAW,CAACK,0BAA0B,GAAG,EAAE;EAChJH,aAAa,CAACI,OAAO,CAAC,CAAChB,KAAK,EAAEiB,UAAU,KAAK;IAC3C,IAAI,OAAOjB,KAAK,KAAK,QAAQ,EAAE;MAC7BW,UAAU,IAAI,GAAGX,KAAK,EAAE;IAC1B,CAAC,MAAM,IAAIzB,cAAc,CAACyB,KAAK,CAAC,EAAE;MAChC,MAAMkB,eAAe,GAAGvB,MAAM,CAACwB,IAAI,CAACnB,KAAK,CAACZ,KAAK,CAAC,CAACE,MAAM;MACvD,MAAM8B,eAAe,GAAGP,SAAS,CAACQ,OAAO,CAACrB,KAAK,CAACsB,IAAI,CAAC,GAAG,CAAC,CAAC;MAC1D,MAAMC,aAAa,GAAGvB,KAAK,CAACZ,KAAK,CAACC,QAAQ;MAC1C,IAAI,CAACkC,aAAa,IAAIH,eAAe,IAAIF,eAAe,KAAK,CAAC,EAAE;QAC9DP,UAAU,IAAI,IAAIX,KAAK,CAACsB,IAAI,IAAI;MAClC,CAAC,MAAM,IAAI,CAACC,aAAa,KAAK,CAACH,eAAe,IAAIF,eAAe,KAAK,CAAC,CAAC,EAAE;QACxEP,UAAU,IAAI,IAAIM,UAAU,MAAMA,UAAU,GAAG;MACjD,CAAC,MAAM,IAAIjB,KAAK,CAACZ,KAAK,CAACI,iBAAiB,EAAE;QACxCmB,UAAU,IAAI,IAAIM,UAAU,MAAMA,UAAU,GAAG;MACjD,CAAC,MAAM,IAAIG,eAAe,IAAIF,eAAe,KAAK,CAAC,IAAI,OAAOK,aAAa,KAAK,QAAQ,EAAE;QACxFZ,UAAU,IAAI,IAAIX,KAAK,CAACsB,IAAI,IAAIC,aAAa,KAAKvB,KAAK,CAACsB,IAAI,GAAG;MACjE,CAAC,MAAM;QACL,MAAME,OAAO,GAAGf,aAAa,CAACc,aAAa,EAAEb,WAAW,CAAC;QACzDC,UAAU,IAAI,IAAIM,UAAU,IAAIO,OAAO,KAAKP,UAAU,GAAG;MAC3D;IACF,CAAC,MAAM,IAAIjB,KAAK,KAAK,IAAI,EAAE;MACzBpB,IAAI,CAAC,2EAA2E,CAAC;IACnF,CAAC,MAAM,IAAI,OAAOoB,KAAK,KAAK,QAAQ,EAAE;MACpC,MAAM;QACJyB,MAAM;QACN,GAAGC;MACL,CAAC,GAAG1B,KAAK;MACT,MAAMmB,IAAI,GAAGxB,MAAM,CAACwB,IAAI,CAACO,KAAK,CAAC;MAC/B,IAAIP,IAAI,CAAC7B,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMqC,KAAK,GAAGF,MAAM,GAAG,GAAGN,IAAI,CAAC,CAAC,CAAC,KAAKM,MAAM,EAAE,GAAGN,IAAI,CAAC,CAAC,CAAC;QACxDR,UAAU,IAAI,KAAKgB,KAAK,IAAI;MAC9B,CAAC,MAAM;QACL/C,IAAI,CAAC,kJAAkJ,EAAEoB,KAAK,CAAC;MACjK;IACF,CAAC,MAAM;MACLpB,IAAI,CAAC,oKAAoK,EAAEoB,KAAK,CAAC;IACnL;EACF,CAAC,CAAC;EACF,OAAOW,UAAU;AACnB;AACA,SAASiB,WAAWA,CAACvC,QAAQ,EAAEwC,YAAY,EAAEC,IAAI,EAAEpB,WAAW,EAAEqB,aAAa,EAAEC,cAAc,EAAE;EAC7F,IAAIH,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE;EAClC,MAAMhB,SAAS,GAAGH,WAAW,CAACK,0BAA0B,IAAI,EAAE;EAC9D,MAAMkB,6BAA6B,GAAGJ,YAAY,IAAI,IAAIK,MAAM,CAACrB,SAAS,CAACsB,GAAG,CAACC,IAAI,IAAI,IAAIA,IAAI,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAACT,YAAY,CAAC;EAChI,IAAI,CAACxC,QAAQ,IAAI,CAAC4C,6BAA6B,IAAI,CAACD,cAAc,EAAE,OAAO,CAACH,YAAY,CAAC;EACzF,MAAM5B,IAAI,GAAG,CAAC,CAAC;EACf,SAASsC,OAAOA,CAACC,MAAM,EAAE;IACvB,MAAM5B,aAAa,GAAGnB,UAAU,CAAC+C,MAAM,CAAC;IACxC5B,aAAa,CAACI,OAAO,CAAChB,KAAK,IAAI;MAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,IAAIhB,WAAW,CAACgB,KAAK,CAAC,EAAEuC,OAAO,CAAChD,WAAW,CAACS,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACzB,cAAc,CAACyB,KAAK,CAAC,EAAEL,MAAM,CAACa,MAAM,CAACP,IAAI,EAAED,KAAK,CAAC;IAC9I,CAAC,CAAC;EACJ;EACAuC,OAAO,CAAClD,QAAQ,CAAC;EACjB,MAAMoD,GAAG,GAAG9D,IAAI,CAAC+D,KAAK,CAAC,MAAMb,YAAY,MAAM,CAAC;EAChD,MAAMc,IAAI,GAAG;IACX,GAAG1C,IAAI;IACP,GAAG8B;EACL,CAAC;EACD,SAASa,WAAWA,CAAC5C,KAAK,EAAEf,IAAI,EAAE4D,aAAa,EAAE;IAC/C,MAAML,MAAM,GAAGjD,WAAW,CAACS,KAAK,CAAC;IACjC,MAAM8C,cAAc,GAAGC,MAAM,CAACP,MAAM,EAAEvD,IAAI,CAACI,QAAQ,EAAEwD,aAAa,CAAC;IACnE,OAAOnD,qBAAqB,CAAC8C,MAAM,CAAC,IAAIM,cAAc,CAACxD,MAAM,KAAK,CAAC,IAAIU,KAAK,CAACZ,KAAK,IAAIY,KAAK,CAACZ,KAAK,CAACI,iBAAiB,GAAGgD,MAAM,GAAGM,cAAc;EAC/I;EACA,SAASE,iBAAiBA,CAAChD,KAAK,EAAEiD,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAEC,MAAM,EAAE;IACvD,IAAIpD,KAAK,CAACqD,KAAK,EAAE;MACfrD,KAAK,CAACX,QAAQ,GAAG4D,KAAK;MACtBC,GAAG,CAACI,IAAI,CAAC9E,YAAY,CAACwB,KAAK,EAAE;QAC3BuD,GAAG,EAAEJ;MACP,CAAC,EAAEC,MAAM,GAAGI,SAAS,GAAGP,KAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACLC,GAAG,CAACI,IAAI,CAAC,GAAG5E,QAAQ,CAACyD,GAAG,CAAC,CAACnC,KAAK,CAAC,EAAEyD,CAAC,IAAI;QACrC,MAAMrE,KAAK,GAAG;UACZ,GAAGqE,CAAC,CAACrE;QACP,CAAC;QACD,OAAOA,KAAK,CAACI,iBAAiB;QAC9B,OAAOlB,KAAK,CAACG,aAAa,CAACgF,CAAC,CAACnC,IAAI,EAAEjD,QAAQ,CAAC,CAAC,CAAC,EAAEe,KAAK,EAAE;UACrDmE,GAAG,EAAEJ,CAAC;UACNO,GAAG,EAAED,CAAC,CAACC;QACT,CAAC,EAAEN,MAAM,GAAG,CAAC,CAAC,GAAG;UACf/D,QAAQ,EAAE4D;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;IACL;EACF;EACA,SAASF,MAAMA,CAACY,SAAS,EAAEC,OAAO,EAAEf,aAAa,EAAE;IACjD,MAAMgB,UAAU,GAAGpE,UAAU,CAACkE,SAAS,CAAC;IACxC,MAAMG,QAAQ,GAAGrE,UAAU,CAACmE,OAAO,CAAC;IACpC,OAAOE,QAAQ,CAACC,MAAM,CAAC,CAACb,GAAG,EAAEjE,IAAI,EAAEkE,CAAC,KAAK;MACvC,MAAMa,kBAAkB,GAAG/E,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACmC,OAAO,IAAIM,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAClF,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACmC,OAAO,EAAEmB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;MACjL,IAAInF,IAAI,CAACqC,IAAI,KAAK,KAAK,EAAE;QACvB,IAAI+C,GAAG,GAAGR,UAAU,CAACS,QAAQ,CAACrF,IAAI,CAACsF,IAAI,EAAE,EAAE,CAAC,CAAC;QAC7C,IAAI1B,aAAa,CAACvD,MAAM,KAAK,CAAC,IAAI,CAAC+E,GAAG,EAAEA,GAAG,GAAGxB,aAAa,CAAC,CAAC,CAAC,CAAC5D,IAAI,CAACsF,IAAI,CAAC;QACzE,IAAI,CAACF,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;QAClB,MAAMrE,KAAK,GAAGL,MAAM,CAACwB,IAAI,CAAClC,IAAI,CAACuF,KAAK,CAAC,CAAClF,MAAM,KAAK,CAAC,GAAGc,UAAU,CAAC;UAC9DhB,KAAK,EAAEH,IAAI,CAACuF;QACd,CAAC,EAAEH,GAAG,CAAC,GAAGA,GAAG;QACb,MAAMI,SAAS,GAAGlG,cAAc,CAACyB,KAAK,CAAC;QACvC,MAAM0E,8BAA8B,GAAGD,SAAS,IAAIzF,WAAW,CAACC,IAAI,EAAE,IAAI,CAAC,IAAI,CAACA,IAAI,CAAC0F,WAAW;QAChG,MAAMC,oBAAoB,GAAG3C,6BAA6B,IAAI,OAAOjC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACqD,KAAK,IAAI,CAACoB,SAAS;QACpH,MAAMI,gBAAgB,GAAG,OAAOxF,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,IAAIM,MAAM,CAACmF,cAAc,CAAChF,IAAI,CAACT,QAAQ,EAAEJ,IAAI,CAACsF,IAAI,CAAC;QAC7H,IAAI,OAAOvE,KAAK,KAAK,QAAQ,EAAE;UAC7B,MAAM2B,KAAK,GAAGG,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACnE,KAAK,EAAE2C,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;UAChFlB,GAAG,CAACI,IAAI,CAAC3B,KAAK,CAAC;QACjB,CAAC,MAAM,IAAI3C,WAAW,CAACgB,KAAK,CAAC,IAAI0E,8BAA8B,EAAE;UAC/D,MAAMzB,KAAK,GAAGL,WAAW,CAAC5C,KAAK,EAAEf,IAAI,EAAE4D,aAAa,CAAC;UACrDG,iBAAiB,CAAChD,KAAK,EAAEiD,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAIyB,oBAAoB,EAAE;UAC/B,MAAM3B,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE5E,IAAI,CAACI,QAAQ,EAAEwD,aAAa,CAAC;UAC9DG,iBAAiB,CAAChD,KAAK,EAAEiD,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAI4B,MAAM,CAACC,KAAK,CAACC,UAAU,CAAChG,IAAI,CAACsF,IAAI,CAAC,CAAC,EAAE;UAC9C,IAAIM,gBAAgB,EAAE;YACpB,MAAM5B,KAAK,GAAGL,WAAW,CAAC5C,KAAK,EAAEf,IAAI,EAAE4D,aAAa,CAAC;YACrDG,iBAAiB,CAAChD,KAAK,EAAEiD,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAElE,IAAI,CAAC0F,WAAW,CAAC;UAC3D,CAAC,MAAM,IAAIjE,WAAW,CAACI,0BAA0B,IAAID,SAAS,CAACQ,OAAO,CAACpC,IAAI,CAACsF,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACtF,IAAItF,IAAI,CAAC0F,WAAW,EAAE;cACpBzB,GAAG,CAACI,IAAI,CAAC7E,aAAa,CAACQ,IAAI,CAACsF,IAAI,EAAE;gBAChChB,GAAG,EAAE,GAAGtE,IAAI,CAACsF,IAAI,IAAIpB,CAAC;cACxB,CAAC,CAAC,CAAC;YACL,CAAC,MAAM;cACL,MAAMF,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE5E,IAAI,CAACI,QAAQ,EAAEwD,aAAa,CAAC;cAC9DK,GAAG,CAACI,IAAI,CAAC7E,aAAa,CAACQ,IAAI,CAACsF,IAAI,EAAE;gBAChChB,GAAG,EAAE,GAAGtE,IAAI,CAACsF,IAAI,IAAIpB,CAAC;cACxB,CAAC,EAAEF,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,MAAM,IAAIhE,IAAI,CAAC0F,WAAW,EAAE;YAC3BzB,GAAG,CAACI,IAAI,CAAC,IAAIrE,IAAI,CAACsF,IAAI,KAAK,CAAC;UAC9B,CAAC,MAAM;YACL,MAAMtB,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE5E,IAAI,CAACI,QAAQ,EAAEwD,aAAa,CAAC;YAC9DK,GAAG,CAACI,IAAI,CAAC,IAAIrE,IAAI,CAACsF,IAAI,IAAItB,KAAK,KAAKhE,IAAI,CAACsF,IAAI,GAAG,CAAC;UACnD;QACF,CAAC,MAAM,IAAI,OAAOvE,KAAK,KAAK,QAAQ,IAAI,CAACyE,SAAS,EAAE;UAClD,MAAMjD,OAAO,GAAGvC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAG2E,kBAAkB,GAAG,IAAI;UAC5D,IAAIxC,OAAO,EAAE0B,GAAG,CAACI,IAAI,CAAC9B,OAAO,CAAC;QAChC,CAAC,MAAM;UACLwB,iBAAiB,CAAChD,KAAK,EAAEgE,kBAAkB,EAAEd,GAAG,EAAEC,CAAC,EAAElE,IAAI,CAACI,QAAQ,CAACC,MAAM,KAAK,CAAC,IAAI,CAAC0E,kBAAkB,CAAC;QACzG;MACF,CAAC,MAAM,IAAI/E,IAAI,CAACqC,IAAI,KAAK,MAAM,EAAE;QAC/B,MAAM4D,aAAa,GAAGxE,WAAW,CAACyE,kBAAkB;QACpD,MAAM3D,OAAO,GAAGQ,cAAc,GAAGtB,WAAW,CAAC0E,QAAQ,CAACtD,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAClF,IAAI,CAACuC,OAAO,EAAEmB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC,CAAC,GAAGtC,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAClF,IAAI,CAACuC,OAAO,EAAEmB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;QAC5M,IAAIc,aAAa,EAAE;UACjBhC,GAAG,CAACI,IAAI,CAAC7E,aAAa,CAACyG,aAAa,EAAE;YACpC3B,GAAG,EAAE,GAAGtE,IAAI,CAACsF,IAAI,IAAIpB,CAAC;UACxB,CAAC,EAAE3B,OAAO,CAAC,CAAC;QACd,CAAC,MAAM;UACL0B,GAAG,CAACI,IAAI,CAAC9B,OAAO,CAAC;QACnB;MACF;MACA,OAAO0B,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EACA,MAAMmC,MAAM,GAAGtC,MAAM,CAAC,CAAC;IACrBM,KAAK,EAAE,IAAI;IACXhE,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC,CAAC,EAAEoD,GAAG,EAAEhD,UAAU,CAACJ,QAAQ,IAAI,EAAE,CAAC,CAAC;EACpC,OAAOE,WAAW,CAAC8F,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,IAAI;IACFlG,QAAQ;IACRmG,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,QAAQ,GAAG,CAAC,CAAC;IACbC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,EAAE;IACFlE,IAAI,EAAEmE,aAAa;IACnBC,CAAC,EAAEC,UAAU;IACbnE,cAAc;IACd,GAAGoE;EACL,CAAC,GAAGb,IAAI;EACR,MAAMzD,IAAI,GAAGmE,aAAa,IAAIlH,OAAO,CAAC,CAAC;EACvC,IAAI,CAAC+C,IAAI,EAAE;IACTjD,QAAQ,CAAC,0EAA0E,CAAC;IACpF,OAAOQ,QAAQ;EACjB;EACA,MAAM6G,CAAC,GAAGC,UAAU,IAAIrE,IAAI,CAACoE,CAAC,CAACG,IAAI,CAACvE,IAAI,CAAC,KAAKwE,CAAC,IAAIA,CAAC,CAAC;EACrD,IAAIX,OAAO,EAAEC,QAAQ,CAACD,OAAO,GAAGA,OAAO;EACvC,MAAMY,mBAAmB,GAAG;IAC1B,GAAGzH,WAAW,CAAC,CAAC;IAChB,IAAIgD,IAAI,CAAC0E,OAAO,IAAI1E,IAAI,CAAC0E,OAAO,CAACC,KAAK;EACxC,CAAC;EACD,IAAIC,UAAU,GAAGV,EAAE,IAAIE,CAAC,CAACF,EAAE,IAAIlE,IAAI,CAAC0E,OAAO,IAAI1E,IAAI,CAAC0E,OAAO,CAACG,SAAS;EACrED,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAC1F,MAAME,YAAY,GAAGnG,aAAa,CAACpB,QAAQ,EAAEkH,mBAAmB,CAAC;EACjE,MAAMM,YAAY,GAAGf,QAAQ,IAAIc,YAAY,IAAIL,mBAAmB,CAACO,mBAAmB,IAAIpB,OAAO;EACnG,MAAM;IACJqB;EACF,CAAC,GAAGR,mBAAmB;EACvB,MAAMhD,GAAG,GAAGmC,OAAO,KAAKqB,YAAY,GAAGA,YAAY,CAACH,YAAY,IAAIC,YAAY,CAAC,GAAGD,YAAY,IAAIC,YAAY,CAAC;EACjH,IAAI/E,IAAI,CAAC0E,OAAO,IAAI1E,IAAI,CAAC0E,OAAO,CAACQ,aAAa,IAAIlF,IAAI,CAAC0E,OAAO,CAACQ,aAAa,CAACC,gBAAgB,EAAE;IAC7FpB,MAAM,GAAGA,MAAM,IAAIlG,MAAM,CAACwB,IAAI,CAAC0E,MAAM,CAAC,CAACvG,MAAM,GAAG,CAAC,GAAG;MAClD,GAAGuG,MAAM;MACT,GAAG/D,IAAI,CAAC0E,OAAO,CAACQ,aAAa,CAACC;IAChC,CAAC,GAAG;MACF,GAAGnF,IAAI,CAAC0E,OAAO,CAACQ,aAAa,CAACC;IAChC,CAAC;EACH;EACA,MAAMC,qBAAqB,GAAGrB,MAAM,GAAGD,QAAQ,CAACoB,aAAa,GAAG;IAC9DA,aAAa,EAAE;MACb,GAAGpB,QAAQ,CAACoB,aAAa;MACzBG,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;IACV;EACF,CAAC;EACD,MAAMrF,aAAa,GAAG;IACpB,GAAG6D,QAAQ;IACXJ,KAAK;IACL,GAAGK,MAAM;IACT,GAAGqB,qBAAqB;IACxBL,YAAY;IACZb,EAAE,EAAEU;EACN,CAAC;EACD,MAAMW,WAAW,GAAG9D,GAAG,GAAG2C,CAAC,CAAC3C,GAAG,EAAExB,aAAa,CAAC,GAAG8E,YAAY;EAC9D,IAAId,UAAU,EAAE;IACdpG,MAAM,CAACwB,IAAI,CAAC4E,UAAU,CAAC,CAAC/E,OAAO,CAACyC,CAAC,IAAI;MACnC,MAAM6D,IAAI,GAAGvB,UAAU,CAACtC,CAAC,CAAC;MAC1B,IAAI,OAAO6D,IAAI,CAAChG,IAAI,KAAK,UAAU,IAAI,CAACgG,IAAI,CAAClI,KAAK,IAAI,CAACkI,IAAI,CAAClI,KAAK,CAACC,QAAQ,IAAIgI,WAAW,CAAChG,OAAO,CAAC,GAAGoC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI4D,WAAW,CAAChG,OAAO,CAAC,GAAGoC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;MACvJ,SAAS8D,aAAaA,CAAA,EAAG;QACvB,OAAOjJ,KAAK,CAACG,aAAa,CAACH,KAAK,CAACkJ,QAAQ,EAAE,IAAI,EAAEF,IAAI,CAAC;MACxD;MACAvB,UAAU,CAACtC,CAAC,CAAC,GAAGnF,KAAK,CAACG,aAAa,CAAC8I,aAAa,EAAE,IAAI,CAAC;IAC1D,CAAC,CAAC;EACJ;EACA,MAAM/F,OAAO,GAAGI,WAAW,CAACmE,UAAU,IAAI1G,QAAQ,EAAEgI,WAAW,EAAEvF,IAAI,EAAEyE,mBAAmB,EAAExE,aAAa,EAAEC,cAAc,CAAC;EAC1H,MAAMyF,WAAW,GAAGhC,MAAM,KAAKjC,SAAS,GAAGiC,MAAM,GAAGc,mBAAmB,CAACmB,kBAAkB;EAC1F,OAAOD,WAAW,GAAGhJ,aAAa,CAACgJ,WAAW,EAAErB,eAAe,EAAE5E,OAAO,CAAC,GAAGA,OAAO;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}