const Joi = require('joi');
const { 
  commonSchemas, 
  consentSchema, 
  sheetSchema, 
  medicalInfoSchema, 
  imageSchema,
  COLLECTIONS 
} = require('./index');

// Patient validation schema for Firebase
const patientSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  nationalId: Joi.string().required(),
  fullName: Joi.string().required(),
  phoneNumber: Joi.string().required(),
  gender: Joi.string().valid('male', 'female', 'other').required(),
  age: Joi.number().required(),
  address: Joi.string().required(),
  occupation: Joi.string().required(),
  medicalInfo: medicalInfoSchema.default({}),
  drId: Joi.string().required(), // Student/Doctor ID
  consent: consentSchema.default({}),
  xrays: Joi.array().items(imageSchema).default([]),
  galleryImages: Joi.array().items(imageSchema).default([]),
  appointments: Joi.array().items(Joi.string()).default([]), // Array of appointment IDs
  teethCharts: Joi.array().items(Joi.string()).default([]), // Array of teeth chart IDs
  treatmentSheets: Joi.array().items(sheetSchema).default([]),
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// Patient creation schema (without ID)
const createPatientSchema = patientSchema.fork(['id'], (schema) => schema.forbidden());

// Patient update schema (partial)
const updatePatientSchema = patientSchema.fork(
  ['nationalId', 'fullName', 'phoneNumber', 'gender', 'age', 'address', 'occupation', 'drId'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Helper functions for Patient operations
const PatientHelpers = {
  // Validate patient data
  validateCreate: (data) => createPatientSchema.validate(data),
  validateUpdate: (data) => updatePatientSchema.validate(data),
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    // Convert appointment and teethChart ObjectIds to strings
    if (transformed.appointments) {
      transformed.appointments = transformed.appointments.map(id => 
        typeof id === 'object' ? id.toString() : id
      );
    }
    
    if (transformed.teethCharts) {
      transformed.teethCharts = transformed.teethCharts.map(id => 
        typeof id === 'object' ? id.toString() : id
      );
    }
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    if (prepared.createdAt && typeof prepared.createdAt === 'string') {
      prepared.createdAt = new Date(prepared.createdAt);
    }
    if (prepared.updatedAt && typeof prepared.updatedAt === 'string') {
      prepared.updatedAt = new Date(prepared.updatedAt);
    }
    
    // Handle consent dates
    if (prepared.consent && prepared.consent.signedAt && typeof prepared.consent.signedAt === 'string') {
      prepared.consent.signedAt = new Date(prepared.consent.signedAt);
    }
    
    // Handle image dates
    if (prepared.xrays) {
      prepared.xrays = prepared.xrays.map(xray => ({
        ...xray,
        date: typeof xray.date === 'string' ? new Date(xray.date) : xray.date
      }));
    }
    
    if (prepared.galleryImages) {
      prepared.galleryImages = prepared.galleryImages.map(image => ({
        ...image,
        date: typeof image.date === 'string' ? new Date(image.date) : image.date
      }));
    }
    
    // Handle treatment sheet dates
    if (prepared.treatmentSheets) {
      prepared.treatmentSheets = prepared.treatmentSheets.map(sheet => ({
        ...sheet,
        createdAt: typeof sheet.createdAt === 'string' ? new Date(sheet.createdAt) : sheet.createdAt,
        updatedAt: typeof sheet.updatedAt === 'string' ? new Date(sheet.updatedAt) : sheet.updatedAt
      }));
    }
    
    return prepared;
  }
};

module.exports = {
  patientSchema,
  createPatientSchema,
  updatePatientSchema,
  PatientHelpers,
  COLLECTION_NAME: COLLECTIONS.PATIENTS
};
