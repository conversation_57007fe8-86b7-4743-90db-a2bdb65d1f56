const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

exports.getAnalytics = async (req, res) => {
  try {
    const [totalStudents, totalSupervisors, totalAdmins, totalAssistants, totalSuperadmins, totalUniversities] = await Promise.all([
      FirestoreHelpers.count(COLLECTIONS.STUDENTS),
      FirestoreHelpers.count(COLLECTIONS.SUPERVISORS),
      FirestoreHelpers.count(COLLECTIONS.ADMINS),
      FirestoreHelpers.count(COLLECTIONS.ASSISTANTS),
      FirestoreHelpers.count(COLLECTIONS.CONFIGS, { field: 'role', operator: '==', value: 'superadmin' }),
      FirestoreHelpers.count(COLLECTIONS.UNIVERSITIES),
    ]);

    // Get recent activity from activity logs
    const recentActivityLogs = await FirestoreHelpers.find(
      COLLECTIONS.ACTIVITY_LOGS,
      null,
      { field: 'timestamp', direction: 'desc' },
      5
    );

    const recentActivity = recentActivityLogs.map(log => ({
      action: log.action,
      user: log.userName,
      date: log.timestamp
    }));

    res.status(200).json({
      totalUniversities,
      totalClinics: 0, // Removed clinic functionality
      totalAccounts: totalStudents + totalSupervisors + totalAdmins + totalAssistants + totalSuperadmins,
      recentActivity,
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.getActivityLog = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      dateRange = '7days',
      userType = 'all',
      actionType = 'all'
    } = req.query;

    // Build filter object
    const filter = {};

    // Date range filter
    if (dateRange !== 'all') {
      const now = new Date();
      let startDate;

      switch (dateRange) {
        case '1day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7days':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90days':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
      }

      if (startDate) {
        filter.timestamp = { $gte: startDate };
      }
    }

    // User type filter
    if (userType !== 'all') {
      filter.userRole = userType;
    }

    // Action type filter
    if (actionType !== 'all') {
      filter.action = { $regex: actionType, $options: 'i' };
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get all activities and filter/paginate manually for now
    // In a production app, you'd want to implement proper Firestore queries
    const allActivities = await FirestoreHelpers.find(COLLECTIONS.ACTIVITY_LOGS);

    // Apply filters
    let filteredActivities = allActivities;

    if (userType !== 'all') {
      filteredActivities = filteredActivities.filter(activity => activity.userRole === userType);
    }

    if (actionType !== 'all') {
      filteredActivities = filteredActivities.filter(activity =>
        activity.action.toLowerCase().includes(actionType.toLowerCase())
      );
    }

    // Sort by timestamp descending
    filteredActivities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    const total = filteredActivities.length;
    const activities = filteredActivities.slice(skip, skip + parseInt(limit));

    const totalPages = Math.ceil(total / parseInt(limit));

    res.status(200).json({
      activities,
      total,
      totalPages,
      currentPage: parseInt(page),
      hasNextPage: parseInt(page) < totalPages,
      hasPrevPage: parseInt(page) > 1
    });
  } catch (error) {
    console.error('Error fetching activity log:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.exportActivityLog = async (req, res) => {
  try {
    const { dateRange = 'all', userType = 'all', actionType = 'all' } = req.query;

    // Build filter object (same as getActivityLog)
    const filter = {};

    if (dateRange !== 'all') {
      const now = new Date();
      let startDate;

      switch (dateRange) {
        case '1day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7days':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90days':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
      }

      if (startDate) {
        filter.timestamp = { $gte: startDate };
      }
    }

    if (userType !== 'all') {
      filter.userRole = userType;
    }

    if (actionType !== 'all') {
      filter.action = { $regex: actionType, $options: 'i' };
    }

    const activities = await ActivityLog.find(filter)
      .sort({ timestamp: -1 })
      .lean();

    // Generate CSV content
    const csvHeader = 'Timestamp,User Name,User Role,Action,Details,IP Address\n';
    const csvRows = activities.map(activity => {
      const timestamp = new Date(activity.timestamp).toISOString();
      const userName = (activity.userName || 'Unknown').replace(/"/g, '""');
      const userRole = (activity.userRole || 'Unknown').replace(/"/g, '""');
      const action = (activity.action || '').replace(/"/g, '""');
      const details = (activity.details || '').replace(/"/g, '""');
      const ipAddress = (activity.ipAddress || '').replace(/"/g, '""');

      return `"${timestamp}","${userName}","${userRole}","${action}","${details}","${ipAddress}"`;
    }).join('\n');

    const csvContent = csvHeader + csvRows;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="activity-log-${new Date().toISOString().split('T')[0]}.csv"`);
    res.status(200).send(csvContent);
  } catch (error) {
    console.error('Error exporting activity log:', error);
    res.status(500).json({ message: 'Server error' });
  }
};