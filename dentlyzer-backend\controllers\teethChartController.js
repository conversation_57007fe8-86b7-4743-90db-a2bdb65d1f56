const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const teethChartSchema = Joi.object({
  date: Joi.date().required(),
  title: Joi.string().required(),
  patient: Joi.string().required(),
  teeth: Joi.array().items(
    Joi.object({
      toothNumber: Joi.number().required(),
      surfaces: Joi.array().items(Joi.string()),
      procedure: Joi.string().required(),
      notes: Joi.string()
    })
  ).default([])
});

const reviewSchema = Joi.object({
  toothNumber: Joi.number().required(),
  changes: Joi.object({
    procedure: Joi.string().allow('N/A'),
    condition: Joi.string().allow('N/A'),
    surfaces: Joi.array().items(Joi.string()),
    notes: Joi.string().allow('')
  }).required(),
  timestamp: Joi.date().required()
});

const createChart = async (req, res) => {
  const { error } = teethChartSchema.validate(req.body);
  if (error) return res.status(400).json({ message: error.details[0].message });

  try {
    const patient = await Patient.findOne({ nationalId: req.body.patient });
    if (!patient) return res.status(404).json({ message: 'Patient not found' });

    const chart = new TeethChart({ ...req.body });
    await chart.save();
    res.status(201).json(chart);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getChartsByPatient = async (req, res) => {
  try {
    const charts = await TeethChart.find({ patient: req.params.nationalId });
    res.json(charts);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const addToothToChart = async (req, res) => {
  try {
    const chart = await TeethChart.findById(req.params.chartId);
    if (!chart) return res.status(404).json({ message: 'Chart not found' });
    if (chart.isLocked) return res.status(403).json({ message: 'This chart is locked and cannot be edited' });

    chart.teeth.push(req.body);
    await chart.save();
    res.status(201).json(chart.teeth[chart.teeth.length - 1]);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const lockChart = async (req, res) => {
  try {
    const chart = await TeethChart.findById(req.params.chartId);
    if (!chart) return res.status(404).json({ message: 'Chart not found' });
    if (chart.isLocked) return res.status(400).json({ message: 'Chart is already locked' });

    chart.isLocked = true;
    await chart.save();
    res.json({ message: 'Chart locked successfully', chart });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const addReview = async (req, res) => {
  const { error } = reviewSchema.validate(req.body);
  if (error) return res.status(400).json({ message: error.details[0].message });

  try {
    const chart = await TeethChart.findById(req.params.chartId);
    if (!chart) return res.status(404).json({ message: 'Chart not found' });
    if (chart.isLocked) return res.status(403).json({ message: 'This chart is locked and cannot be edited' });

    chart.reviews = chart.reviews || [];
    chart.reviews.push(req.body);
    await chart.save();
    res.status(201).json(chart.reviews[chart.reviews.length - 1]);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = { createChart, getChartsByPatient, addToothToChart, lockChart, addReview };