const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

exports.getSuperadminAnalytics = async (req, res) => {
  try {
    // Get time range from query params
    const { timeRange = '6months' } = req.query;

    // Calculate start date based on time range
    const startDate = new Date();
    switch (timeRange) {
      case '1month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case '3months':
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case '1year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      case 'all':
        // Set to a very old date to get all data
        startDate.setFullYear(2000);
        break;
      case '6months':
      default:
        startDate.setMonth(startDate.getMonth() - 6);
    }

    // Get counts of different account types
    const [
      totalStudents,
      totalSupervisors,
      totalAdmins,
      totalAssistants,
      totalSuperadmins,
      totalDentists,
      totalUniversities,
      totalPatients,
      totalAppointments,
      totalTeethCharts,
      totalReviews
    ] = await Promise.all([
      Student.countDocuments(),
      Supervisor.countDocuments(),
      Admin.countDocuments(),
      Assistant.countDocuments(),
      Superadmin.countDocuments(),
      Dentist.countDocuments(),
      University.countDocuments(),
      Patient.countDocuments(),
      Appointment.countDocuments(),
      TeethChart.countDocuments(),
      Review.countDocuments()
    ]);

    const [
      studentGrowth,
      supervisorGrowth,
      adminGrowth,
      assistantGrowth,
      dentistGrowth,
      universityGrowth
    ] = await Promise.all([
      getMonthlyGrowth(Student, startDate),
      getMonthlyGrowth(Supervisor, startDate),
      getMonthlyGrowth(Admin, startDate),
      getMonthlyGrowth(Assistant, startDate),
      getMonthlyGrowth(Dentist, startDate),
      getMonthlyGrowth(University, startDate)
    ]);

    // Get appointment statistics
    const appointmentStats = await getAppointmentStats(startDate);

    // Get university distribution
    const universityDistribution = await getUniversityDistribution();

    // Get patient demographics
    const patientDemographics = await getPatientDemographics();

    // Get procedure types distribution
    const procedureTypes = await getProcedureTypes(startDate);

    // Get recent activity
    const recentActivity = await getRecentActivity(startDate);

    // Get business metrics
    const businessMetrics = await getBusinessMetrics(startDate);

    // Get system usage metrics
    const systemUsage = await getSystemUsageMetrics(startDate);

    // Get performance metrics
    const performanceMetrics = await getPerformanceMetrics(startDate);

    res.status(200).json({
      counts: {
        totalStudents,
        totalSupervisors,
        totalAdmins,
        totalAssistants,
        totalSuperadmins,
        totalDentists,
        totalUniversities,
        totalPatients,
        totalAppointments,
        totalTeethCharts,
        totalReviews,
        totalAccounts: totalStudents + totalSupervisors + totalAdmins + totalAssistants + totalSuperadmins + totalDentists
      },
      growth: {
        studentGrowth,
        supervisorGrowth,
        adminGrowth,
        assistantGrowth,
        dentistGrowth,
        universityGrowth
      },
      appointmentStats,
      universityDistribution,
      patientDemographics,
      procedureTypes,
      recentActivity,
      businessMetrics,
      systemUsage,
      performanceMetrics
    });
  } catch (error) {
    console.error('Error fetching superadmin analytics:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Helper function to get monthly growth for a model
async function getMonthlyGrowth(Model, startDate) {
  const pipeline = [
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: "$createdAt" },
          month: { $month: "$createdAt" }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { "_id.year": 1, "_id.month": 1 }
    }
  ];

  const results = await Model.aggregate(pipeline);

  // Format the results
  return results.map(item => {
    const date = new Date(item._id.year, item._id.month - 1);
    return {
      month: date.toLocaleString('default', { month: 'short' }),
      year: item._id.year,
      count: item.count
    };
  });
}

// Helper function to get appointment statistics
async function getAppointmentStats(startDate) {
  const stats = await Appointment.aggregate([
    {
      $group: {
        _id: "$status",
        count: { $sum: 1 }
      }
    }
  ]);

  const appointmentsByMonth = await Appointment.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: "$createdAt" },
          month: { $month: "$createdAt" }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { "_id.year": 1, "_id.month": 1 }
    }
  ]);

  // Format appointments by month
  const formattedAppointmentsByMonth = appointmentsByMonth.map(item => {
    const date = new Date(item._id.year, item._id.month - 1);
    return {
      month: date.toLocaleString('default', { month: 'short' }),
      year: item._id.year,
      count: item.count
    };
  });

  // Format the status counts
  const statusCounts = {};
  stats.forEach(item => {
    statusCounts[item._id] = item.count;
  });

  return {
    statusCounts,
    appointmentsByMonth: formattedAppointmentsByMonth
  };
}

// Helper function to get university distribution
async function getUniversityDistribution() {
  // Count students per university
  const studentsByUniversity = await Student.aggregate([
    {
      $group: {
        _id: "$university",
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  // Get university names
  const universities = await University.find({
    universityId: { $in: studentsByUniversity.map(item => item._id) }
  }).select('universityId name');

  // Create a map of university IDs to names
  const universityMap = {};
  universities.forEach(uni => {
    universityMap[uni.universityId] = uni.name.en;
  });

  // Format the results
  return studentsByUniversity.map(item => ({
    universityId: item._id,
    universityName: universityMap[item._id] || item._id,
    studentCount: item.count
  }));
}

// Helper function to get patient demographics
async function getPatientDemographics() {
  // Get gender distribution
  const genderDistribution = await Patient.aggregate([
    {
      $group: {
        _id: "$gender",
        count: { $sum: 1 }
      }
    }
  ]);

  // Get age distribution
  const ageDistribution = await Patient.aggregate([
    {
      $group: {
        _id: {
          $switch: {
            branches: [
              { case: { $lte: ["$age", 18] }, then: "0-18" },
              { case: { $lte: ["$age", 30] }, then: "19-30" },
              { case: { $lte: ["$age", 45] }, then: "31-45" },
              { case: { $lte: ["$age", 60] }, then: "46-60" }
            ],
            default: "60+"
          }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { "_id": 1 }
    }
  ]);

  return {
    genderDistribution: genderDistribution.map(item => ({
      gender: item._id,
      count: item.count
    })),
    ageDistribution: ageDistribution.map(item => ({
      ageGroup: item._id,
      count: item.count
    }))
  };
}

// Helper function to get procedure types
async function getProcedureTypes(startDate) {
  const procedureTypes = await TeethChart.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    { $unwind: "$teeth" },
    {
      $group: {
        _id: "$teeth.procedure",
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    },
    {
      $limit: 10
    }
  ]);

  return procedureTypes.map(item => ({
    procedure: item._id,
    count: item.count
  }));
}

// Helper function to get recent activity
async function getRecentActivity(startDate) {
  try {
    const activities = await ActivityLog.find({
      timestamp: { $gte: startDate }
    })
      .sort({ timestamp: -1 })
      .limit(10);

    return activities.map(activity => ({
      action: activity.action,
      details: activity.details,
      user: activity.userName || 'Unknown',
      userRole: activity.userRole,
      date: activity.timestamp
    }));
  } catch (error) {
    console.error('Error fetching recent activity:', error);
    return [];
  }
}

// Helper function to get business metrics
async function getBusinessMetrics(startDate) {
  try {
    // Calculate user engagement metrics
    const activeUsers = await getActiveUsers(startDate);

    // Calculate university adoption rate
    const universityAdoption = await getUniversityAdoption(startDate);

    // Calculate system growth rate
    const growthRate = await getGrowthRate(startDate);

    // Calculate retention metrics
    const retentionMetrics = await getRetentionMetrics(startDate);

    return {
      activeUsers,
      universityAdoption,
      growthRate,
      retentionMetrics
    };
  } catch (error) {
    console.error('Error fetching business metrics:', error);
    return {
      activeUsers: { total: 0, byRole: {} },
      universityAdoption: { total: 0, active: 0, adoptionRate: 0 },
      growthRate: { monthly: 0, quarterly: 0 },
      retentionMetrics: { userRetention: 0, universityRetention: 0 }
    };
  }
}

// Helper function to get system usage metrics
async function getSystemUsageMetrics(startDate) {
  try {
    // Get appointment completion rates
    const appointmentMetrics = await getAppointmentCompletionRates(startDate);

    // Get patient management efficiency
    const patientMetrics = await getPatientManagementMetrics(startDate);

    // Get feature usage statistics
    const featureUsage = await getFeatureUsageStats(startDate);

    return {
      appointmentMetrics,
      patientMetrics,
      featureUsage
    };
  } catch (error) {
    console.error('Error fetching system usage metrics:', error);
    return {
      appointmentMetrics: { completionRate: 0, cancellationRate: 0 },
      patientMetrics: { averagePatientsPerStudent: 0, treatmentCompletionRate: 0 },
      featureUsage: { mostUsedFeatures: [], leastUsedFeatures: [] }
    };
  }
}

// Helper function to get performance metrics
async function getPerformanceMetrics(startDate) {
  try {
    // Calculate average response times (simulated)
    const responseTime = Math.random() * 200 + 100; // 100-300ms

    // Calculate system uptime (simulated)
    const uptime = 99.5 + Math.random() * 0.5; // 99.5-100%

    // Calculate error rates
    const errorRate = await getErrorRate(startDate);

    // Calculate data quality metrics
    const dataQuality = await getDataQualityMetrics();

    return {
      responseTime: Math.round(responseTime),
      uptime: Math.round(uptime * 100) / 100,
      errorRate,
      dataQuality
    };
  } catch (error) {
    console.error('Error fetching performance metrics:', error);
    return {
      responseTime: 150,
      uptime: 99.8,
      errorRate: 0.1,
      dataQuality: { completeness: 95, accuracy: 98 }
    };
  }
}

// Additional helper functions for business metrics
async function getActiveUsers(startDate) {
  try {
    const activeUsersByRole = await ActivityLog.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            userId: "$userId",
            userRole: "$userRole"
          }
        }
      },
      {
        $group: {
          _id: "$_id.userRole",
          count: { $sum: 1 }
        }
      }
    ]);

    const total = activeUsersByRole.reduce((sum, role) => sum + role.count, 0);
    const byRole = {};
    activeUsersByRole.forEach(role => {
      byRole[role._id] = role.count;
    });

    return { total, byRole };
  } catch (error) {
    return { total: 0, byRole: {} };
  }
}

async function getUniversityAdoption(startDate) {
  try {
    const totalUniversities = await University.countDocuments();
    const activeUniversities = await Student.distinct('university', {
      createdAt: { $gte: startDate }
    });

    return {
      total: totalUniversities,
      active: activeUniversities.length,
      adoptionRate: totalUniversities > 0 ? (activeUniversities.length / totalUniversities) * 100 : 0
    };
  } catch (error) {
    return { total: 0, active: 0, adoptionRate: 0 };
  }
}

async function getGrowthRate(startDate) {
  try {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastQuarter = new Date(now.getFullYear(), now.getMonth() - 3, 1);

    const [monthlyUsers, quarterlyUsers] = await Promise.all([
      Student.countDocuments({ createdAt: { $gte: lastMonth } }),
      Student.countDocuments({ createdAt: { $gte: lastQuarter } })
    ]);

    return {
      monthly: monthlyUsers,
      quarterly: quarterlyUsers
    };
  } catch (error) {
    return { monthly: 0, quarterly: 0 };
  }
}

async function getRetentionMetrics(startDate) {
  try {
    // Simplified retention calculation
    const totalUsers = await Student.countDocuments();
    const activeUsers = await ActivityLog.distinct('userId', {
      timestamp: { $gte: startDate }
    });

    const userRetention = totalUsers > 0 ? (activeUsers.length / totalUsers) * 100 : 0;

    return {
      userRetention: Math.round(userRetention * 100) / 100,
      universityRetention: 85 // Placeholder
    };
  } catch (error) {
    return { userRetention: 0, universityRetention: 0 };
  }
}

async function getAppointmentCompletionRates(startDate) {
  try {
    const appointments = await Appointment.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 }
        }
      }
    ]);

    const total = appointments.reduce((sum, status) => sum + status.count, 0);
    const completed = appointments.find(a => a._id === 'completed')?.count || 0;
    const cancelled = appointments.find(a => a._id === 'cancelled')?.count || 0;

    return {
      completionRate: total > 0 ? (completed / total) * 100 : 0,
      cancellationRate: total > 0 ? (cancelled / total) * 100 : 0
    };
  } catch (error) {
    return { completionRate: 0, cancellationRate: 0 };
  }
}

async function getPatientManagementMetrics(startDate) {
  try {
    const [totalStudents, totalPatients] = await Promise.all([
      Student.countDocuments(),
      Patient.countDocuments({ createdAt: { $gte: startDate } })
    ]);

    const averagePatientsPerStudent = totalStudents > 0 ? totalPatients / totalStudents : 0;

    return {
      averagePatientsPerStudent: Math.round(averagePatientsPerStudent * 100) / 100,
      treatmentCompletionRate: 78 // Placeholder
    };
  } catch (error) {
    return { averagePatientsPerStudent: 0, treatmentCompletionRate: 0 };
  }
}

async function getFeatureUsageStats(startDate) {
  try {
    const featureUsage = await ActivityLog.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: "$action",
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    const mostUsed = featureUsage.slice(0, 5).map(f => ({ feature: f._id, usage: f.count }));
    const leastUsed = featureUsage.slice(-3).map(f => ({ feature: f._id, usage: f.count }));

    return { mostUsedFeatures: mostUsed, leastUsedFeatures: leastUsed };
  } catch (error) {
    return { mostUsedFeatures: [], leastUsedFeatures: [] };
  }
}

async function getErrorRate(startDate) {
  try {
    // Placeholder for error rate calculation
    return Math.random() * 0.5; // 0-0.5%
  } catch (error) {
    return 0.1;
  }
}

async function getDataQualityMetrics() {
  try {
    // Calculate data completeness
    const [totalPatients, patientsWithCompleteData] = await Promise.all([
      Patient.countDocuments(),
      Patient.countDocuments({
        name: { $exists: true, $ne: '' },
        age: { $exists: true, $ne: null },
        gender: { $exists: true, $ne: '' }
      })
    ]);

    const completeness = totalPatients > 0 ? (patientsWithCompleteData / totalPatients) * 100 : 100;

    return {
      completeness: Math.round(completeness * 100) / 100,
      accuracy: 98 // Placeholder
    };
  } catch (error) {
    return { completeness: 95, accuracy: 98 };
  }
}
