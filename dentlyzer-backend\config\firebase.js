const admin = require('firebase-admin');
require('dotenv').config();

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAaJKFcxipd7SBS-GQK97ZIFr0oBBEKQOU",
  authDomain: "odenta-82359.firebaseapp.com",
  projectId: "odenta-82359",
  storageBucket: "odenta-82359.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:12ccba8515c9648b1d8941",
  measurementId: "G-HFXCKFE42Y"
};

// Initialize Firebase Admin SDK
let firebaseApp;

const initializeFirebase = () => {
  try {
    if (!firebaseApp) {
      // For production, you should use a service account key
      // For development, you can use the application default credentials
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        // Parse the service account key from environment variable
        const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);

        firebaseApp = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: firebaseConfig.projectId,
          storageBucket: firebaseConfig.storageBucket
        });
      } else {
        // For development - using project ID only with no authentication
        // This requires Firestore rules to allow unauthenticated access
        console.log('⚠️  Running in development mode without service account');
        console.log('⚠️  Make sure Firestore rules allow unauthenticated access');

        firebaseApp = admin.initializeApp({
          projectId: firebaseConfig.projectId,
          storageBucket: firebaseConfig.storageBucket
        });
      }

      console.log('✅ Firebase Admin SDK initialized successfully');
      console.log(`🔥 Project ID: ${firebaseConfig.projectId}`);
    }

    return firebaseApp;
  } catch (error) {
    console.error('❌ Firebase initialization error:', error.message);
    console.log('💡 For development, you can:');
    console.log('   1. Set FIREBASE_SERVICE_ACCOUNT_KEY environment variable');
    console.log('   2. Or use the development Firestore rules (firestore-dev.rules)');
    throw error;
  }
};

// Get Firestore database instance
const getFirestore = () => {
  if (!firebaseApp) {
    initializeFirebase();
  }
  return admin.firestore();
};

// Get Firebase Auth instance
const getAuth = () => {
  if (!firebaseApp) {
    initializeFirebase();
  }
  return admin.auth();
};

// Get Firebase Storage instance
const getStorage = () => {
  if (!firebaseApp) {
    initializeFirebase();
  }
  return admin.storage();
};

module.exports = {
  initializeFirebase,
  getFirestore,
  getAuth,
  getStorage,
  firebaseConfig
};
