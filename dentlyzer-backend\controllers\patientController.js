const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { PatientHelpers, createPatientSchema, updatePatientSchema } = require('../models/firebase/Patient');
const { upload, isCloudinaryConfigured } = require('../middleware/upload');

// Import Cloudinary helpers if configured
let deleteFromCloudinary, extractPublicId;
if (isCloudinaryConfigured()) {
  const cloudinaryHelpers = require('../config/cloudinary');
  deleteFromCloudinary = cloudinaryHelpers.deleteFromCloudinary;
  extractPublicId = cloudinaryHelpers.extractPublicId;
}

// Helper function to get file URL based on storage type
const getFileUrl = (file) => {
  if (isCloudinaryConfigured()) {
    // For Cloudinary, the file.path contains the full URL
    return file.path;
  } else {
    // For local storage, create relative path
    let relativePath = file.path.replace(/\\/g, '/');
    if (!relativePath.startsWith('uploads/')) {
      relativePath = 'uploads/' + relativePath.split('/').pop();
    }
    return relativePath;
  }
};

// Sheet validation schema
const sheetSchema = Joi.object({
  type: Joi.string()
    .valid('Operative', 'Fixed Prosthodontics', 'Removable Prosthodontics', 'Endodontics', 'Periodontics')
    .required(),
  diagnosis: Joi.string().required(),
  treatmentPlan: Joi.string().required(),
  notes: Joi.string().allow('').default(''),
  specificData: Joi.object().default({})
});

// Patient validation schema
const patientSchema = Joi.object({
  nationalId: Joi.string().required(),
  drId: Joi.string().required(),
  fullName: Joi.string().required(),
  age: Joi.number().required(),
  phoneNumber: Joi.string().required(),
  gender: Joi.string().valid('male', 'female', 'other').required(),
  address: Joi.string().allow('').default(''),
  occupation: Joi.string().allow('').default(''),
  medicalInfo: Joi.object({
    chronicDiseases: Joi.array().items(Joi.string()).default([]),
    recentSurgicalProcedures: Joi.string().allow('').default(''),
    currentMedications: Joi.string().allow('').default(''),
    chiefComplaint: Joi.string().allow('').default(''),
  }).default({ chiefComplaint: '' }),
  xrays: Joi.array().default([]),
  galleryImages: Joi.array().default([])
});

// Create patient
const createPatient = async (req, res) => {
  console.log('Creating patient with data:', req.body);

  // Check if this is an assistant creating a patient for a student
  const isAssistant = req.user && req.user.role === 'assistant';

  const { error, value } = patientSchema.validate(req.body, {
    abortEarly: false,
    allowUnknown: true
  });

  if (error) {
    console.error('Validation error:', error.details);
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(d => d.message)
    });
  }

  try {
    // Check if patient already exists
    const existingPatient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: value.nationalId }
    );

    // If patient exists and this is an assistant, we'll handle it differently
    if (existingPatient) {
      if (isAssistant) {
        console.log(`Patient ${value.nationalId} already exists, checking if already assigned to student ${value.drId}`);

        // Check if this patient is already assigned to this student
        const studentHasPatient = existingPatient.drId === value.drId;

        if (studentHasPatient) {
          console.log(`Patient ${value.nationalId} is already assigned to student ${value.drId}`);
          return res.status(200).json({
            message: 'Patient already exists for this student',
            patient: existingPatient
          });
        } else {
          console.log(`Patient ${value.nationalId} exists but not for student ${value.drId}, updating assignment`);
          // Update the existing patient to be assigned to the new student
          const updatedPatient = await FirestoreHelpers.update(
            COLLECTIONS.PATIENTS,
            existingPatient.id,
            { drId: value.drId, updatedAt: new Date() }
          );

          return res.status(200).json({
            message: 'Patient reassigned to new student successfully',
            patient: updatedPatient
          });
        }
      } else {
        // For non-assistants, just return an error
        return res.status(400).json({ message: 'Patient already exists' });
      }
    }

    const cleanedMedicalInfo = {
      chiefComplaint: value.medicalInfo.chiefComplaint,
      ...(value.medicalInfo.chronicDiseases?.length > 0 && {
        chronicDiseases: value.medicalInfo.chronicDiseases
      }),
      ...(value.medicalInfo.recentSurgicalProcedures && {
        recentSurgicalProcedures: value.medicalInfo.recentSurgicalProcedures
      }),
      ...(value.medicalInfo.currentMedications && {
        currentMedications: value.medicalInfo.currentMedications
      })
    };

    const patientData = {
      nationalId: value.nationalId,
      drId: value.drId,
      fullName: value.fullName,
      age: value.age,
      phoneNumber: value.phoneNumber,
      gender: value.gender,
      address: value.address,
      occupation: value.occupation,
      medicalInfo: cleanedMedicalInfo,
      xrays: req.files?.xrays?.map(file => ({
        url: getFileUrl(file),
        date: new Date(),
        note: ''
      })) || [],
      galleryImages: req.files?.galleryImages?.map(file => ({
        url: getFileUrl(file),
        date: new Date(),
        note: ''
      })) || [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const patient = await FirestoreHelpers.create(COLLECTIONS.PATIENTS, patientData);

    res.status(201).json({
      message: 'Patient created successfully',
      patient: patient
    });
  } catch (error) {
    console.error('Error creating patient:', error);

    // Handle Firebase duplicate error for nationalId
    if (error.message && error.message.includes('nationalId')) {
      return res.status(400).json({
        message: 'A patient with this National ID already exists. Please use a different National ID.'
      });
    }

    res.status(500).json({
      message: 'Server error while creating patient',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get patient by ID
const getPatientById = async (req, res) => {
  try {
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    patient.medicalInfo = patient.medicalInfo || {
      chronicDiseases: [],
      recentSurgicalProcedures: '',
      currentMedications: '',
      chiefComplaint: ''
    };
    patient.treatmentSheets = patient.treatmentSheets || [];

    res.json(patient);
  } catch (error) {
    console.error('Error fetching patient:', error);
    res.status(500).json({
      message: 'Server error while fetching patient',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update patient
const updatePatient = async (req, res) => {
  const { error, value } = updatePatientSchema.validate(req.body, {
    abortEarly: false,
    allowUnknown: true
  });

  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(d => d.message)
    });
  }

  try {
    // First find the patient
    const existingPatient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );

    if (!existingPatient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    const updateData = {
      ...value,
      medicalInfo: {
        chiefComplaint: value.medicalInfo?.chiefComplaint || existingPatient.medicalInfo?.chiefComplaint || '',
        ...(value.medicalInfo?.chronicDiseases && {
          chronicDiseases: value.medicalInfo.chronicDiseases
        }),
        ...(value.medicalInfo?.recentSurgicalProcedures && {
          recentSurgicalProcedures: value.medicalInfo.recentSurgicalProcedures
        }),
        ...(value.medicalInfo?.currentMedications && {
          currentMedications: value.medicalInfo.currentMedications
        })
      },
      updatedAt: new Date()
    };

    const patient = await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      existingPatient.id,
      updateData
    );

    res.json({
      message: 'Patient updated successfully',
      patient
    });
  } catch (error) {
    console.error('Error updating patient:', error);
    res.status(500).json({
      message: 'Server error while updating patient',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Add treatment sheet
const addTreatmentSheet = async (req, res) => {
  const { error, value } = sheetSchema.validate(req.body, { abortEarly: false });

  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(d => d.message)
    });
  }

  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    patient.treatmentSheets.push({
      type: value.type,
      details: {
        diagnosis: value.diagnosis,
        treatmentPlan: value.treatmentPlan,
        notes: value.notes,
        specificData: value.specificData
      }
    });

    await patient.save();

    res.status(201).json({
      message: 'Treatment sheet added successfully',
      sheet: patient.treatmentSheets[patient.treatmentSheets.length - 1]
    });
  } catch (error) {
    console.error('Error adding treatment sheet:', error);
    res.status(500).json({
      message: 'Server error while adding treatment sheet',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all treatment sheets
const getTreatmentSheets = async (req, res) => {
  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId })
      .select('treatmentSheets')
      .lean();

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    res.json(patient.treatmentSheets || []);
  } catch (error) {
    console.error('Error fetching treatment sheets:', error);
    res.status(500).json({
      message: 'Server error while fetching treatment sheets',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update treatment sheet
const updateTreatmentSheet = async (req, res) => {
  const { error, value } = sheetSchema.validate(req.body, { abortEarly: false });

  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(d => d.message)
    });
  }

  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    const sheet = patient.treatmentSheets.id(req.params.sheetId);
    if (!sheet) {
      return res.status(404).json({ message: 'Treatment sheet not found' });
    }

    sheet.type = value.type;
    sheet.details = {
      diagnosis: value.diagnosis,
      treatmentPlan: value.treatmentPlan,
      notes: value.notes,
      specificData: value.specificData
    };
    sheet.updatedAt = new Date();

    await patient.save();

    res.json({
      message: 'Treatment sheet updated successfully',
      sheet
    });
  } catch (error) {
    console.error('Error updating treatment sheet:', error);
    res.status(500).json({
      message: 'Server error while updating treatment sheet',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete treatment sheet
const deleteTreatmentSheet = async (req, res) => {
  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    const sheet = patient.treatmentSheets.id(req.params.sheetId);
    if (!sheet) {
      return res.status(404).json({ message: 'Treatment sheet not found' });
    }

    sheet.remove();
    await patient.save();

    res.json({ message: 'Treatment sheet deleted successfully' });
  } catch (error) {
    console.error('Error deleting treatment sheet:', error);
    res.status(500).json({
      message: 'Server error while deleting treatment sheet',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Add X-ray
const addXray = async (req, res) => {
  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId });
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    if (!req.files || !req.files.xrays) return res.status(400).json({ message: 'No X-ray files uploaded' });

    // Create proper URL paths for the files
    const newXrays = req.files.xrays.map(file => ({
      url: getFileUrl(file),
      date: new Date(),
      note: ''
    }));

    patient.xrays.push(...newXrays);
    await patient.save();

    console.log(`Added ${newXrays.length} X-rays for patient ${patient.nationalId}`);
    res.json(patient.xrays);
  } catch (error) {
    console.error('Error adding X-rays:', error);
    res.status(500).json({
      message: 'Server error while adding X-rays',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Add gallery image
const addGalleryImage = async (req, res) => {
  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId });
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    if (!req.files || !req.files.galleryImages) return res.status(400).json({ message: 'No gallery images uploaded' });

    // Create proper URL paths for the files
    const newImages = req.files.galleryImages.map(file => ({
      url: getFileUrl(file),
      date: new Date(),
      note: ''
    }));

    patient.galleryImages.push(...newImages);
    await patient.save();

    console.log(`Added ${newImages.length} gallery images for patient ${patient.nationalId}`);
    res.json(patient.galleryImages);
  } catch (error) {
    console.error('Error adding gallery images:', error);
    res.status(500).json({
      message: 'Server error while adding gallery images',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update X-ray note
const updateXrayNote = async (req, res) => {
  const { xrayId, note } = req.body;
  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId });
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    const xray = patient.xrays.id(xrayId);
    if (!xray) return res.status(404).json({ message: 'X-ray not found' });
    xray.note = note;
    await patient.save();
    res.json(xray);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update gallery image note
const updateGalleryImageNote = async (req, res) => {
  const { imageId, note } = req.body;
  try {
    const patient = await Patient.findOne({ nationalId: req.params.nationalId });
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    const image = patient.galleryImages.id(imageId);
    if (!image) return res.status(404).json({ message: 'Image not found' });
    image.note = note;
    await patient.save();
    res.json(image);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Delete X-ray
const deleteXray = async (req, res) => {
  try {
    const { nationalId, xrayId } = req.params;

    const patient = await Patient.findOne({ nationalId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Find the X-ray by ID
    const xrayIndex = patient.xrays.findIndex(x => x._id.toString() === xrayId);
    if (xrayIndex === -1) {
      return res.status(404).json({ message: 'X-ray not found' });
    }

    const xray = patient.xrays[xrayIndex];

    // Delete from Cloudinary if configured
    if (isCloudinaryConfigured() && xray.url && deleteFromCloudinary && extractPublicId) {
      try {
        const publicId = extractPublicId(xray.url);
        if (publicId) {
          await deleteFromCloudinary(publicId);
          console.log(`Deleted X-ray from Cloudinary: ${publicId}`);
        }
      } catch (cloudinaryError) {
        console.error('Error deleting X-ray from Cloudinary:', cloudinaryError);
        // Continue with database deletion even if Cloudinary deletion fails
      }
    }

    // Remove the X-ray from the array
    patient.xrays.splice(xrayIndex, 1);
    await patient.save();

    res.json({ message: 'X-ray deleted successfully' });
  } catch (error) {
    console.error('Error deleting X-ray:', error);
    res.status(500).json({
      message: 'Server error while deleting X-ray',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete gallery image
const deleteGalleryImage = async (req, res) => {
  try {
    const { nationalId, imageId } = req.params;

    const patient = await Patient.findOne({ nationalId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Find the gallery image by ID
    const imageIndex = patient.galleryImages.findIndex(img => img._id.toString() === imageId);
    if (imageIndex === -1) {
      return res.status(404).json({ message: 'Gallery image not found' });
    }

    const image = patient.galleryImages[imageIndex];

    // Delete from Cloudinary if configured
    if (isCloudinaryConfigured() && image.url && deleteFromCloudinary && extractPublicId) {
      try {
        const publicId = extractPublicId(image.url);
        if (publicId) {
          await deleteFromCloudinary(publicId);
          console.log(`Deleted gallery image from Cloudinary: ${publicId}`);
        }
      } catch (cloudinaryError) {
        console.error('Error deleting gallery image from Cloudinary:', cloudinaryError);
        // Continue with database deletion even if Cloudinary deletion fails
      }
    }

    // Remove the image from the array
    patient.galleryImages.splice(imageIndex, 1);
    await patient.save();

    res.json({ message: 'Gallery image deleted successfully' });
  } catch (error) {
    console.error('Error deleting gallery image:', error);
    res.status(500).json({
      message: 'Server error while deleting gallery image',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get patients by doctor
const getPatientsByDoctor = async (req, res) => {
  try {
    console.log('Getting patients for doctor:', req.user);

    // Use studentId or dentistId depending on the user role
    const doctorId = req.user.studentId || req.user.dentistId || req.user.id;

    console.log('Looking for patients with drId:', doctorId);
    const patients = await Patient.find({ drId: doctorId });

    console.log(`Found ${patients.length} patients for doctor ${doctorId}`);
    res.json(patients);
  } catch (error) {
    console.error('Error getting patients by doctor:', error);
    res.status(500).json({ message: error.message });
  }
};

// Update patient medical info
const updatePatientMedicalInfo = async (req, res) => {
  try {
    const { nationalId } = req.params;
    const { medicalInfo } = req.body;

    const patient = await Patient.findOneAndUpdate(
      { nationalId },
      { $set: { medicalInfo } },
      { new: true, runValidators: true }
    );

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    res.json(patient);
  } catch (error) {
    console.error('Error updating patient medical info:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete patient
const deletePatient = async (req, res) => {
  try {
    const patient = await Patient.findOneAndDelete({ nationalId: req.params.nationalId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    res.json({ message: 'Patient deleted successfully' });
  } catch (error) {
    console.error('Error deleting patient:', error);
    res.status(500).json({
      message: 'Server error while deleting patient',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get treatment sheet by type
const getTreatmentSheetByType = async (req, res) => {
  try {
    const { nationalId, sheetType } = req.params;

    console.log(`Fetching sheet of type '${sheetType}' for patient with nationalId '${nationalId}'`);

    const patient = await Patient.findOne({ nationalId })
      .select('treatmentSheets')
      .lean();

    if (!patient) {
      console.log(`Patient with nationalId '${nationalId}' not found`);
      return res.status(404).json({ message: 'Patient not found' });
    }

    if (!patient.treatmentSheets || patient.treatmentSheets.length === 0) {
      console.log(`No treatment sheets found for patient with nationalId '${nationalId}'`);
      return res.status(404).json({ message: `No treatment sheets found for this patient` });
    }

    console.log(`Found ${patient.treatmentSheets.length} treatment sheets for patient`);

    // Find the most recent sheet of the specified type
    const matchingSheets = patient.treatmentSheets.filter(sheet => sheet.type === sheetType);
    console.log(`Found ${matchingSheets.length} sheets of type '${sheetType}'`);

    if (matchingSheets.length === 0) {
      return res.status(404).json({ message: `No ${sheetType} sheet found for this patient` });
    }

    const sheet = matchingSheets.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];
    console.log(`Returning most recent sheet with id ${sheet._id}`);

    res.json(sheet);
  } catch (error) {
    console.error(`Error fetching ${req.params.sheetType} sheet:`, error);
    res.status(500).json({
      message: `Server error while fetching ${req.params.sheetType} sheet`,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update patient consent
const updatePatientConsent = async (req, res) => {
  try {
    const { nationalId } = req.params;
    const { signatureText, signatureImage } = req.body;

    // Validate that at least one signature method is provided
    if (!signatureText && !signatureImage) {
      return res.status(400).json({ message: 'At least one signature method (text or image) is required' });
    }

    const consentData = {
      signatureText: signatureText || '',
      signatureImage: signatureImage || '',
      signedAt: new Date(),
      isSigned: true
    };

    const patient = await Patient.findOneAndUpdate(
      { nationalId },
      { $set: { consent: consentData } },
      { new: true, runValidators: true }
    );

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    res.json({
      message: 'Consent updated successfully',
      consent: patient.consent
    });
  } catch (error) {
    console.error('Error updating patient consent:', error);
    res.status(500).json({
      message: 'Server error while updating consent',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createPatient,
  getPatientById,
  updatePatient,
  addXray,
  addGalleryImage,
  updateXrayNote,
  updateGalleryImageNote,
  deleteXray,
  deleteGalleryImage,
  getPatientsByDoctor,
  updatePatientMedicalInfo,
  deletePatient,
  addTreatmentSheet,
  getTreatmentSheets,
  updateTreatmentSheet,
  deleteTreatmentSheet,
  getTreatmentSheetByType,
  updatePatientConsent
};