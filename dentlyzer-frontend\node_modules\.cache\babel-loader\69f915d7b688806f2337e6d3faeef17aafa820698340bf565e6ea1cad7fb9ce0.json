{"ast": null, "code": "import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport function withSSR() {\n  return function Extend(WrappedComponent) {\n    function I18nextWithSSR(_ref) {\n      let {\n        initialI18nStore,\n        initialLanguage,\n        ...rest\n      } = _ref;\n      useSSR(initialI18nStore, initialLanguage);\n      return createElement(WrappedComponent, {\n        ...rest\n      });\n    }\n    I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n    I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n    I18nextWithSSR.WrappedComponent = WrappedComponent;\n    return I18nextWithSSR;\n  };\n}", "map": {"version": 3, "names": ["createElement", "useSSR", "composeInitialProps", "getDisplayName", "withSSR", "Extend", "WrappedComponent", "I18nextWithSSR", "_ref", "initialI18nStore", "initialLanguage", "rest", "getInitialProps", "displayName"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/node_modules/react-i18next/dist/es/withSSR.js"], "sourcesContent": ["import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport function withSSR() {\n  return function Extend(WrappedComponent) {\n    function I18nextWithSSR(_ref) {\n      let {\n        initialI18nStore,\n        initialLanguage,\n        ...rest\n      } = _ref;\n      useSSR(initialI18nStore, initialLanguage);\n      return createElement(WrappedComponent, {\n        ...rest\n      });\n    }\n    I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n    I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n    I18nextWithSSR.WrappedComponent = WrappedComponent;\n    return I18nextWithSSR;\n  };\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,mBAAmB,QAAQ,cAAc;AAClD,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAO,SAASC,OAAOA,CAAA,EAAG;EACxB,OAAO,SAASC,MAAMA,CAACC,gBAAgB,EAAE;IACvC,SAASC,cAAcA,CAACC,IAAI,EAAE;MAC5B,IAAI;QACFC,gBAAgB;QAChBC,eAAe;QACf,GAAGC;MACL,CAAC,GAAGH,IAAI;MACRP,MAAM,CAACQ,gBAAgB,EAAEC,eAAe,CAAC;MACzC,OAAOV,aAAa,CAACM,gBAAgB,EAAE;QACrC,GAAGK;MACL,CAAC,CAAC;IACJ;IACAJ,cAAc,CAACK,eAAe,GAAGV,mBAAmB,CAACI,gBAAgB,CAAC;IACtEC,cAAc,CAACM,WAAW,GAAG,kBAAkBV,cAAc,CAACG,gBAAgB,CAAC,GAAG;IAClFC,cAAc,CAACD,gBAAgB,GAAGA,gBAAgB;IAClD,OAAOC,cAAc;EACvB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}