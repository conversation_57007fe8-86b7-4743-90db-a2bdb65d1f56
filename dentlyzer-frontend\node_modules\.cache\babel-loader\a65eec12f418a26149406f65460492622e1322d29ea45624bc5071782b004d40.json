{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\pages\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport { motion } from 'framer-motion';\nimport { FaTooth, FaEye, FaEyeSlash } from 'react-icons/fa';\nimport { FcGoogle } from 'react-icons/fc';\nimport Loader from '../components/Loader';\n\n// Define valid routes to prevent open redirect attacks\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VALID_ROUTES = ['/', '/student/dashboard', '/supervisor/dashboard', '/admin/dashboard', '/superadmin/dashboard', '/assistant/dashboard', '/forgot-password'];\nconst Login = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [googleLoading, setGoogleLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  // Hardcoded language (set to 'ar' for Arabic)\n  const language = 'en'; // Change to 'ar' for Arabic\n\n  // Initialize Google Sign-In\n  useEffect(() => {\n    const initializeGoogleSignIn = () => {\n      if (window.google) {\n        window.google.accounts.id.initialize({\n          client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,\n          callback: handleGoogleResponse\n        });\n      }\n    };\n\n    // Load Google Identity Services script\n    if (!window.google) {\n      const script = document.createElement('script');\n      script.src = 'https://accounts.google.com/gsi/client';\n      script.async = true;\n      script.defer = true;\n      script.onload = initializeGoogleSignIn;\n      document.head.appendChild(script);\n    } else {\n      initializeGoogleSignIn();\n    }\n  }, []);\n\n  // Secure navigation handler\n  const safeNavigate = useCallback(path => {\n    if (VALID_ROUTES.includes(path)) {\n      navigate(path);\n    } else {\n      console.warn('Invalid navigation attempt:', path);\n      navigate('/'); // Fallback to home\n    }\n  }, [navigate]);\n\n  // Handle Google Sign-In response\n  const handleGoogleResponse = async response => {\n    setGoogleLoading(true);\n    setError('');\n    try {\n      const result = await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/google-login`, {\n        token: response.credential\n      });\n      const {\n        token,\n        user\n      } = result.data;\n      console.log('Google login response:', result.data);\n      login(token, user);\n      const redirectPath = getDashboardPath(user.role);\n      safeNavigate(redirectPath);\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      setError(language === 'ar' ? ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'فشل تسجيل الدخول بـ Google. حاول مرة أخرى.' : ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Google sign-in failed. Please try again.');\n      setGoogleLoading(false);\n    }\n  };\n\n  // Handle Google Sign-In button click\n  const handleGoogleSignIn = () => {\n    if (window.google) {\n      window.google.accounts.id.prompt();\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/login`, {\n        email,\n        password\n      });\n      const {\n        token,\n        user\n      } = response.data;\n      console.log('Login response:', response.data);\n      login(token, user);\n      const redirectPath = getDashboardPath(user.role);\n      safeNavigate(redirectPath);\n    } catch (err) {\n      var _err$response3, _err$response3$data, _err$response4, _err$response4$data;\n      setError(language === 'ar' ? ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'فشل تسجيل الدخول. حاول مرة أخرى.' : ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Login failed. Please try again.');\n      setLoading(false);\n    }\n  };\n  const getDashboardPath = role => {\n    switch (role) {\n      case 'student':\n        return '/student/dashboard';\n      case 'supervisor':\n        return '/supervisor/dashboard';\n      case 'admin':\n        return '/admin/dashboard';\n      case 'superadmin':\n        return '/superadmin/dashboard';\n      case 'assistant':\n        return '/assistant/dashboard';\n      case 'dentist':\n        return '/dentist/dashboard';\n      default:\n        return '/';\n    }\n  };\n\n  // Animation variants to match Home.jsx\n  const container = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading || googleLoading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] p-4 sm:p-6 relative overflow-hidden\",\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: container,\n      initial: \"hidden\",\n      animate: \"show\",\n      className: \"bg-white bg-opacity-95 rounded-2xl shadow-xl w-full max-w-md p-6 sm:p-8 border border-[rgba(0,119,182,0.2)] z-10\",\n      style: {\n        direction: language === 'ar' ? 'rtl' : 'ltr'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center w-full py-4\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\n            ,\n            alt: \"ODenta Logo\",\n            className: \"h-10 w-auto\" // Adjust size as needed\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.h2, {\n        variants: item,\n        className: \"text-2xl sm:text-3xl font-bold text-[#0077B6] text-center mb-2\",\n        children: language === 'ar' ? 'مرحبًا بعودتك' : 'Welcome Back'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n        variants: item,\n        className: \"text-[#333333] text-center mb-6 text-sm sm:text-base\",\n        children: language === 'ar' ? 'سجل الدخول للوصول إلى لوحة التحكم الخاصة بك' : 'Sign in to access your dashboard'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: item,\n        className: \"mb-6 p-4 bg-red-50 text-red-700 rounded-lg text-sm border border-red-200\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          variants: item,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            className: `block text-sm font-medium text-[#333333] mb-1 ${language === 'ar' ? 'text-right' : 'text-left'}`,\n            children: language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            required: true,\n            className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] outline-none transition-colors bg-gray-50 text-[#333333] text-sm sm:text-base\",\n            placeholder: language === 'ar' ? '<EMAIL>' : '<EMAIL>',\n            \"aria-label\": language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          variants: item,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            className: `block text-sm font-medium text-[#333333] mb-1 ${language === 'ar' ? 'text-right' : 'text-left'}`,\n            children: language === 'ar' ? 'كلمة المرور' : 'Password'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? \"text\" : \"password\",\n              id: \"password\",\n              value: password,\n              onChange: e => setPassword(e.target.value),\n              required: true,\n              className: \"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] outline-none transition-colors bg-gray-50 text-[#333333] text-sm sm:text-base pr-10\",\n              placeholder: language === 'ar' ? '••••••••' : '••••••••',\n              \"aria-label\": language === 'ar' ? 'كلمة المرور' : 'Password'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-[#333333] hover:text-[#0077B6]\",\n              onClick: () => setShowPassword(!showPassword),\n              \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n              children: showPassword ? /*#__PURE__*/_jsxDEV(FaEyeSlash, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(FaEye, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          variants: item,\n          className: `text-sm ${language === 'ar' ? 'text-right' : 'text-right'}`,\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/forgot-password\",\n            onClick: () => safeNavigate('/forgot-password'),\n            className: \"text-[#0077B6] hover:text-[#20B2AA] font-medium transition-colors\",\n            \"aria-label\": language === 'ar' ? 'هل نسيت كلمة المرور؟' : 'Forgot your password?',\n            children: language === 'ar' ? 'هل نسيت كلمة المرور؟' : 'Forgot your password?'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          variants: item,\n          type: \"submit\",\n          disabled: loading,\n          className: `w-full bg-gradient-to-r ${loading ? 'from-[#0077B6]/70 to-[#20B2AA]/70 cursor-not-allowed' : 'from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'} text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:ring-offset-2 text-sm sm:text-base`,\n          whileHover: {\n            scale: loading ? 1 : 1.05\n          },\n          whileTap: {\n            scale: loading ? 1 : 0.95\n          },\n          \"aria-label\": loading ? language === 'ar' ? 'جارٍ تسجيل الدخول...' : 'Signing In...' : language === 'ar' ? 'تسجيل الدخول' : 'Sign In',\n          children: loading ? language === 'ar' ? 'جارٍ تسجيل الدخول...' : 'Signing In...' : language === 'ar' ? 'تسجيل الدخول' : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: item,\n        className: \"mt-6 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full border-t border-gray-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex justify-center text-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-2 bg-white text-gray-500\",\n              children: language === 'ar' ? 'أو' : 'or'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n        variants: item,\n        type: \"button\",\n        onClick: handleGoogleSignIn,\n        disabled: loading || googleLoading,\n        className: `w-full flex items-center justify-center px-6 py-3 border border-gray-300 rounded-full shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#20B2AA] transition-all duration-300 ${loading || googleLoading ? 'opacity-50 cursor-not-allowed' : ''}`,\n        whileHover: {\n          scale: loading || googleLoading ? 1 : 1.02\n        },\n        whileTap: {\n          scale: loading || googleLoading ? 1 : 0.98\n        },\n        children: [/*#__PURE__*/_jsxDEV(FcGoogle, {\n          className: \"w-5 h-5 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), googleLoading ? language === 'ar' ? 'جارٍ تسجيل الدخول...' : 'Signing in...' : language === 'ar' ? 'تسجيل الدخول بـ Google' : 'Continue with Google']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: item,\n        className: \"mt-6 text-center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          onClick: () => safeNavigate('/'),\n          className: \"text-[#0077B6] hover:text-[#20B2AA] font-medium transition-colors flex items-center justify-center\",\n          \"aria-label\": language === 'ar' ? 'العودة إلى الرئيسية' : 'Back to Home',\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: language === 'ar' ? 'ml-1' : 'mr-1',\n            children: language === 'ar' ? 'العودة إلى الرئيسية' : 'Back to Home'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: `w-4 h-4 ${language === 'ar' ? 'transform rotate-180' : ''}`,\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: \"2\",\n              d: \"M15 19l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"2NH9diWowPo/wJcyZvw2ZDRE1rA=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "Link", "useNavigate", "axios", "useAuth", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaEye", "FaEyeSlash", "FcGoogle", "Loader", "jsxDEV", "_jsxDEV", "VALID_ROUTES", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "error", "setError", "loading", "setLoading", "showPassword", "setShowPassword", "googleLoading", "setGoogleLoading", "login", "navigate", "language", "initializeGoogleSignIn", "window", "google", "accounts", "id", "initialize", "client_id", "process", "env", "REACT_APP_GOOGLE_CLIENT_ID", "callback", "handleGoogleResponse", "script", "document", "createElement", "src", "async", "defer", "onload", "head", "append<PERSON><PERSON><PERSON>", "safeNavigate", "path", "includes", "console", "warn", "response", "result", "post", "REACT_APP_API_URL", "token", "credential", "user", "data", "log", "redirectPath", "getDashboardPath", "role", "err", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "message", "handleGoogleSignIn", "prompt", "handleSubmit", "e", "preventDefault", "_err$response3", "_err$response3$data", "_err$response4", "_err$response4$data", "container", "hidden", "opacity", "y", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "div", "variants", "initial", "animate", "style", "direction", "to", "alt", "h2", "p", "onSubmit", "htmlFor", "type", "value", "onChange", "target", "required", "placeholder", "onClick", "button", "disabled", "whileHover", "scale", "whileTap", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/pages/Login.jsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport { motion } from 'framer-motion';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';\r\nimport { FcGoogle } from 'react-icons/fc';\r\nimport Loader from '../components/Loader';\r\n\r\n// Define valid routes to prevent open redirect attacks\r\nconst VALID_ROUTES = [\r\n  '/',\r\n  '/student/dashboard',\r\n  '/supervisor/dashboard',\r\n  '/admin/dashboard',\r\n  '/superadmin/dashboard',\r\n  '/assistant/dashboard',\r\n  '/forgot-password',\r\n];\r\n\r\nconst Login = () => {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [googleLoading, setGoogleLoading] = useState(false);\r\n  const { login } = useAuth();\r\n  const navigate = useNavigate();\r\n  // Hardcoded language (set to 'ar' for Arabic)\r\n  const language = 'en'; // Change to 'ar' for Arabic\r\n\r\n  // Initialize Google Sign-In\r\n  useEffect(() => {\r\n    const initializeGoogleSignIn = () => {\r\n      if (window.google) {\r\n        window.google.accounts.id.initialize({\r\n          client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,\r\n          callback: handleGoogleResponse,\r\n        });\r\n      }\r\n    };\r\n\r\n    // Load Google Identity Services script\r\n    if (!window.google) {\r\n      const script = document.createElement('script');\r\n      script.src = 'https://accounts.google.com/gsi/client';\r\n      script.async = true;\r\n      script.defer = true;\r\n      script.onload = initializeGoogleSignIn;\r\n      document.head.appendChild(script);\r\n    } else {\r\n      initializeGoogleSignIn();\r\n    }\r\n  }, []);\r\n\r\n  // Secure navigation handler\r\n  const safeNavigate = useCallback((path) => {\r\n    if (VALID_ROUTES.includes(path)) {\r\n      navigate(path);\r\n    } else {\r\n      console.warn('Invalid navigation attempt:', path);\r\n      navigate('/'); // Fallback to home\r\n    }\r\n  }, [navigate]);\r\n\r\n  // Handle Google Sign-In response\r\n  const handleGoogleResponse = async (response) => {\r\n    setGoogleLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      const result = await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/google-login`, {\r\n        token: response.credential,\r\n      });\r\n\r\n      const { token, user } = result.data;\r\n      console.log('Google login response:', result.data);\r\n      login(token, user);\r\n      const redirectPath = getDashboardPath(user.role);\r\n      safeNavigate(redirectPath);\r\n    } catch (err) {\r\n      setError(\r\n        language === 'ar'\r\n          ? err.response?.data?.message || 'فشل تسجيل الدخول بـ Google. حاول مرة أخرى.'\r\n          : err.response?.data?.message || 'Google sign-in failed. Please try again.'\r\n      );\r\n      setGoogleLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle Google Sign-In button click\r\n  const handleGoogleSignIn = () => {\r\n    if (window.google) {\r\n      window.google.accounts.id.prompt();\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/login`, {\r\n        email,\r\n        password,\r\n      });\r\n\r\n      const { token, user } = response.data;\r\n      console.log('Login response:', response.data);\r\n      login(token, user);\r\n      const redirectPath = getDashboardPath(user.role);\r\n      safeNavigate(redirectPath);\r\n    } catch (err) {\r\n      setError(\r\n        language === 'ar'\r\n          ? err.response?.data?.message || 'فشل تسجيل الدخول. حاول مرة أخرى.'\r\n          : err.response?.data?.message || 'Login failed. Please try again.'\r\n      );\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getDashboardPath = (role) => {\r\n    switch (role) {\r\n      case 'student':\r\n        return '/student/dashboard';\r\n      case 'supervisor':\r\n        return '/supervisor/dashboard';\r\n      case 'admin':\r\n        return '/admin/dashboard';\r\n      case 'superadmin':\r\n        return '/superadmin/dashboard';\r\n      case 'assistant':\r\n        return '/assistant/dashboard';\r\n      case 'dentist':\r\n        return '/dentist/dashboard';\r\n      default:\r\n        return '/';\r\n    }\r\n  };\r\n\r\n  // Animation variants to match Home.jsx\r\n  const container = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading || googleLoading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] p-4 sm:p-6 relative overflow-hidden\">\r\n      {/* Login Card */}\r\n      <motion.div\r\n        variants={container}\r\n        initial=\"hidden\"\r\n        animate=\"show\"\r\n        className=\"bg-white bg-opacity-95 rounded-2xl shadow-xl w-full max-w-md p-6 sm:p-8 border border-[rgba(0,119,182,0.2)] z-10\"\r\n        style={{ direction: language === 'ar' ? 'rtl' : 'ltr' }}\r\n      >\r\n        {/* <motion.div\r\n          variants={item}\r\n          className={`flex items-center justify-center mb-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}\r\n        >\r\n          <motion.div\r\n            className={`w-10 h-10 flex items-center justify-center ${language === 'ar' ? 'ml-2' : 'mr-2'}`}\r\n            whileHover={{ rotate: 10 }}\r\n          >\r\n            <FaTooth className=\"w-8 h-8 text-[#0077B6]\" />\r\n          </motion.div>\r\n          <h1 className=\"text-xl sm:text-2xl font-bold text-[#0077B6]\">\r\n            DENT<span className=\"text-[#20B2AA]\">LYZER</span>\r\n          </h1>\r\n        </motion.div> */}\r\n        <div className=\"flex justify-center items-center w-full py-4\">\r\n        <Link to=\"/\">\r\n                  <img \r\n                    src=\"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\r\n                    alt=\"ODenta Logo\"\r\n                    className=\"h-10 w-auto\" // Adjust size as needed\r\n                  />\r\n           </Link>\r\n           </div>\r\n\r\n        <motion.h2\r\n          variants={item}\r\n          className=\"text-2xl sm:text-3xl font-bold text-[#0077B6] text-center mb-2\"\r\n        >\r\n          {language === 'ar' ? 'مرحبًا بعودتك' : 'Welcome Back'}\r\n        </motion.h2>\r\n        <motion.p\r\n          variants={item}\r\n          className=\"text-[#333333] text-center mb-6 text-sm sm:text-base\"\r\n        >\r\n          {language === 'ar'\r\n            ? 'سجل الدخول للوصول إلى لوحة التحكم الخاصة بك'\r\n            : 'Sign in to access your dashboard'}\r\n        </motion.p>\r\n\r\n        {error && (\r\n          <motion.div\r\n            variants={item}\r\n            className=\"mb-6 p-4 bg-red-50 text-red-700 rounded-lg text-sm border border-red-200\"\r\n          >\r\n            {error}\r\n          </motion.div>\r\n        )}\r\n\r\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n          <motion.div variants={item}>\r\n            <label\r\n              htmlFor=\"email\"\r\n              className={`block text-sm font-medium text-[#333333] mb-1 ${\r\n                language === 'ar' ? 'text-right' : 'text-left'\r\n              }`}\r\n            >\r\n              {language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}\r\n            </label>\r\n            <input\r\n              type=\"email\"\r\n              id=\"email\"\r\n              value={email}\r\n              onChange={(e) => setEmail(e.target.value)}\r\n              required\r\n              className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] outline-none transition-colors bg-gray-50 text-[#333333] text-sm sm:text-base\"\r\n              placeholder={language === 'ar' ? '<EMAIL>' : '<EMAIL>'}\r\n              aria-label={language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}\r\n            />\r\n          </motion.div>\r\n\r\n          <motion.div variants={item}>\r\n            <label\r\n              htmlFor=\"password\"\r\n              className={`block text-sm font-medium text-[#333333] mb-1 ${\r\n                language === 'ar' ? 'text-right' : 'text-left'\r\n              }`}\r\n            >\r\n              {language === 'ar' ? 'كلمة المرور' : 'Password'}\r\n            </label>\r\n            <div className=\"relative\">\r\n              <input\r\n                type={showPassword ? \"text\" : \"password\"}\r\n                id=\"password\"\r\n                value={password}\r\n                onChange={(e) => setPassword(e.target.value)}\r\n                required\r\n                className=\"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] outline-none transition-colors bg-gray-50 text-[#333333] text-sm sm:text-base pr-10\"\r\n                placeholder={language === 'ar' ? '••••••••' : '••••••••'}\r\n                aria-label={language === 'ar' ? 'كلمة المرور' : 'Password'}\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-[#333333] hover:text-[#0077B6]\"\r\n                onClick={() => setShowPassword(!showPassword)}\r\n                aria-label={showPassword ? \"Hide password\" : \"Show password\"}\r\n              >\r\n                {showPassword ? (\r\n                  <FaEyeSlash className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <FaEye className=\"h-5 w-5\" />\r\n                )}\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            variants={item}\r\n            className={`text-sm ${language === 'ar' ? 'text-right' : 'text-right'}`}\r\n          >\r\n            <Link\r\n              to=\"/forgot-password\"\r\n              onClick={() => safeNavigate('/forgot-password')}\r\n              className=\"text-[#0077B6] hover:text-[#20B2AA] font-medium transition-colors\"\r\n              aria-label={language === 'ar' ? 'هل نسيت كلمة المرور؟' : 'Forgot your password?'}\r\n            >\r\n              {language === 'ar' ? 'هل نسيت كلمة المرور؟' : 'Forgot your password?'}\r\n            </Link>\r\n          </motion.div>\r\n\r\n          <motion.button\r\n            variants={item}\r\n            type=\"submit\"\r\n            disabled={loading}\r\n            className={`w-full bg-gradient-to-r ${\r\n              loading\r\n                ? 'from-[#0077B6]/70 to-[#20B2AA]/70 cursor-not-allowed'\r\n                : 'from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'\r\n            } text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:ring-offset-2 text-sm sm:text-base`}\r\n            whileHover={{ scale: loading ? 1 : 1.05 }}\r\n            whileTap={{ scale: loading ? 1 : 0.95 }}\r\n            aria-label={\r\n              loading\r\n                ? language === 'ar'\r\n                  ? 'جارٍ تسجيل الدخول...'\r\n                  : 'Signing In...'\r\n                : language === 'ar'\r\n                ? 'تسجيل الدخول'\r\n                : 'Sign In'\r\n            }\r\n          >\r\n            {loading\r\n              ? language === 'ar'\r\n                ? 'جارٍ تسجيل الدخول...'\r\n                : 'Signing In...'\r\n              : language === 'ar'\r\n              ? 'تسجيل الدخول'\r\n              : 'Sign In'}\r\n          </motion.button>\r\n        </form>\r\n\r\n        {/* Divider */}\r\n        <motion.div variants={item} className=\"mt-6 mb-6\">\r\n          <div className=\"relative\">\r\n            <div className=\"absolute inset-0 flex items-center\">\r\n              <div className=\"w-full border-t border-gray-300\"></div>\r\n            </div>\r\n            <div className=\"relative flex justify-center text-sm\">\r\n              <span className=\"px-2 bg-white text-gray-500\">\r\n                {language === 'ar' ? 'أو' : 'or'}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Google Sign-In Button */}\r\n        <motion.button\r\n          variants={item}\r\n          type=\"button\"\r\n          onClick={handleGoogleSignIn}\r\n          disabled={loading || googleLoading}\r\n          className={`w-full flex items-center justify-center px-6 py-3 border border-gray-300 rounded-full shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#20B2AA] transition-all duration-300 ${\r\n            (loading || googleLoading) ? 'opacity-50 cursor-not-allowed' : ''\r\n          }`}\r\n          whileHover={{ scale: (loading || googleLoading) ? 1 : 1.02 }}\r\n          whileTap={{ scale: (loading || googleLoading) ? 1 : 0.98 }}\r\n        >\r\n          <FcGoogle className=\"w-5 h-5 mr-3\" />\r\n          {googleLoading\r\n            ? language === 'ar'\r\n              ? 'جارٍ تسجيل الدخول...'\r\n              : 'Signing in...'\r\n            : language === 'ar'\r\n            ? 'تسجيل الدخول بـ Google'\r\n            : 'Continue with Google'}\r\n        </motion.button>\r\n\r\n        <motion.div variants={item} className=\"mt-6 text-center\">\r\n          <Link\r\n            to=\"/\"\r\n            onClick={() => safeNavigate('/')}\r\n            className=\"text-[#0077B6] hover:text-[#20B2AA] font-medium transition-colors flex items-center justify-center\"\r\n            aria-label={language === 'ar' ? 'العودة إلى الرئيسية' : 'Back to Home'}\r\n          >\r\n            <span className={language === 'ar' ? 'ml-1' : 'mr-1'}>\r\n              {language === 'ar' ? 'العودة إلى الرئيسية' : 'Back to Home'}\r\n            </span>\r\n            <svg\r\n              className={`w-4 h-4 ${language === 'ar' ? 'transform rotate-180' : ''}`}\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth=\"2\"\r\n                d=\"M15 19l-7-7 7-7\"\r\n              />\r\n            </svg>\r\n          </Link>\r\n        </motion.div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,QAAQ,gBAAgB;AAC3D,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG,CACnB,GAAG,EACH,oBAAoB,EACpB,uBAAuB,EACvB,kBAAkB,EAClB,uBAAuB,EACvB,sBAAsB,EACtB,kBAAkB,CACnB;AAED,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM;IAAE8B;EAAM,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAMyB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM4B,QAAQ,GAAG,IAAI,CAAC,CAAC;;EAEvB;EACA9B,SAAS,CAAC,MAAM;IACd,MAAM+B,sBAAsB,GAAGA,CAAA,KAAM;MACnC,IAAIC,MAAM,CAACC,MAAM,EAAE;QACjBD,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,UAAU,CAAC;UACnCC,SAAS,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA0B;UACjDC,QAAQ,EAAEC;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACA,IAAI,CAACV,MAAM,CAACC,MAAM,EAAE;MAClB,MAAMU,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,GAAG,GAAG,wCAAwC;MACrDH,MAAM,CAACI,KAAK,GAAG,IAAI;MACnBJ,MAAM,CAACK,KAAK,GAAG,IAAI;MACnBL,MAAM,CAACM,MAAM,GAAGlB,sBAAsB;MACtCa,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,MAAM,CAAC;IACnC,CAAC,MAAM;MACLZ,sBAAsB,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqB,YAAY,GAAGrD,WAAW,CAAEsD,IAAI,IAAK;IACzC,IAAIxC,YAAY,CAACyC,QAAQ,CAACD,IAAI,CAAC,EAAE;MAC/BxB,QAAQ,CAACwB,IAAI,CAAC;IAChB,CAAC,MAAM;MACLE,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEH,IAAI,CAAC;MACjDxB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMa,oBAAoB,GAAG,MAAOe,QAAQ,IAAK;IAC/C9B,gBAAgB,CAAC,IAAI,CAAC;IACtBN,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqC,MAAM,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAAC,GAAGrB,OAAO,CAACC,GAAG,CAACqB,iBAAiB,wBAAwB,EAAE;QACxFC,KAAK,EAAEJ,QAAQ,CAACK;MAClB,CAAC,CAAC;MAEF,MAAM;QAAED,KAAK;QAAEE;MAAK,CAAC,GAAGL,MAAM,CAACM,IAAI;MACnCT,OAAO,CAACU,GAAG,CAAC,wBAAwB,EAAEP,MAAM,CAACM,IAAI,CAAC;MAClDpC,KAAK,CAACiC,KAAK,EAAEE,IAAI,CAAC;MAClB,MAAMG,YAAY,GAAGC,gBAAgB,CAACJ,IAAI,CAACK,IAAI,CAAC;MAChDhB,YAAY,CAACc,YAAY,CAAC;IAC5B,CAAC,CAAC,OAAOG,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZpD,QAAQ,CACNS,QAAQ,KAAK,IAAI,GACb,EAAAwC,aAAA,GAAAD,GAAG,CAACZ,QAAQ,cAAAa,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcN,IAAI,cAAAO,kBAAA,uBAAlBA,kBAAA,CAAoBG,OAAO,KAAI,4CAA4C,GAC3E,EAAAF,cAAA,GAAAH,GAAG,CAACZ,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcR,IAAI,cAAAS,mBAAA,uBAAlBA,mBAAA,CAAoBC,OAAO,KAAI,0CACrC,CAAC;MACD/C,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMgD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI3C,MAAM,CAACC,MAAM,EAAE;MACjBD,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACyC,MAAM,CAAC,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBxD,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMoC,QAAQ,GAAG,MAAMtD,KAAK,CAACwD,IAAI,CAAC,GAAGrB,OAAO,CAACC,GAAG,CAACqB,iBAAiB,iBAAiB,EAAE;QACnF5C,KAAK;QACLE;MACF,CAAC,CAAC;MAEF,MAAM;QAAE2C,KAAK;QAAEE;MAAK,CAAC,GAAGN,QAAQ,CAACO,IAAI;MACrCT,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAER,QAAQ,CAACO,IAAI,CAAC;MAC7CpC,KAAK,CAACiC,KAAK,EAAEE,IAAI,CAAC;MAClB,MAAMG,YAAY,GAAGC,gBAAgB,CAACJ,IAAI,CAACK,IAAI,CAAC;MAChDhB,YAAY,CAACc,YAAY,CAAC;IAC5B,CAAC,CAAC,OAAOG,GAAG,EAAE;MAAA,IAAAW,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ9D,QAAQ,CACNS,QAAQ,KAAK,IAAI,GACb,EAAAkD,cAAA,GAAAX,GAAG,CAACZ,QAAQ,cAAAuB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchB,IAAI,cAAAiB,mBAAA,uBAAlBA,mBAAA,CAAoBP,OAAO,KAAI,kCAAkC,GACjE,EAAAQ,cAAA,GAAAb,GAAG,CAACZ,QAAQ,cAAAyB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAclB,IAAI,cAAAmB,mBAAA,uBAAlBA,mBAAA,CAAoBT,OAAO,KAAI,iCACrC,CAAC;MACDnD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,gBAAgB,GAAIC,IAAI,IAAK;IACjC,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,oBAAoB;MAC7B,KAAK,YAAY;QACf,OAAO,uBAAuB;MAChC,KAAK,OAAO;QACV,OAAO,kBAAkB;MAC3B,KAAK,YAAY;QACf,OAAO,uBAAuB;MAChC,KAAK,WAAW;QACd,OAAO,sBAAsB;MAC/B,KAAK,SAAS;QACZ,OAAO,oBAAoB;MAC7B;QACE,OAAO,GAAG;IACd;EACF,CAAC;;EAED;EACA,MAAMgB,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC7BC,IAAI,EAAE;MACJF,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,IAAI,GAAG;IACXN,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC7BC,IAAI,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAIjE,OAAO,IAAII,aAAa,EAAE;IAC5B,oBAAOd,OAAA,CAACF,MAAM;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACEnF,OAAA;IAAKoF,SAAS,EAAC,0JAA0J;IAAAC,QAAA,eAEvKrF,OAAA,CAACP,MAAM,CAAC6F,GAAG;MACTC,QAAQ,EAAEf,SAAU;MACpBgB,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,MAAM;MACdL,SAAS,EAAC,kHAAkH;MAC5HM,KAAK,EAAE;QAAEC,SAAS,EAAEzE,QAAQ,KAAK,IAAI,GAAG,KAAK,GAAG;MAAM,CAAE;MAAAmE,QAAA,gBAgBxDrF,OAAA;QAAKoF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC7DrF,OAAA,CAACX,IAAI;UAACuG,EAAE,EAAC,GAAG;UAAAP,QAAA,eACFrF,OAAA;YACEkC,GAAG,EAAC,wBAAwB,CAAC;YAAA;YAC7B2D,GAAG,EAAC,aAAa;YACjBT,SAAS,EAAC,aAAa,CAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETnF,OAAA,CAACP,MAAM,CAACqG,EAAE;QACRP,QAAQ,EAAER,IAAK;QACfK,SAAS,EAAC,gEAAgE;QAAAC,QAAA,EAEzEnE,QAAQ,KAAK,IAAI,GAAG,eAAe,GAAG;MAAc;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACZnF,OAAA,CAACP,MAAM,CAACsG,CAAC;QACPR,QAAQ,EAAER,IAAK;QACfK,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAE/DnE,QAAQ,KAAK,IAAI,GACd,6CAA6C,GAC7C;MAAkC;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAEV3E,KAAK,iBACJR,OAAA,CAACP,MAAM,CAAC6F,GAAG;QACTC,QAAQ,EAAER,IAAK;QACfK,SAAS,EAAC,0EAA0E;QAAAC,QAAA,EAEnF7E;MAAK;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAEDnF,OAAA;QAAMgG,QAAQ,EAAE/B,YAAa;QAACmB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjDrF,OAAA,CAACP,MAAM,CAAC6F,GAAG;UAACC,QAAQ,EAAER,IAAK;UAAAM,QAAA,gBACzBrF,OAAA;YACEiG,OAAO,EAAC,OAAO;YACfb,SAAS,EAAE,iDACTlE,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG,WAAW,EAC7C;YAAAmE,QAAA,EAEFnE,QAAQ,KAAK,IAAI,GAAG,mBAAmB,GAAG;UAAe;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACRnF,OAAA;YACEkG,IAAI,EAAC,OAAO;YACZ3E,EAAE,EAAC,OAAO;YACV4E,KAAK,EAAE/F,KAAM;YACbgG,QAAQ,EAAGlC,CAAC,IAAK7D,QAAQ,CAAC6D,CAAC,CAACmC,MAAM,CAACF,KAAK,CAAE;YAC1CG,QAAQ;YACRlB,SAAS,EAAC,2LAA2L;YACrMmB,WAAW,EAAErF,QAAQ,KAAK,IAAI,GAAG,iBAAiB,GAAG,iBAAkB;YACvE,cAAYA,QAAQ,KAAK,IAAI,GAAG,mBAAmB,GAAG;UAAgB;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAEbnF,OAAA,CAACP,MAAM,CAAC6F,GAAG;UAACC,QAAQ,EAAER,IAAK;UAAAM,QAAA,gBACzBrF,OAAA;YACEiG,OAAO,EAAC,UAAU;YAClBb,SAAS,EAAE,iDACTlE,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG,WAAW,EAC7C;YAAAmE,QAAA,EAEFnE,QAAQ,KAAK,IAAI,GAAG,aAAa,GAAG;UAAU;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACRnF,OAAA;YAAKoF,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBrF,OAAA;cACEkG,IAAI,EAAEtF,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCW,EAAE,EAAC,UAAU;cACb4E,KAAK,EAAE7F,QAAS;cAChB8F,QAAQ,EAAGlC,CAAC,IAAK3D,WAAW,CAAC2D,CAAC,CAACmC,MAAM,CAACF,KAAK,CAAE;cAC7CG,QAAQ;cACRlB,SAAS,EAAC,iMAAiM;cAC3MmB,WAAW,EAAErF,QAAQ,KAAK,IAAI,GAAG,UAAU,GAAG,UAAW;cACzD,cAAYA,QAAQ,KAAK,IAAI,GAAG,aAAa,GAAG;YAAW;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACFnF,OAAA;cACEkG,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,uFAAuF;cACjGoB,OAAO,EAAEA,CAAA,KAAM3F,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9C,cAAYA,YAAY,GAAG,eAAe,GAAG,eAAgB;cAAAyE,QAAA,EAE5DzE,YAAY,gBACXZ,OAAA,CAACJ,UAAU;gBAACwF,SAAS,EAAC;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAElCnF,OAAA,CAACL,KAAK;gBAACyF,SAAS,EAAC;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC7B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbnF,OAAA,CAACP,MAAM,CAAC6F,GAAG;UACTC,QAAQ,EAAER,IAAK;UACfK,SAAS,EAAE,WAAWlE,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG,YAAY,EAAG;UAAAmE,QAAA,eAExErF,OAAA,CAACX,IAAI;YACHuG,EAAE,EAAC,kBAAkB;YACrBY,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,kBAAkB,CAAE;YAChD4C,SAAS,EAAC,mEAAmE;YAC7E,cAAYlE,QAAQ,KAAK,IAAI,GAAG,sBAAsB,GAAG,uBAAwB;YAAAmE,QAAA,EAEhFnE,QAAQ,KAAK,IAAI,GAAG,sBAAsB,GAAG;UAAuB;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAEbnF,OAAA,CAACP,MAAM,CAACgH,MAAM;UACZlB,QAAQ,EAAER,IAAK;UACfmB,IAAI,EAAC,QAAQ;UACbQ,QAAQ,EAAEhG,OAAQ;UAClB0E,SAAS,EAAE,2BACT1E,OAAO,GACH,sDAAsD,GACtD,qEAAqE,oLAC0G;UACrLiG,UAAU,EAAE;YAAEC,KAAK,EAAElG,OAAO,GAAG,CAAC,GAAG;UAAK,CAAE;UAC1CmG,QAAQ,EAAE;YAAED,KAAK,EAAElG,OAAO,GAAG,CAAC,GAAG;UAAK,CAAE;UACxC,cACEA,OAAO,GACHQ,QAAQ,KAAK,IAAI,GACf,sBAAsB,GACtB,eAAe,GACjBA,QAAQ,KAAK,IAAI,GACjB,cAAc,GACd,SACL;UAAAmE,QAAA,EAEA3E,OAAO,GACJQ,QAAQ,KAAK,IAAI,GACf,sBAAsB,GACtB,eAAe,GACjBA,QAAQ,KAAK,IAAI,GACjB,cAAc,GACd;QAAS;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAGPnF,OAAA,CAACP,MAAM,CAAC6F,GAAG;QAACC,QAAQ,EAAER,IAAK;QAACK,SAAS,EAAC,WAAW;QAAAC,QAAA,eAC/CrF,OAAA;UAAKoF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBrF,OAAA;YAAKoF,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjDrF,OAAA;cAAKoF,SAAS,EAAC;YAAiC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNnF,OAAA;YAAKoF,SAAS,EAAC,sCAAsC;YAAAC,QAAA,eACnDrF,OAAA;cAAMoF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAC1CnE,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG;YAAI;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbnF,OAAA,CAACP,MAAM,CAACgH,MAAM;QACZlB,QAAQ,EAAER,IAAK;QACfmB,IAAI,EAAC,QAAQ;QACbM,OAAO,EAAEzC,kBAAmB;QAC5B2C,QAAQ,EAAEhG,OAAO,IAAII,aAAc;QACnCsE,SAAS,EAAE,oQACR1E,OAAO,IAAII,aAAa,GAAI,+BAA+B,GAAG,EAAE,EAChE;QACH6F,UAAU,EAAE;UAAEC,KAAK,EAAGlG,OAAO,IAAII,aAAa,GAAI,CAAC,GAAG;QAAK,CAAE;QAC7D+F,QAAQ,EAAE;UAAED,KAAK,EAAGlG,OAAO,IAAII,aAAa,GAAI,CAAC,GAAG;QAAK,CAAE;QAAAuE,QAAA,gBAE3DrF,OAAA,CAACH,QAAQ;UAACuF,SAAS,EAAC;QAAc;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACpCrE,aAAa,GACVI,QAAQ,KAAK,IAAI,GACf,sBAAsB,GACtB,eAAe,GACjBA,QAAQ,KAAK,IAAI,GACjB,wBAAwB,GACxB,sBAAsB;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEhBnF,OAAA,CAACP,MAAM,CAAC6F,GAAG;QAACC,QAAQ,EAAER,IAAK;QAACK,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eACtDrF,OAAA,CAACX,IAAI;UACHuG,EAAE,EAAC,GAAG;UACNY,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,GAAG,CAAE;UACjC4C,SAAS,EAAC,oGAAoG;UAC9G,cAAYlE,QAAQ,KAAK,IAAI,GAAG,qBAAqB,GAAG,cAAe;UAAAmE,QAAA,gBAEvErF,OAAA;YAAMoF,SAAS,EAAElE,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,MAAO;YAAAmE,QAAA,EAClDnE,QAAQ,KAAK,IAAI,GAAG,qBAAqB,GAAG;UAAc;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACPnF,OAAA;YACEoF,SAAS,EAAE,WAAWlE,QAAQ,KAAK,IAAI,GAAG,sBAAsB,GAAG,EAAE,EAAG;YACxE4F,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YAAA3B,QAAA,eAEnBrF,OAAA;cACEiH,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAC,GAAG;cACfC,CAAC,EAAC;YAAiB;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAChF,EAAA,CAhXID,KAAK;EAAA,QAOSV,OAAO,EACRF,WAAW;AAAA;AAAA+H,EAAA,GARxBnH,KAAK;AAkXX,eAAeA,KAAK;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}