const mongoose = require('mongoose');
const Student = require('./models/Student');

async function checkStudents() {
  try {
    await mongoose.connect('mongodb://localhost:27017/dentlyzer');
    console.log('Connected to MongoDB');
    
    const allStudents = await Student.find({});
    console.log(`\n=== All Students (${allStudents.length}) ===`);
    allStudents.forEach(s => {
      console.log(`- Name: ${s.name}, Role: ${s.role}, StudentId: ${s.studentId}, University: ${s.university}`);
    });
    
    const studentRoleStudents = await Student.find({ role: 'student' });
    console.log(`\n=== Students with role 'student' (${studentRoleStudents.length}) ===`);
    studentRoleStudents.forEach(s => {
      console.log(`- Name: ${s.name}, StudentId: ${s.studentId}, University: ${s.university}`);
    });
    
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

checkStudents();
