{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\admin\\\\Analytics.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AdminSidebar from './AdminSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaChartLine, FaCalendarAlt, FaStar, FaUserAlt, FaUsers, FaUserGraduate, FaUserNurse, FaUserMd, FaClipboardCheck, FaChartPie, FaChartBar, FaChartArea, FaFileMedical } from 'react-icons/fa';\nimport { <PERSON>, Pie, Doughnut, Line } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement, Filler } from 'chart.js';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst websiteColorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement, Filler);\n\n// Define a consistent color palette based on the website design system\nconst colorPalette = {\n  primary: {\n    main: `${websiteColorPalette.primary}CC`,\n    border: websiteColorPalette.primary\n  },\n  secondary: {\n    main: `${websiteColorPalette.secondary}CC`,\n    border: websiteColorPalette.secondary\n  },\n  accent: {\n    main: `${websiteColorPalette.accent}CC`,\n    border: websiteColorPalette.accent\n  },\n  red: {\n    main: 'rgba(239, 68, 68, 0.8)',\n    border: 'rgba(239, 68, 68, 1)'\n  },\n  orange: {\n    main: 'rgba(249, 115, 22, 0.8)',\n    border: 'rgba(249, 115, 22, 1)'\n  },\n  amber: {\n    main: 'rgba(245, 158, 11, 0.8)',\n    border: 'rgba(245, 158, 11, 1)'\n  }\n};\nconst Analytics = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [appointments, setAppointments] = useState([]);\n  const [reviews, setReviews] = useState([]);\n  const [patients, setPatients] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [supervisors, setSupervisors] = useState([]);\n  const [assistants, setAssistants] = useState([]);\n  const [treatmentSheets, setTreatmentSheets] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        setError('Please log in to view analytics.');\n        setLoading(false);\n        return;\n      }\n      if (!user.university) {\n        setError('User profile incomplete. Missing university information.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n\n        // First, get students, supervisors, and other data\n        const initialRequests = [axios.get(`${process.env.REACT_APP_API_URL}/api/admin/appointments?university=${encodeURIComponent(user.university)}`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/admin/patients?university=${encodeURIComponent(user.university)}`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/admin/students?university=${encodeURIComponent(user.university)}`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/admin/supervisors?university=${encodeURIComponent(user.university)}`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/admin/assistants?university=${encodeURIComponent(user.university)}`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/admin/treatment-sheets`, config)];\n\n        // Execute initial requests and handle errors individually\n        const [appointmentsRes, patientsRes, studentsRes, supervisorsRes, assistantsRes, treatmentSheetsRes] = await Promise.all(initialRequests.map(request => request.catch(error => {\n          console.error('API request failed:', error.message);\n          return {\n            data: []\n          }; // Return empty data on error\n        })));\n        const students = studentsRes.data || [];\n        console.log(`Found ${students.length} students in university ${user.university}`);\n\n        // Now fetch reviews for each student in the university\n        let allReviews = [];\n        if (students.length > 0) {\n          console.log('Fetching reviews for students...');\n\n          // Create an array of promises for fetching reviews for each student\n          const reviewPromises = students.map(student => {\n            const url = `${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student.studentId}`;\n            console.log(`Fetching reviews for student ${student.name} with URL: ${url}`);\n            return axios.get(url, config).then(response => {\n              console.log(`Successfully fetched ${response.data.length} reviews for student ${student.name}`);\n              return response;\n            }).catch(error => {\n              console.error(`Error fetching reviews for student ${student.name}:`, error.message);\n              // Try with _id as fallback\n              return axios.get(`${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student._id}`, config).then(response => {\n                console.log(`Fallback successful! Fetched ${response.data.length} reviews for student ${student.name}`);\n                return response;\n              }).catch(fallbackError => {\n                console.error(`Fallback also failed for student ${student.name}:`, fallbackError.message);\n                return {\n                  data: []\n                }; // Return empty array on error\n              });\n            });\n          });\n\n          // Execute all promises in parallel\n          const reviewsResults = await Promise.all(reviewPromises);\n\n          // Combine all reviews into a single array\n          reviewsResults.forEach(result => {\n            if (result.data && Array.isArray(result.data)) {\n              // Filter out storage signature from reviews\n              const filteredReviews = result.data.map(review => {\n                // Create a new object without the signature property\n                const {\n                  signature,\n                  ...reviewWithoutSignature\n                } = review;\n                return reviewWithoutSignature;\n              });\n              allReviews = [...allReviews, ...filteredReviews];\n            }\n          });\n\n          // Remove any duplicate reviews (in case a student appears in multiple queries)\n          allReviews = Array.from(new Map(allReviews.map(review => [review._id, review])).values());\n          console.log(`Found ${allReviews.length} total reviews for students in university ${user.university}`);\n        }\n        setAppointments(appointmentsRes.data || []);\n        setReviews(allReviews);\n        setPatients(patientsRes.data || []);\n        setStudents(students);\n        setSupervisors(supervisorsRes.data || []);\n        setAssistants(assistantsRes.data || []);\n        setTreatmentSheets(treatmentSheetsRes.data || []);\n        if (appointmentsRes.data.length === 0 && allReviews.length === 0 && patientsRes.data.length === 0 && treatmentSheetsRes.data.length === 0) {\n          setError('No data found for analytics.');\n        }\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response4$data, _err$response5;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 404 ? 'Analytics endpoints not found.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Failed to load analytics';\n        setError(errorMessage);\n        if (((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status) === 401) navigate('/login');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user, token, navigate]);\n\n  // Process appointments data\n  const appointmentsAnalytics = {\n    total: appointments.length,\n    pending: appointments.filter(a => a.status === 'pending').length,\n    completed: appointments.filter(a => a.status === 'completed').length,\n    cancelled: appointments.filter(a => a.status === 'cancelled').length,\n    today: appointments.filter(a => {\n      const apptDate = new Date(a.date);\n      const todayDate = new Date();\n      return apptDate.toDateString() === todayDate.toDateString();\n    }).length,\n    thisWeek: appointments.filter(a => {\n      const apptDate = new Date(a.date);\n      const today = new Date();\n      const startOfWeek = new Date(today);\n      startOfWeek.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)\n      const endOfWeek = new Date(startOfWeek);\n      endOfWeek.setDate(startOfWeek.getDate() + 6); // End of week (Saturday)\n      return apptDate >= startOfWeek && apptDate <= endOfWeek;\n    }).length,\n    byMonth: getAppointmentsByMonth(appointments),\n    byDay: getAppointmentsByDay(appointments),\n    byType: getAppointmentsByType(appointments)\n  };\n\n  // Filter out signature storage reviews\n  const filteredReviews = reviews.filter(review => !(review.patientId && review.patientId.nationalId === 'signature-storage'));\n\n  // Process reviews data\n  const reviewsAnalytics = {\n    total: filteredReviews.length,\n    pending: filteredReviews.filter(r => r.status === 'pending').length,\n    accepted: filteredReviews.filter(r => r.status === 'accepted').length,\n    declined: filteredReviews.filter(r => r.status === 'declined' || r.status === 'denied').length,\n    avgProcedureQuality: calculateAverageRating(filteredReviews, 'procedureQuality'),\n    avgPatientInteraction: calculateAverageRating(filteredReviews, 'patientInteraction'),\n    byProcedureType: getReviewsByProcedureType(filteredReviews),\n    byMonth: getReviewsByMonth(filteredReviews),\n    byStudent: getReviewsByStudent(filteredReviews)\n  };\n  console.log('Reviews analytics:', reviewsAnalytics);\n\n  // Process patients data\n  const patientsAnalytics = {\n    total: patients.length,\n    byAge: getPatientsAgeDistribution(patients),\n    byGender: getPatientsGenderDistribution(patients),\n    byChronicDisease: getPatientsChronicDiseaseDistribution(patients)\n  };\n\n  // People analytics\n  const peopleAnalytics = {\n    students: students.length,\n    supervisors: supervisors.length,\n    assistants: assistants.length,\n    patients: patients.length\n  };\n\n  // Process treatment sheets data\n  const proceduresAnalytics = {\n    total: treatmentSheets.length,\n    byType: getProceduresByType(treatmentSheets),\n    byMonth: getProceduresByMonth(treatmentSheets),\n    byStudent: getProceduresByStudent(treatmentSheets),\n    byPatient: getProceduresByPatient(treatmentSheets),\n    recentSheets: treatmentSheets.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, 10)\n  };\n\n  // Chart data for overview\n  const overviewChartData = {\n    labels: ['Patients', 'Students', 'Supervisors', 'Assistants', 'Appointments', 'Reviews', 'Procedures'],\n    datasets: [{\n      label: 'Counts',\n      data: [peopleAnalytics.patients, peopleAnalytics.students, peopleAnalytics.supervisors, peopleAnalytics.assistants, appointmentsAnalytics.total, reviewsAnalytics.total, proceduresAnalytics.total],\n      backgroundColor: [colorPalette.primary.main, colorPalette.secondary.main, colorPalette.accent.main, colorPalette.primary.main, colorPalette.secondary.main, colorPalette.accent.main, colorPalette.orange.main],\n      borderColor: [colorPalette.primary.border, colorPalette.secondary.border, colorPalette.accent.border, colorPalette.primary.border, colorPalette.secondary.border, colorPalette.accent.border, colorPalette.orange.border],\n      borderWidth: 1,\n      borderRadius: 6,\n      hoverOffset: 4\n    }]\n  };\n\n  // Appointments status chart data\n  const appointmentsStatusChartData = {\n    labels: ['Pending', 'Completed', 'Cancelled'],\n    datasets: [{\n      data: [appointmentsAnalytics.pending, appointmentsAnalytics.completed, appointmentsAnalytics.cancelled],\n      backgroundColor: [colorPalette.amber.main, colorPalette.accent.main, colorPalette.red.main],\n      borderColor: [colorPalette.amber.border, colorPalette.accent.border, colorPalette.red.border],\n      borderWidth: 1,\n      hoverOffset: 8\n    }]\n  };\n\n  // Appointments by month chart data\n  const appointmentsByMonthChartData = {\n    labels: appointmentsAnalytics.byMonth.map(item => item.month),\n    datasets: [{\n      label: 'Appointments',\n      data: appointmentsAnalytics.byMonth.map(item => item.count),\n      backgroundColor: colorPalette.primary.main,\n      borderColor: colorPalette.primary.border,\n      borderWidth: 1,\n      tension: 0.4,\n      fill: true\n    }]\n  };\n\n  // Appointments by day chart data\n  const appointmentsByDayChartData = {\n    labels: appointmentsAnalytics.byDay.map(item => item.day),\n    datasets: [{\n      label: 'Appointments',\n      data: appointmentsAnalytics.byDay.map(item => item.count),\n      backgroundColor: colorPalette.secondary.main,\n      borderColor: colorPalette.secondary.border,\n      borderWidth: 1,\n      borderRadius: 6\n    }]\n  };\n\n  // Appointments by type chart data\n  const appointmentsByTypeChartData = {\n    labels: Object.keys(appointmentsAnalytics.byType),\n    datasets: [{\n      data: Object.values(appointmentsAnalytics.byType),\n      backgroundColor: Object.keys(appointmentsAnalytics.byType).map((_, index) => {\n        const colorKeys = Object.keys(colorPalette);\n        return colorPalette[colorKeys[index % colorKeys.length]].main;\n      }),\n      borderColor: Object.keys(appointmentsAnalytics.byType).map((_, index) => {\n        const colorKeys = Object.keys(colorPalette);\n        return colorPalette[colorKeys[index % colorKeys.length]].border;\n      }),\n      borderWidth: 1,\n      hoverOffset: 8\n    }]\n  };\n\n  // Reviews status chart data\n  const reviewsStatusChartData = {\n    labels: ['Pending', 'Accepted', 'Declined'],\n    datasets: [{\n      data: [reviewsAnalytics.pending, reviewsAnalytics.accepted, reviewsAnalytics.declined],\n      backgroundColor: [colorPalette.amber.main, colorPalette.accent.main, colorPalette.red.main],\n      borderColor: [colorPalette.amber.border, colorPalette.accent.border, colorPalette.red.border],\n      borderWidth: 1,\n      hoverOffset: 8\n    }]\n  };\n\n  // Reviews by procedure type chart data\n  const reviewsByProcedureTypeChartData = {\n    labels: Object.keys(reviewsAnalytics.byProcedureType),\n    datasets: [{\n      data: Object.values(reviewsAnalytics.byProcedureType),\n      backgroundColor: Object.keys(reviewsAnalytics.byProcedureType).map((_, index) => {\n        const colorKeys = Object.keys(colorPalette);\n        return colorPalette[colorKeys[index % colorKeys.length]].main;\n      }),\n      borderColor: Object.keys(reviewsAnalytics.byProcedureType).map((_, index) => {\n        const colorKeys = Object.keys(colorPalette);\n        return colorPalette[colorKeys[index % colorKeys.length]].border;\n      }),\n      borderWidth: 1,\n      hoverOffset: 8\n    }]\n  };\n\n  // Reviews by month chart data\n  const reviewsByMonthChartData = {\n    labels: reviewsAnalytics.byMonth.map(item => item.month),\n    datasets: [{\n      label: 'Reviews',\n      data: reviewsAnalytics.byMonth.map(item => item.count),\n      backgroundColor: colorPalette.secondary.main,\n      borderColor: colorPalette.secondary.border,\n      borderWidth: 1,\n      tension: 0.4,\n      fill: true\n    }]\n  };\n\n  // Patients by age chart data\n  const patientsByAgeChartData = {\n    labels: Object.keys(patientsAnalytics.byAge),\n    datasets: [{\n      label: 'Patients',\n      data: Object.values(patientsAnalytics.byAge),\n      backgroundColor: colorPalette.accent.main,\n      borderColor: colorPalette.accent.border,\n      borderWidth: 1,\n      borderRadius: 6\n    }]\n  };\n\n  // Patients by gender chart data\n  const patientsByGenderChartData = {\n    labels: Object.keys(patientsAnalytics.byGender),\n    datasets: [{\n      data: Object.values(patientsAnalytics.byGender),\n      backgroundColor: [colorPalette.primary.main, colorPalette.secondary.main, colorPalette.accent.main],\n      borderColor: [colorPalette.primary.border, colorPalette.secondary.border, colorPalette.accent.border],\n      borderWidth: 1,\n      hoverOffset: 8\n    }]\n  };\n\n  // Patients by chronic disease chart data\n  const patientsByChronicDiseaseChartData = {\n    labels: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5),\n    // Top 5 chronic diseases\n    datasets: [{\n      label: 'Patients',\n      data: Object.values(patientsAnalytics.byChronicDisease).slice(0, 5),\n      // Top 5 chronic diseases\n      backgroundColor: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5).map((_, index) => {\n        const colorKeys = Object.keys(colorPalette);\n        return colorPalette[colorKeys[index % colorKeys.length]].main;\n      }),\n      borderColor: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5).map((_, index) => {\n        const colorKeys = Object.keys(colorPalette);\n        return colorPalette[colorKeys[index % colorKeys.length]].border;\n      }),\n      borderWidth: 1,\n      borderRadius: 6\n    }]\n  };\n\n  // Procedures by type chart data\n  const proceduresByTypeChartData = {\n    labels: Object.keys(proceduresAnalytics.byType),\n    datasets: [{\n      data: Object.values(proceduresAnalytics.byType),\n      backgroundColor: Object.keys(proceduresAnalytics.byType).map((_, index) => {\n        const colorKeys = Object.keys(colorPalette);\n        return colorPalette[colorKeys[index % colorKeys.length]].main;\n      }),\n      borderColor: Object.keys(proceduresAnalytics.byType).map((_, index) => {\n        const colorKeys = Object.keys(colorPalette);\n        return colorPalette[colorKeys[index % colorKeys.length]].border;\n      }),\n      borderWidth: 1,\n      hoverOffset: 8\n    }]\n  };\n\n  // Procedures by month chart data\n  const proceduresByMonthChartData = {\n    labels: proceduresAnalytics.byMonth.map(item => item.month),\n    datasets: [{\n      label: 'Procedures',\n      data: proceduresAnalytics.byMonth.map(item => item.count),\n      backgroundColor: colorPalette.accent.main,\n      borderColor: colorPalette.accent.border,\n      borderWidth: 1,\n      tension: 0.4,\n      fill: true\n    }]\n  };\n\n  // Procedures by student chart data\n  const proceduresByStudentChartData = {\n    labels: Object.keys(proceduresAnalytics.byStudent).slice(0, 10),\n    // Top 10 students\n    datasets: [{\n      label: 'Procedures',\n      data: Object.values(proceduresAnalytics.byStudent).slice(0, 10),\n      backgroundColor: colorPalette.primary.main,\n      borderColor: colorPalette.primary.border,\n      borderWidth: 1,\n      borderRadius: 6\n    }]\n  };\n\n  // Procedures by patient chart data\n  const proceduresByPatientChartData = {\n    labels: Object.keys(proceduresAnalytics.byPatient).slice(0, 8),\n    // Top 8 patients\n    datasets: [{\n      label: 'Procedures',\n      data: Object.values(proceduresAnalytics.byPatient).slice(0, 8),\n      backgroundColor: colorPalette.secondary.main,\n      borderColor: colorPalette.secondary.border,\n      borderWidth: 1,\n      borderRadius: 6\n    }]\n  };\n\n  // Chart options\n  const barChartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 12\n          },\n          padding: 20,\n          usePointStyle: true,\n          boxWidth: 8\n        }\n      },\n      title: {\n        display: false,\n        font: {\n          family: \"'Inter', sans-serif\",\n          size: 16,\n          weight: 'bold'\n        },\n        color: '#1e3a8a',\n        padding: {\n          bottom: 20\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        titleColor: '#1e3a8a',\n        bodyColor: '#4b5563',\n        borderColor: '#e5e7eb',\n        borderWidth: 1,\n        padding: 12,\n        boxPadding: 6,\n        usePointStyle: true,\n        bodyFont: {\n          family: \"'Inter', sans-serif\"\n        },\n        titleFont: {\n          family: \"'Inter', sans-serif\",\n          weight: 'bold'\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: 'rgba(226, 232, 240, 0.6)'\n        },\n        ticks: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 11\n          },\n          color: '#64748b'\n        }\n      },\n      x: {\n        grid: {\n          display: false\n        },\n        ticks: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 11\n          },\n          color: '#64748b'\n        }\n      }\n    }\n  };\n\n  // Pie chart options\n  const pieChartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'right',\n        labels: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 12\n          },\n          padding: 20,\n          usePointStyle: true,\n          boxWidth: 8\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        titleColor: '#1e3a8a',\n        bodyColor: '#4b5563',\n        borderColor: '#e5e7eb',\n        borderWidth: 1,\n        padding: 12,\n        boxPadding: 6,\n        usePointStyle: true,\n        bodyFont: {\n          family: \"'Inter', sans-serif\"\n        },\n        titleFont: {\n          family: \"'Inter', sans-serif\",\n          weight: 'bold'\n        }\n      }\n    },\n    cutout: '60%',\n    radius: '90%'\n  };\n\n  // Line chart options\n  const lineChartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 12\n          },\n          padding: 20,\n          usePointStyle: true,\n          boxWidth: 8\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        titleColor: '#1e3a8a',\n        bodyColor: '#4b5563',\n        borderColor: '#e5e7eb',\n        borderWidth: 1,\n        padding: 12,\n        boxPadding: 6,\n        usePointStyle: true,\n        bodyFont: {\n          family: \"'Inter', sans-serif\"\n        },\n        titleFont: {\n          family: \"'Inter', sans-serif\",\n          weight: 'bold'\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: 'rgba(226, 232, 240, 0.6)'\n        },\n        ticks: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 11\n          },\n          color: '#64748b'\n        }\n      },\n      x: {\n        grid: {\n          display: false\n        },\n        ticks: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 11\n          },\n          color: '#64748b'\n        }\n      }\n    }\n  };\n\n  // Helper function to get appointments by month\n  function getAppointmentsByMonth(appointments) {\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const appointmentsByMonth = {};\n\n    // Initialize all months with 0\n    months.forEach(month => {\n      appointmentsByMonth[month] = 0;\n    });\n\n    // Count appointments by month\n    appointments.forEach(appointment => {\n      const date = new Date(appointment.date);\n      const month = months[date.getMonth()];\n      appointmentsByMonth[month]++;\n    });\n\n    // Convert to array for chart\n    return months.map(month => ({\n      month,\n      count: appointmentsByMonth[month]\n    }));\n  }\n\n  // Helper function to get appointments by day of week\n  function getAppointmentsByDay(appointments) {\n    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n    const appointmentsByDay = {};\n\n    // Initialize all days with 0\n    days.forEach(day => {\n      appointmentsByDay[day] = 0;\n    });\n\n    // Count appointments by day\n    appointments.forEach(appointment => {\n      const date = new Date(appointment.date);\n      const day = days[date.getDay()];\n      appointmentsByDay[day]++;\n    });\n\n    // Convert to array for chart\n    return days.map(day => ({\n      day,\n      count: appointmentsByDay[day]\n    }));\n  }\n\n  // Helper function to get appointments by type\n  function getAppointmentsByType(appointments) {\n    const appointmentsByType = {};\n\n    // Count appointments by type\n    appointments.forEach(appointment => {\n      const type = appointment.type || 'Other';\n      appointmentsByType[type] = (appointmentsByType[type] || 0) + 1;\n    });\n    return appointmentsByType;\n  }\n\n  // Helper function to get reviews by procedure type\n  function getReviewsByProcedureType(reviews) {\n    const reviewsByProcedureType = {};\n\n    // Count reviews by procedure type\n    reviews.forEach(review => {\n      // Skip signature storage reviews\n      if (review.patientId && review.patientId.nationalId === 'signature-storage') return;\n      let type = 'Other';\n\n      // Handle different possible formats of procedureType\n      if (review.procedureType) {\n        type = review.procedureType;\n      } else if (review.procedure && review.procedure.type) {\n        type = review.procedure.type;\n      }\n      reviewsByProcedureType[type] = (reviewsByProcedureType[type] || 0) + 1;\n    });\n\n    // If we have too many categories, limit to top 5 plus \"Other\"\n    const entries = Object.entries(reviewsByProcedureType);\n    if (entries.length > 6) {\n      const sortedEntries = entries.sort((a, b) => b[1] - a[1]);\n      const top5 = sortedEntries.slice(0, 5);\n      const others = sortedEntries.slice(5).reduce((sum, entry) => sum + entry[1], 0);\n      const result = Object.fromEntries(top5);\n      result['Other'] = others;\n      return result;\n    }\n    return reviewsByProcedureType;\n  }\n\n  // Helper function to get reviews by month\n  function getReviewsByMonth(reviews) {\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const reviewsByMonth = {};\n\n    // Initialize all months with 0\n    months.forEach(month => {\n      reviewsByMonth[month] = 0;\n    });\n\n    // Count reviews by month\n    reviews.forEach(review => {\n      try {\n        // Skip signature storage reviews\n        if (review.patientId && review.patientId.nationalId === 'signature-storage') return;\n\n        // Try different date fields that might exist in the review object\n        let dateStr = null;\n        if (review.submittedDate) dateStr = review.submittedDate;else if (review.createdAt) dateStr = review.createdAt;else if (review.date) dateStr = review.date;else if (review.reviewedDate) dateStr = review.reviewedDate;\n        if (!dateStr) return; // Skip if no valid date found\n\n        const date = new Date(dateStr);\n        if (isNaN(date.getTime())) return; // Skip if date is invalid\n\n        const month = months[date.getMonth()];\n        reviewsByMonth[month]++;\n      } catch (error) {\n        console.error('Error processing review date:', error);\n      }\n    });\n\n    // Convert to array for chart\n    return months.map(month => ({\n      month,\n      count: reviewsByMonth[month]\n    }));\n  }\n\n  // Helper function to get reviews by student\n  function getReviewsByStudent(reviews) {\n    const reviewsByStudent = {};\n\n    // Count reviews by student\n    reviews.forEach(review => {\n      // Skip signature storage reviews\n      if (review.patientId && review.patientId.nationalId === 'signature-storage') return;\n\n      // Get student name from different possible formats\n      let student = 'Unknown';\n      if (review.studentName) {\n        student = review.studentName;\n      } else if (review.student && review.student.name) {\n        student = review.student.name;\n      } else if (review.studentId) {\n        // Try to find student name from students array\n        const foundStudent = students.find(s => s._id === review.studentId || s.studentId === review.studentId);\n        if (foundStudent) {\n          student = foundStudent.name;\n        }\n      }\n      if (!reviewsByStudent[student]) {\n        reviewsByStudent[student] = {\n          total: 0,\n          accepted: 0,\n          declined: 0,\n          pending: 0\n        };\n      }\n      reviewsByStudent[student].total++;\n      if (review.status === 'accepted') reviewsByStudent[student].accepted++;else if (review.status === 'declined' || review.status === 'denied') reviewsByStudent[student].declined++;else reviewsByStudent[student].pending++;\n    });\n\n    // Sort by total reviews and limit to top 10 students\n    const sortedStudents = Object.entries(reviewsByStudent).sort((a, b) => b[1].total - a[1].total).slice(0, 10);\n    return Object.fromEntries(sortedStudents);\n  }\n\n  // Helper function to calculate average rating\n  function calculateAverageRating(reviews, field) {\n    // Filter reviews that have a valid rating for the specified field\n    const validReviews = reviews.filter(review => {\n      // Skip signature storage reviews\n      if (review.patientId && review.patientId.nationalId === 'signature-storage') return false;\n\n      // Check direct field\n      if (review[field] && typeof review[field] === 'number' && review[field] > 0) {\n        return true;\n      }\n\n      // Check nested fields (e.g., ratings.procedureQuality)\n      if (review.ratings && review.ratings[field] && typeof review.ratings[field] === 'number' && review.ratings[field] > 0) {\n        return true;\n      }\n      return false;\n    });\n    if (validReviews.length === 0) return 0;\n\n    // Calculate sum considering both direct and nested fields\n    const sum = validReviews.reduce((total, review) => {\n      if (review[field] && typeof review[field] === 'number') {\n        return total + review[field];\n      } else if (review.ratings && review.ratings[field] && typeof review.ratings[field] === 'number') {\n        return total + review.ratings[field];\n      }\n      return total;\n    }, 0);\n    return (sum / validReviews.length).toFixed(1);\n  }\n\n  // Helper function to get patients age distribution\n  function getPatientsAgeDistribution(patients) {\n    const ageGroups = {\n      '0-18': 0,\n      '19-30': 0,\n      '31-45': 0,\n      '46-60': 0,\n      '61+': 0\n    };\n    patients.forEach(patient => {\n      const age = patient.age || 0;\n      if (age <= 18) ageGroups['0-18']++;else if (age <= 30) ageGroups['19-30']++;else if (age <= 45) ageGroups['31-45']++;else if (age <= 60) ageGroups['46-60']++;else ageGroups['61+']++;\n    });\n    return ageGroups;\n  }\n\n  // Helper function to get patients gender distribution\n  function getPatientsGenderDistribution(patients) {\n    const genderDistribution = {\n      'Male': 0,\n      'Female': 0,\n      'Other': 0\n    };\n    patients.forEach(patient => {\n      const gender = patient.gender || 'Other';\n      genderDistribution[gender] = (genderDistribution[gender] || 0) + 1;\n    });\n    return genderDistribution;\n  }\n\n  // Helper function to get patients chronic disease distribution\n  function getPatientsChronicDiseaseDistribution(patients) {\n    const diseaseDistribution = {};\n    patients.forEach(patient => {\n      if (patient.medicalInfo && patient.medicalInfo.chronicDiseases) {\n        patient.medicalInfo.chronicDiseases.forEach(disease => {\n          diseaseDistribution[disease] = (diseaseDistribution[disease] || 0) + 1;\n        });\n      }\n    });\n\n    // Sort by count and return\n    return Object.fromEntries(Object.entries(diseaseDistribution).sort((a, b) => b[1] - a[1]));\n  }\n\n  // Helper function to get procedures by type\n  function getProceduresByType(sheets) {\n    const proceduresByType = {};\n    sheets.forEach(sheet => {\n      const type = sheet.type || 'Other';\n      proceduresByType[type] = (proceduresByType[type] || 0) + 1;\n    });\n    return proceduresByType;\n  }\n\n  // Helper function to get procedures by month\n  function getProceduresByMonth(sheets) {\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const proceduresByMonth = {};\n\n    // Initialize all months with 0\n    months.forEach(month => {\n      proceduresByMonth[month] = 0;\n    });\n\n    // Count procedures by month\n    sheets.forEach(sheet => {\n      try {\n        const date = new Date(sheet.createdAt);\n        if (isNaN(date.getTime())) return; // Skip if date is invalid\n\n        const month = months[date.getMonth()];\n        proceduresByMonth[month]++;\n      } catch (error) {\n        console.error('Error processing sheet date:', error);\n      }\n    });\n\n    // Convert to array for chart\n    return months.map(month => ({\n      month,\n      count: proceduresByMonth[month]\n    }));\n  }\n\n  // Helper function to get procedures by student\n  function getProceduresByStudent(sheets) {\n    const proceduresByStudent = {};\n    sheets.forEach(sheet => {\n      const student = sheet.studentName || 'Unknown Student';\n      proceduresByStudent[student] = (proceduresByStudent[student] || 0) + 1;\n    });\n\n    // Sort by count and return\n    return Object.fromEntries(Object.entries(proceduresByStudent).sort((a, b) => b[1] - a[1]));\n  }\n\n  // Helper function to get procedures by patient\n  function getProceduresByPatient(sheets) {\n    const proceduresByPatient = {};\n    sheets.forEach(sheet => {\n      const patient = sheet.patientName || 'Unknown Patient';\n      proceduresByPatient[patient] = (proceduresByPatient[patient] || 0) + 1;\n    });\n\n    // Sort by count and return top 10\n    return Object.fromEntries(Object.entries(proceduresByPatient).sort((a, b) => b[1] - a[1]).slice(0, 10));\n  }\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1126,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-red-500 mr-3\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`,\n                children: \"Analytics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-[${websiteColorPalette.text}]`,\n                children: \"View analytics for your university\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                      className: `h-6 w-6 text-[${websiteColorPalette.primary}]`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1169,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1168,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Appointments\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1172,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-blue-900\",\n                      children: appointmentsAnalytics.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1173,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1171,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1167,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(FaStar, {\n                      className: `h-6 w-6 text-[${websiteColorPalette.primary}]`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1184,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1183,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Reviews\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1187,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-blue-900\",\n                      children: reviewsAnalytics.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1188,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1186,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1182,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(FaFileMedical, {\n                      className: `h-6 w-6 text-[${websiteColorPalette.primary}]`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1199,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Procedures\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1202,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-blue-900\",\n                      children: proceduresAnalytics.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1203,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1201,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1197,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1193,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: item,\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/_jsxDEV(\"nav\", {\n                  className: \"flex -mb-px\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `py-4 px-6 font-medium text-sm border-b-2 ${activeTab === 'overview' ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]` : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                    onClick: () => setActiveTab('overview'),\n                    children: \"Overview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1216,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `py-4 px-6 font-medium text-sm border-b-2 ${activeTab === 'appointments' ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]` : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                    onClick: () => setActiveTab('appointments'),\n                    children: \"Appointments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1226,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `py-4 px-6 font-medium text-sm border-b-2 ${activeTab === 'reviews' ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]` : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                    onClick: () => setActiveTab('reviews'),\n                    children: \"Reviews\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1236,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `py-4 px-6 font-medium text-sm border-b-2 ${activeTab === 'procedures' ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]` : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                    onClick: () => setActiveTab('procedures'),\n                    children: \"Procedures\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1246,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `py-4 px-6 font-medium text-sm border-b-2 ${activeTab === 'patients' ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]` : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                    onClick: () => setActiveTab('patients'),\n                    children: \"Patients\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1256,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1215,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: `text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(FaChartLine, {\n                        className: \"h-5 w-5 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1276,\n                        columnNumber: 27\n                      }, this), \"University Overview\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1275,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 p-6 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-96\",\n                      children: /*#__PURE__*/_jsxDEV(Bar, {\n                        data: overviewChartData,\n                        options: barChartOptions\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1282,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1281,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1280,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1273,\n                  columnNumber: 21\n                }, this), activeTab === 'appointments' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: `text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                        className: \"h-5 w-5 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1293,\n                        columnNumber: 27\n                      }, this), \"Appointment Analytics\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1292,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1291,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Total\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1301,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: appointmentsAnalytics.total\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1302,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1300,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Pending\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1305,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: appointmentsAnalytics.pending\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1306,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1304,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Completed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1309,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: appointmentsAnalytics.completed\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1310,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1308,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Cancelled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1313,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: appointmentsAnalytics.cancelled\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1314,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1312,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Today\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1317,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: appointmentsAnalytics.today\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1318,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1316,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1299,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1326,\n                          columnNumber: 29\n                        }, this), \"Appointment Status\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1325,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Doughnut, {\n                          data: appointmentsStatusChartData,\n                          options: pieChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1330,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1329,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1324,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1335,\n                          columnNumber: 29\n                        }, this), \"Appointment Types\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1334,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Pie, {\n                          data: appointmentsByTypeChartData,\n                          options: pieChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1339,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1338,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1333,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1323,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartArea, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1347,\n                          columnNumber: 29\n                        }, this), \"Appointments by Month\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1346,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Line, {\n                          data: appointmentsByMonthChartData,\n                          options: lineChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1351,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1350,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1345,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1356,\n                          columnNumber: 29\n                        }, this), \"Appointments by Day\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1355,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Bar, {\n                          data: appointmentsByDayChartData,\n                          options: barChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1360,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1359,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1354,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1344,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1290,\n                  columnNumber: 21\n                }, this), activeTab === 'reviews' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: `text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(FaStar, {\n                        className: \"h-5 w-5 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1372,\n                        columnNumber: 27\n                      }, this), \"Review Analytics\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1371,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1370,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Total\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1380,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: reviewsAnalytics.total\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1381,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1379,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Pending\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1384,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: reviewsAnalytics.pending\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1385,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1383,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Accepted\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1388,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: reviewsAnalytics.accepted\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1389,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1387,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Declined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1392,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: reviewsAnalytics.declined\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1393,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1391,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Avg. Quality\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1396,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: [reviewsAnalytics.avgProcedureQuality, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1397,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1395,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1378,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1405,\n                          columnNumber: 29\n                        }, this), \"Review Status\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1404,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Doughnut, {\n                          data: reviewsStatusChartData,\n                          options: pieChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1409,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1408,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1403,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1414,\n                          columnNumber: 29\n                        }, this), \"Procedure Types\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1413,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Pie, {\n                          data: reviewsByProcedureTypeChartData,\n                          options: pieChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1418,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1417,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1412,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1402,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FaChartArea, {\n                        className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1425,\n                        columnNumber: 27\n                      }, this), \"Reviews by Month\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1424,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-64\",\n                      children: /*#__PURE__*/_jsxDEV(Line, {\n                        data: reviewsByMonthChartData,\n                        options: lineChartOptions\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1429,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1428,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1423,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FaUserGraduate, {\n                        className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1436,\n                        columnNumber: 27\n                      }, this), \"Student Performance\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1435,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"overflow-x-auto\",\n                      children: /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                          className: \"bg-gray-50\",\n                          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              scope: \"col\",\n                              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                              children: \"Student\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1443,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              scope: \"col\",\n                              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                              children: \"Total Reviews\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1446,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              scope: \"col\",\n                              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                              children: \"Accepted\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1449,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              scope: \"col\",\n                              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                              children: \"Declined\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1452,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              scope: \"col\",\n                              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                              children: \"Pending\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1455,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1442,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1441,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          className: \"bg-white divide-y divide-gray-200\",\n                          children: Object.entries(reviewsAnalytics.byStudent).sort((a, b) => b[1].total - a[1].total).slice(0, 5).map(([student, stats]) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                              children: student\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1466,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                              children: stats.total\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1469,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800\",\n                                children: stats.accepted\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1473,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1472,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800\",\n                                children: stats.declined\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1478,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1477,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800\",\n                                children: stats.pending\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1483,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1482,\n                              columnNumber: 37\n                            }, this)]\n                          }, student, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1465,\n                            columnNumber: 35\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1460,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1440,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1439,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1434,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1369,\n                  columnNumber: 21\n                }, this), activeTab === 'procedures' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: `text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n                        className: \"h-5 w-5 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1501,\n                        columnNumber: 27\n                      }, this), \"Procedures Analytics\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1500,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1499,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Total\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1509,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: proceduresAnalytics.total\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1510,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1508,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Recent Sheets\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1513,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-900\",\n                        children: proceduresAnalytics.recentSheets.length\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1514,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1512,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1522,\n                          columnNumber: 29\n                        }, this), \"Procedures by Type\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1521,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Pie, {\n                          data: proceduresByTypeChartData,\n                          options: pieChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1526,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1525,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1520,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartArea, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1531,\n                          columnNumber: 29\n                        }, this), \"Procedures by Month\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1530,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Line, {\n                          data: proceduresByMonthChartData,\n                          options: lineChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1535,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1534,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1529,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1519,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1543,\n                          columnNumber: 29\n                        }, this), \"Top Students by Procedures\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1542,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Bar, {\n                          data: proceduresByStudentChartData,\n                          options: barChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1547,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1546,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1541,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1552,\n                          columnNumber: 29\n                        }, this), \"Top Patients by Procedures\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1551,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Bar, {\n                          data: proceduresByPatientChartData,\n                          options: barChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1556,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1555,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1550,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1540,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaFileMedical, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1564,\n                          columnNumber: 29\n                        }, this), \"Recent Procedures\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1563,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64 overflow-y-auto\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"space-y-2\",\n                          children: proceduresAnalytics.recentSheets.map((sheet, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"p-3 bg-gray-50 rounded-lg\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex justify-between items-start\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                                  className: \"text-sm font-medium text-gray-900\",\n                                  children: sheet.type\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1573,\n                                  columnNumber: 39\n                                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                  className: \"text-xs text-gray-500\",\n                                  children: [\"Patient: \", sheet.patientName]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1574,\n                                  columnNumber: 39\n                                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                  className: \"text-xs text-gray-500\",\n                                  children: [\"Student: \", sheet.studentName]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1575,\n                                  columnNumber: 39\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1572,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"text-xs text-gray-400\",\n                                children: new Date(sheet.createdAt).toLocaleDateString()\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1577,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1571,\n                              columnNumber: 35\n                            }, this)\n                          }, index, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1570,\n                            columnNumber: 33\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1568,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1567,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1562,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1588,\n                          columnNumber: 29\n                        }, this), \"Procedures Summary\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1587,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64 flex flex-col justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"grid grid-cols-2 gap-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-2xl font-bold text-blue-900\",\n                              children: proceduresAnalytics.total\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1594,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm text-gray-500\",\n                              children: \"Total Procedures\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1595,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1593,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-2xl font-bold text-green-900\",\n                              children: Object.keys(proceduresAnalytics.byType).length\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1598,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm text-gray-500\",\n                              children: \"Procedure Types\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1599,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1597,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-2xl font-bold text-purple-900\",\n                              children: Object.keys(proceduresAnalytics.byStudent).length\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1602,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm text-gray-500\",\n                              children: \"Active Students\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1603,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1601,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-2xl font-bold text-orange-900\",\n                              children: Object.keys(proceduresAnalytics.byPatient).length\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1606,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm text-gray-500\",\n                              children: \"Patients Treated\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1607,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1605,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1592,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1591,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1586,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1561,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1498,\n                  columnNumber: 21\n                }, this), activeTab === 'patients' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: `text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(FaUserAlt, {\n                        className: \"h-5 w-5 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1621,\n                        columnNumber: 27\n                      }, this), \"Patient Analytics\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1620,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1619,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1630,\n                          columnNumber: 29\n                        }, this), \"Gender Distribution\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1629,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Pie, {\n                          data: patientsByGenderChartData,\n                          options: pieChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1634,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1633,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1628,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1639,\n                          columnNumber: 29\n                        }, this), \"Age Distribution\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1638,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-64\",\n                        children: /*#__PURE__*/_jsxDEV(Bar, {\n                          data: patientsByAgeChartData,\n                          options: barChartOptions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1643,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1642,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1637,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1627,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-medium text-gray-700 mb-3 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                        className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1651,\n                        columnNumber: 27\n                      }, this), \"Top Chronic Diseases\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1650,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-64\",\n                      children: /*#__PURE__*/_jsxDEV(Bar, {\n                        data: patientsByChronicDiseaseChartData,\n                        options: barChartOptions\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1655,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1654,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1649,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1618,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1134,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1129,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"rer69SX5UxB5RB84NzQZULu2Rlg=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "AdminSidebar", "Loader", "motion", "FaChartLine", "FaCalendarAlt", "FaStar", "FaUserAlt", "FaUsers", "FaUserGraduate", "FaUserNurse", "FaUserMd", "FaClipboardCheck", "FaChart<PERSON>ie", "FaChartBar", "FaChartArea", "FaFileMedical", "Bar", "Pie", "Doughnut", "Line", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "PointElement", "LineElement", "Filler", "jsxDEV", "_jsxDEV", "websiteColorPalette", "primary", "secondary", "background", "text", "accent", "register", "colorPalette", "main", "border", "red", "orange", "amber", "Analytics", "_s", "sidebarOpen", "setSidebarOpen", "activeTab", "setActiveTab", "appointments", "setAppointments", "reviews", "setReviews", "patients", "setPatients", "students", "setStudents", "supervisors", "setSupervisors", "assistants", "setAssistants", "treatmentSheets", "setTreatmentSheets", "loading", "setLoading", "error", "setError", "navigate", "user", "token", "fetchData", "university", "config", "headers", "Authorization", "initialRequests", "get", "process", "env", "REACT_APP_API_URL", "encodeURIComponent", "appointmentsRes", "patientsRes", "studentsRes", "supervisorsRes", "assistants<PERSON>es", "treatmentSheetsRes", "Promise", "all", "map", "request", "catch", "console", "message", "data", "log", "length", "allReviews", "reviewPromises", "student", "url", "studentId", "name", "then", "response", "_id", "fallback<PERSON><PERSON>r", "reviewsResults", "for<PERSON>ach", "result", "Array", "isArray", "filteredReviews", "review", "signature", "reviewWithoutSignature", "from", "Map", "values", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response4$data", "_err$response5", "errorMessage", "status", "appointmentsAnalytics", "total", "pending", "filter", "a", "completed", "cancelled", "today", "apptDate", "Date", "date", "todayDate", "toDateString", "thisWeek", "startOfWeek", "setDate", "getDate", "getDay", "endOfWeek", "byMonth", "getAppointmentsByMonth", "byDay", "getAppointmentsByDay", "byType", "getAppointmentsByType", "patientId", "nationalId", "reviewsAnalytics", "r", "accepted", "declined", "avgProcedureQuality", "calculateAverageRating", "avgPatientInteraction", "byProcedureType", "getReviewsByProcedureType", "getReviewsByMonth", "byStudent", "getReviewsByStudent", "patientsAnalytics", "byAge", "getPatientsAgeDistribution", "by<PERSON><PERSON>", "getPatientsGenderDistribution", "byChronicDisease", "getPatientsChronicDiseaseDistribution", "peopleAnalytics", "proceduresAnalytics", "getProceduresByType", "getProceduresByMonth", "getProceduresByStudent", "byPatient", "getProceduresByPatient", "recentSheets", "sort", "b", "createdAt", "slice", "overviewChartData", "labels", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "hoverOffset", "appointmentsStatusChartData", "appointmentsByMonthChartData", "item", "month", "count", "tension", "fill", "appointmentsByDayChartData", "day", "appointmentsByTypeChartData", "Object", "keys", "_", "index", "colorKeys", "reviewsStatusChartData", "reviewsByProcedureTypeChartData", "reviewsByMonthChartData", "patientsByAgeChartData", "patientsByGenderChartData", "patientsByChronicDiseaseChartData", "proceduresByTypeChartData", "proceduresByMonthChartData", "proceduresByStudentChartData", "proceduresByPatientChartData", "barChartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "font", "family", "size", "padding", "usePointStyle", "boxWidth", "title", "display", "weight", "color", "bottom", "tooltip", "titleColor", "bodyColor", "boxPadding", "bodyFont", "titleFont", "scales", "y", "beginAtZero", "grid", "ticks", "x", "pieChartOptions", "cutout", "radius", "lineChartOptions", "months", "appointmentsByMonth", "appointment", "getMonth", "days", "appointmentsByDay", "appointmentsByType", "type", "reviewsByProcedureType", "procedureType", "procedure", "entries", "sortedEntries", "top5", "others", "reduce", "sum", "entry", "fromEntries", "reviewsByMonth", "dateStr", "submittedDate", "reviewedDate", "isNaN", "getTime", "reviewsByStudent", "studentName", "foundStudent", "find", "s", "sortedStudents", "field", "validReviews", "ratings", "toFixed", "ageGroups", "patient", "age", "genderDistribution", "gender", "diseaseDistribution", "medicalInfo", "chronicDiseases", "disease", "sheets", "proceduresByType", "sheet", "proceduresByMonth", "proceduresByStudent", "proceduresByPatient", "patientName", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "viewBox", "fillRule", "d", "clipRule", "duration", "variants", "whileInView", "viewport", "once", "onClick", "options", "scope", "stats", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/admin/Analytics.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AdminSidebar from './AdminSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport {\n  FaChartLine,\n  FaCalendarAlt,\n  FaStar,\n  FaUserAlt,\n  FaUsers,\n  FaUserGraduate,\n  FaUserNurse,\n  FaUserMd,\n  FaClipboardCheck,\n  FaChartPie,\n  FaChartBar,\n  FaChartArea,\n  FaFileMedical\n} from 'react-icons/fa';\nimport { Bar, Pie, Doughnut, Line } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n  PointElement,\n  LineElement,\n  Filler\n} from 'chart.js';\n\n// Website color palette\nconst websiteColorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n  PointElement,\n  LineElement,\n  Filler\n);\n\n// Define a consistent color palette based on the website design system\nconst colorPalette = {\n  primary: { main: `${websiteColorPalette.primary}CC`, border: websiteColorPalette.primary },\n  secondary: { main: `${websiteColorPalette.secondary}CC`, border: websiteColorPalette.secondary },\n  accent: { main: `${websiteColorPalette.accent}CC`, border: websiteColorPalette.accent },\n  red: { main: 'rgba(239, 68, 68, 0.8)', border: 'rgba(239, 68, 68, 1)' },\n  orange: { main: 'rgba(249, 115, 22, 0.8)', border: 'rgba(249, 115, 22, 1)' },\n  amber: { main: 'rgba(245, 158, 11, 0.8)', border: 'rgba(245, 158, 11, 1)' },\n};\n\nconst Analytics = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [appointments, setAppointments] = useState([]);\n  const [reviews, setReviews] = useState([]);\n  const [patients, setPatients] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [supervisors, setSupervisors] = useState([]);\n  const [assistants, setAssistants] = useState([]);\n  const [treatmentSheets, setTreatmentSheets] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        setError('Please log in to view analytics.');\n        setLoading(false);\n        return;\n      }\n\n      if (!user.university) {\n        setError('User profile incomplete. Missing university information.');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        const config = { headers: { Authorization: `Bearer ${token}` } };\n\n        // First, get students, supervisors, and other data\n        const initialRequests = [\n          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/appointments?university=${encodeURIComponent(user.university)}`, config),\n          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/patients?university=${encodeURIComponent(user.university)}`, config),\n          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/students?university=${encodeURIComponent(user.university)}`, config),\n          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/supervisors?university=${encodeURIComponent(user.university)}`, config),\n          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/assistants?university=${encodeURIComponent(user.university)}`, config),\n          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/treatment-sheets`, config)\n        ];\n\n        // Execute initial requests and handle errors individually\n        const [appointmentsRes, patientsRes, studentsRes, supervisorsRes, assistantsRes, treatmentSheetsRes] = await Promise.all(\n          initialRequests.map(request =>\n            request.catch(error => {\n              console.error('API request failed:', error.message);\n              return { data: [] }; // Return empty data on error\n            })\n          )\n        );\n\n        const students = studentsRes.data || [];\n        console.log(`Found ${students.length} students in university ${user.university}`);\n\n        // Now fetch reviews for each student in the university\n        let allReviews = [];\n\n        if (students.length > 0) {\n          console.log('Fetching reviews for students...');\n\n          // Create an array of promises for fetching reviews for each student\n          const reviewPromises = students.map(student => {\n            const url = `${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student.studentId}`;\n            console.log(`Fetching reviews for student ${student.name} with URL: ${url}`);\n\n            return axios.get(url, config)\n              .then(response => {\n                console.log(`Successfully fetched ${response.data.length} reviews for student ${student.name}`);\n                return response;\n              })\n              .catch(error => {\n                console.error(`Error fetching reviews for student ${student.name}:`, error.message);\n                // Try with _id as fallback\n                return axios.get(`${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student._id}`, config)\n                  .then(response => {\n                    console.log(`Fallback successful! Fetched ${response.data.length} reviews for student ${student.name}`);\n                    return response;\n                  })\n                  .catch(fallbackError => {\n                    console.error(`Fallback also failed for student ${student.name}:`, fallbackError.message);\n                    return { data: [] }; // Return empty array on error\n                  });\n              });\n          });\n\n          // Execute all promises in parallel\n          const reviewsResults = await Promise.all(reviewPromises);\n\n          // Combine all reviews into a single array\n          reviewsResults.forEach(result => {\n            if (result.data && Array.isArray(result.data)) {\n              // Filter out storage signature from reviews\n              const filteredReviews = result.data.map(review => {\n                // Create a new object without the signature property\n                const { signature, ...reviewWithoutSignature } = review;\n                return reviewWithoutSignature;\n              });\n\n              allReviews = [...allReviews, ...filteredReviews];\n            }\n          });\n\n          // Remove any duplicate reviews (in case a student appears in multiple queries)\n          allReviews = Array.from(new Map(allReviews.map(review => [review._id, review])).values());\n\n          console.log(`Found ${allReviews.length} total reviews for students in university ${user.university}`);\n        }\n\n        setAppointments(appointmentsRes.data || []);\n        setReviews(allReviews);\n        setPatients(patientsRes.data || []);\n        setStudents(students);\n        setSupervisors(supervisorsRes.data || []);\n        setAssistants(assistantsRes.data || []);\n        setTreatmentSheets(treatmentSheetsRes.data || []);\n\n        if (\n          appointmentsRes.data.length === 0 &&\n          allReviews.length === 0 &&\n          patientsRes.data.length === 0 &&\n          treatmentSheetsRes.data.length === 0\n        ) {\n          setError('No data found for analytics.');\n        }\n      } catch (err) {\n        console.error('Fetch error:', err.response?.data || err.message);\n        const errorMessage =\n          err.response?.status === 404\n            ? 'Analytics endpoints not found.'\n            : err.response?.status === 401\n            ? 'Unauthorized. Please log in again.'\n            : err.response?.data?.message || 'Failed to load analytics';\n        setError(errorMessage);\n        if (err.response?.status === 401) navigate('/login');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user, token, navigate]);\n\n  // Process appointments data\n  const appointmentsAnalytics = {\n    total: appointments.length,\n    pending: appointments.filter((a) => a.status === 'pending').length,\n    completed: appointments.filter((a) => a.status === 'completed').length,\n    cancelled: appointments.filter((a) => a.status === 'cancelled').length,\n    today: appointments.filter((a) => {\n      const apptDate = new Date(a.date);\n      const todayDate = new Date();\n      return apptDate.toDateString() === todayDate.toDateString();\n    }).length,\n    thisWeek: appointments.filter((a) => {\n      const apptDate = new Date(a.date);\n      const today = new Date();\n      const startOfWeek = new Date(today);\n      startOfWeek.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)\n      const endOfWeek = new Date(startOfWeek);\n      endOfWeek.setDate(startOfWeek.getDate() + 6); // End of week (Saturday)\n      return apptDate >= startOfWeek && apptDate <= endOfWeek;\n    }).length,\n    byMonth: getAppointmentsByMonth(appointments),\n    byDay: getAppointmentsByDay(appointments),\n    byType: getAppointmentsByType(appointments)\n  };\n\n  // Filter out signature storage reviews\n  const filteredReviews = reviews.filter(review =>\n    !(review.patientId && review.patientId.nationalId === 'signature-storage')\n  );\n\n  // Process reviews data\n  const reviewsAnalytics = {\n    total: filteredReviews.length,\n    pending: filteredReviews.filter(r => r.status === 'pending').length,\n    accepted: filteredReviews.filter(r => r.status === 'accepted').length,\n    declined: filteredReviews.filter(r => r.status === 'declined' || r.status === 'denied').length,\n    avgProcedureQuality: calculateAverageRating(filteredReviews, 'procedureQuality'),\n    avgPatientInteraction: calculateAverageRating(filteredReviews, 'patientInteraction'),\n    byProcedureType: getReviewsByProcedureType(filteredReviews),\n    byMonth: getReviewsByMonth(filteredReviews),\n    byStudent: getReviewsByStudent(filteredReviews)\n  };\n\n  console.log('Reviews analytics:', reviewsAnalytics);\n\n  // Process patients data\n  const patientsAnalytics = {\n    total: patients.length,\n    byAge: getPatientsAgeDistribution(patients),\n    byGender: getPatientsGenderDistribution(patients),\n    byChronicDisease: getPatientsChronicDiseaseDistribution(patients)\n  };\n\n  // People analytics\n  const peopleAnalytics = {\n    students: students.length,\n    supervisors: supervisors.length,\n    assistants: assistants.length,\n    patients: patients.length\n  };\n\n  // Process treatment sheets data\n  const proceduresAnalytics = {\n    total: treatmentSheets.length,\n    byType: getProceduresByType(treatmentSheets),\n    byMonth: getProceduresByMonth(treatmentSheets),\n    byStudent: getProceduresByStudent(treatmentSheets),\n    byPatient: getProceduresByPatient(treatmentSheets),\n    recentSheets: treatmentSheets\n      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))\n      .slice(0, 10)\n  };\n\n  // Chart data for overview\n  const overviewChartData = {\n    labels: ['Patients', 'Students', 'Supervisors', 'Assistants', 'Appointments', 'Reviews', 'Procedures'],\n    datasets: [\n      {\n        label: 'Counts',\n        data: [\n          peopleAnalytics.patients,\n          peopleAnalytics.students,\n          peopleAnalytics.supervisors,\n          peopleAnalytics.assistants,\n          appointmentsAnalytics.total,\n          reviewsAnalytics.total,\n          proceduresAnalytics.total,\n        ],\n        backgroundColor: [\n          colorPalette.primary.main,\n          colorPalette.secondary.main,\n          colorPalette.accent.main,\n          colorPalette.primary.main,\n          colorPalette.secondary.main,\n          colorPalette.accent.main,\n          colorPalette.orange.main,\n        ],\n        borderColor: [\n          colorPalette.primary.border,\n          colorPalette.secondary.border,\n          colorPalette.accent.border,\n          colorPalette.primary.border,\n          colorPalette.secondary.border,\n          colorPalette.accent.border,\n          colorPalette.orange.border,\n        ],\n        borderWidth: 1,\n        borderRadius: 6,\n        hoverOffset: 4,\n      },\n    ],\n  };\n\n  // Appointments status chart data\n  const appointmentsStatusChartData = {\n    labels: ['Pending', 'Completed', 'Cancelled'],\n    datasets: [\n      {\n        data: [\n          appointmentsAnalytics.pending,\n          appointmentsAnalytics.completed,\n          appointmentsAnalytics.cancelled,\n        ],\n        backgroundColor: [\n          colorPalette.amber.main,\n          colorPalette.accent.main,\n          colorPalette.red.main,\n        ],\n        borderColor: [\n          colorPalette.amber.border,\n          colorPalette.accent.border,\n          colorPalette.red.border,\n        ],\n        borderWidth: 1,\n        hoverOffset: 8,\n      },\n    ],\n  };\n\n  // Appointments by month chart data\n  const appointmentsByMonthChartData = {\n    labels: appointmentsAnalytics.byMonth.map(item => item.month),\n    datasets: [\n      {\n        label: 'Appointments',\n        data: appointmentsAnalytics.byMonth.map(item => item.count),\n        backgroundColor: colorPalette.primary.main,\n        borderColor: colorPalette.primary.border,\n        borderWidth: 1,\n        tension: 0.4,\n        fill: true,\n      },\n    ],\n  };\n\n  // Appointments by day chart data\n  const appointmentsByDayChartData = {\n    labels: appointmentsAnalytics.byDay.map(item => item.day),\n    datasets: [\n      {\n        label: 'Appointments',\n        data: appointmentsAnalytics.byDay.map(item => item.count),\n        backgroundColor: colorPalette.secondary.main,\n        borderColor: colorPalette.secondary.border,\n        borderWidth: 1,\n        borderRadius: 6,\n      },\n    ],\n  };\n\n  // Appointments by type chart data\n  const appointmentsByTypeChartData = {\n    labels: Object.keys(appointmentsAnalytics.byType),\n    datasets: [\n      {\n        data: Object.values(appointmentsAnalytics.byType),\n        backgroundColor: Object.keys(appointmentsAnalytics.byType).map((_, index) => {\n          const colorKeys = Object.keys(colorPalette);\n          return colorPalette[colorKeys[index % colorKeys.length]].main;\n        }),\n        borderColor: Object.keys(appointmentsAnalytics.byType).map((_, index) => {\n          const colorKeys = Object.keys(colorPalette);\n          return colorPalette[colorKeys[index % colorKeys.length]].border;\n        }),\n        borderWidth: 1,\n        hoverOffset: 8,\n      },\n    ],\n  };\n\n  // Reviews status chart data\n  const reviewsStatusChartData = {\n    labels: ['Pending', 'Accepted', 'Declined'],\n    datasets: [\n      {\n        data: [\n          reviewsAnalytics.pending,\n          reviewsAnalytics.accepted,\n          reviewsAnalytics.declined,\n        ],\n        backgroundColor: [\n          colorPalette.amber.main,\n          colorPalette.accent.main,\n          colorPalette.red.main,\n        ],\n        borderColor: [\n          colorPalette.amber.border,\n          colorPalette.accent.border,\n          colorPalette.red.border,\n        ],\n        borderWidth: 1,\n        hoverOffset: 8,\n      },\n    ],\n  };\n\n  // Reviews by procedure type chart data\n  const reviewsByProcedureTypeChartData = {\n    labels: Object.keys(reviewsAnalytics.byProcedureType),\n    datasets: [\n      {\n        data: Object.values(reviewsAnalytics.byProcedureType),\n        backgroundColor: Object.keys(reviewsAnalytics.byProcedureType).map((_, index) => {\n          const colorKeys = Object.keys(colorPalette);\n          return colorPalette[colorKeys[index % colorKeys.length]].main;\n        }),\n        borderColor: Object.keys(reviewsAnalytics.byProcedureType).map((_, index) => {\n          const colorKeys = Object.keys(colorPalette);\n          return colorPalette[colorKeys[index % colorKeys.length]].border;\n        }),\n        borderWidth: 1,\n        hoverOffset: 8,\n      },\n    ],\n  };\n\n  // Reviews by month chart data\n  const reviewsByMonthChartData = {\n    labels: reviewsAnalytics.byMonth.map(item => item.month),\n    datasets: [\n      {\n        label: 'Reviews',\n        data: reviewsAnalytics.byMonth.map(item => item.count),\n        backgroundColor: colorPalette.secondary.main,\n        borderColor: colorPalette.secondary.border,\n        borderWidth: 1,\n        tension: 0.4,\n        fill: true,\n      },\n    ],\n  };\n\n  // Patients by age chart data\n  const patientsByAgeChartData = {\n    labels: Object.keys(patientsAnalytics.byAge),\n    datasets: [\n      {\n        label: 'Patients',\n        data: Object.values(patientsAnalytics.byAge),\n        backgroundColor: colorPalette.accent.main,\n        borderColor: colorPalette.accent.border,\n        borderWidth: 1,\n        borderRadius: 6,\n      },\n    ],\n  };\n\n  // Patients by gender chart data\n  const patientsByGenderChartData = {\n    labels: Object.keys(patientsAnalytics.byGender),\n    datasets: [\n      {\n        data: Object.values(patientsAnalytics.byGender),\n        backgroundColor: [\n          colorPalette.primary.main,\n          colorPalette.secondary.main,\n          colorPalette.accent.main,\n        ],\n        borderColor: [\n          colorPalette.primary.border,\n          colorPalette.secondary.border,\n          colorPalette.accent.border,\n        ],\n        borderWidth: 1,\n        hoverOffset: 8,\n      },\n    ],\n  };\n\n  // Patients by chronic disease chart data\n  const patientsByChronicDiseaseChartData = {\n    labels: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5), // Top 5 chronic diseases\n    datasets: [\n      {\n        label: 'Patients',\n        data: Object.values(patientsAnalytics.byChronicDisease).slice(0, 5), // Top 5 chronic diseases\n        backgroundColor: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5).map((_, index) => {\n          const colorKeys = Object.keys(colorPalette);\n          return colorPalette[colorKeys[index % colorKeys.length]].main;\n        }),\n        borderColor: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5).map((_, index) => {\n          const colorKeys = Object.keys(colorPalette);\n          return colorPalette[colorKeys[index % colorKeys.length]].border;\n        }),\n        borderWidth: 1,\n        borderRadius: 6,\n      },\n    ],\n  };\n\n  // Procedures by type chart data\n  const proceduresByTypeChartData = {\n    labels: Object.keys(proceduresAnalytics.byType),\n    datasets: [\n      {\n        data: Object.values(proceduresAnalytics.byType),\n        backgroundColor: Object.keys(proceduresAnalytics.byType).map((_, index) => {\n          const colorKeys = Object.keys(colorPalette);\n          return colorPalette[colorKeys[index % colorKeys.length]].main;\n        }),\n        borderColor: Object.keys(proceduresAnalytics.byType).map((_, index) => {\n          const colorKeys = Object.keys(colorPalette);\n          return colorPalette[colorKeys[index % colorKeys.length]].border;\n        }),\n        borderWidth: 1,\n        hoverOffset: 8,\n      },\n    ],\n  };\n\n  // Procedures by month chart data\n  const proceduresByMonthChartData = {\n    labels: proceduresAnalytics.byMonth.map(item => item.month),\n    datasets: [\n      {\n        label: 'Procedures',\n        data: proceduresAnalytics.byMonth.map(item => item.count),\n        backgroundColor: colorPalette.accent.main,\n        borderColor: colorPalette.accent.border,\n        borderWidth: 1,\n        tension: 0.4,\n        fill: true,\n      },\n    ],\n  };\n\n  // Procedures by student chart data\n  const proceduresByStudentChartData = {\n    labels: Object.keys(proceduresAnalytics.byStudent).slice(0, 10), // Top 10 students\n    datasets: [\n      {\n        label: 'Procedures',\n        data: Object.values(proceduresAnalytics.byStudent).slice(0, 10),\n        backgroundColor: colorPalette.primary.main,\n        borderColor: colorPalette.primary.border,\n        borderWidth: 1,\n        borderRadius: 6,\n      },\n    ],\n  };\n\n  // Procedures by patient chart data\n  const proceduresByPatientChartData = {\n    labels: Object.keys(proceduresAnalytics.byPatient).slice(0, 8), // Top 8 patients\n    datasets: [\n      {\n        label: 'Procedures',\n        data: Object.values(proceduresAnalytics.byPatient).slice(0, 8),\n        backgroundColor: colorPalette.secondary.main,\n        borderColor: colorPalette.secondary.border,\n        borderWidth: 1,\n        borderRadius: 6,\n      },\n    ],\n  };\n\n  // Chart options\n  const barChartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 12\n          },\n          padding: 20,\n          usePointStyle: true,\n          boxWidth: 8\n        }\n      },\n      title: {\n        display: false,\n        font: {\n          family: \"'Inter', sans-serif\",\n          size: 16,\n          weight: 'bold'\n        },\n        color: '#1e3a8a',\n        padding: {\n          bottom: 20\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        titleColor: '#1e3a8a',\n        bodyColor: '#4b5563',\n        borderColor: '#e5e7eb',\n        borderWidth: 1,\n        padding: 12,\n        boxPadding: 6,\n        usePointStyle: true,\n        bodyFont: {\n          family: \"'Inter', sans-serif\"\n        },\n        titleFont: {\n          family: \"'Inter', sans-serif\",\n          weight: 'bold'\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: 'rgba(226, 232, 240, 0.6)'\n        },\n        ticks: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 11\n          },\n          color: '#64748b'\n        }\n      },\n      x: {\n        grid: {\n          display: false\n        },\n        ticks: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 11\n          },\n          color: '#64748b'\n        }\n      }\n    }\n  };\n\n  // Pie chart options\n  const pieChartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'right',\n        labels: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 12\n          },\n          padding: 20,\n          usePointStyle: true,\n          boxWidth: 8\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        titleColor: '#1e3a8a',\n        bodyColor: '#4b5563',\n        borderColor: '#e5e7eb',\n        borderWidth: 1,\n        padding: 12,\n        boxPadding: 6,\n        usePointStyle: true,\n        bodyFont: {\n          family: \"'Inter', sans-serif\"\n        },\n        titleFont: {\n          family: \"'Inter', sans-serif\",\n          weight: 'bold'\n        }\n      }\n    },\n    cutout: '60%',\n    radius: '90%'\n  };\n\n  // Line chart options\n  const lineChartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 12\n          },\n          padding: 20,\n          usePointStyle: true,\n          boxWidth: 8\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        titleColor: '#1e3a8a',\n        bodyColor: '#4b5563',\n        borderColor: '#e5e7eb',\n        borderWidth: 1,\n        padding: 12,\n        boxPadding: 6,\n        usePointStyle: true,\n        bodyFont: {\n          family: \"'Inter', sans-serif\"\n        },\n        titleFont: {\n          family: \"'Inter', sans-serif\",\n          weight: 'bold'\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: 'rgba(226, 232, 240, 0.6)'\n        },\n        ticks: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 11\n          },\n          color: '#64748b'\n        }\n      },\n      x: {\n        grid: {\n          display: false\n        },\n        ticks: {\n          font: {\n            family: \"'Inter', sans-serif\",\n            size: 11\n          },\n          color: '#64748b'\n        }\n      }\n    }\n  };\n\n  // Helper function to get appointments by month\n  function getAppointmentsByMonth(appointments) {\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const appointmentsByMonth = {};\n\n    // Initialize all months with 0\n    months.forEach(month => {\n      appointmentsByMonth[month] = 0;\n    });\n\n    // Count appointments by month\n    appointments.forEach(appointment => {\n      const date = new Date(appointment.date);\n      const month = months[date.getMonth()];\n      appointmentsByMonth[month]++;\n    });\n\n    // Convert to array for chart\n    return months.map(month => ({\n      month,\n      count: appointmentsByMonth[month]\n    }));\n  }\n\n  // Helper function to get appointments by day of week\n  function getAppointmentsByDay(appointments) {\n    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n    const appointmentsByDay = {};\n\n    // Initialize all days with 0\n    days.forEach(day => {\n      appointmentsByDay[day] = 0;\n    });\n\n    // Count appointments by day\n    appointments.forEach(appointment => {\n      const date = new Date(appointment.date);\n      const day = days[date.getDay()];\n      appointmentsByDay[day]++;\n    });\n\n    // Convert to array for chart\n    return days.map(day => ({\n      day,\n      count: appointmentsByDay[day]\n    }));\n  }\n\n  // Helper function to get appointments by type\n  function getAppointmentsByType(appointments) {\n    const appointmentsByType = {};\n\n    // Count appointments by type\n    appointments.forEach(appointment => {\n      const type = appointment.type || 'Other';\n      appointmentsByType[type] = (appointmentsByType[type] || 0) + 1;\n    });\n\n    return appointmentsByType;\n  }\n\n  // Helper function to get reviews by procedure type\n  function getReviewsByProcedureType(reviews) {\n    const reviewsByProcedureType = {};\n\n    // Count reviews by procedure type\n    reviews.forEach(review => {\n      // Skip signature storage reviews\n      if (review.patientId && review.patientId.nationalId === 'signature-storage') return;\n\n      let type = 'Other';\n\n      // Handle different possible formats of procedureType\n      if (review.procedureType) {\n        type = review.procedureType;\n      } else if (review.procedure && review.procedure.type) {\n        type = review.procedure.type;\n      }\n\n      reviewsByProcedureType[type] = (reviewsByProcedureType[type] || 0) + 1;\n    });\n\n    // If we have too many categories, limit to top 5 plus \"Other\"\n    const entries = Object.entries(reviewsByProcedureType);\n    if (entries.length > 6) {\n      const sortedEntries = entries.sort((a, b) => b[1] - a[1]);\n      const top5 = sortedEntries.slice(0, 5);\n      const others = sortedEntries.slice(5).reduce((sum, entry) => sum + entry[1], 0);\n\n      const result = Object.fromEntries(top5);\n      result['Other'] = others;\n      return result;\n    }\n\n    return reviewsByProcedureType;\n  }\n\n  // Helper function to get reviews by month\n  function getReviewsByMonth(reviews) {\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const reviewsByMonth = {};\n\n    // Initialize all months with 0\n    months.forEach(month => {\n      reviewsByMonth[month] = 0;\n    });\n\n    // Count reviews by month\n    reviews.forEach(review => {\n      try {\n        // Skip signature storage reviews\n        if (review.patientId && review.patientId.nationalId === 'signature-storage') return;\n\n        // Try different date fields that might exist in the review object\n        let dateStr = null;\n        if (review.submittedDate) dateStr = review.submittedDate;\n        else if (review.createdAt) dateStr = review.createdAt;\n        else if (review.date) dateStr = review.date;\n        else if (review.reviewedDate) dateStr = review.reviewedDate;\n\n        if (!dateStr) return; // Skip if no valid date found\n\n        const date = new Date(dateStr);\n        if (isNaN(date.getTime())) return; // Skip if date is invalid\n\n        const month = months[date.getMonth()];\n        reviewsByMonth[month]++;\n      } catch (error) {\n        console.error('Error processing review date:', error);\n      }\n    });\n\n    // Convert to array for chart\n    return months.map(month => ({\n      month,\n      count: reviewsByMonth[month]\n    }));\n  }\n\n  // Helper function to get reviews by student\n  function getReviewsByStudent(reviews) {\n    const reviewsByStudent = {};\n\n    // Count reviews by student\n    reviews.forEach(review => {\n      // Skip signature storage reviews\n      if (review.patientId && review.patientId.nationalId === 'signature-storage') return;\n\n      // Get student name from different possible formats\n      let student = 'Unknown';\n      if (review.studentName) {\n        student = review.studentName;\n      } else if (review.student && review.student.name) {\n        student = review.student.name;\n      } else if (review.studentId) {\n        // Try to find student name from students array\n        const foundStudent = students.find(s => s._id === review.studentId || s.studentId === review.studentId);\n        if (foundStudent) {\n          student = foundStudent.name;\n        }\n      }\n\n      if (!reviewsByStudent[student]) {\n        reviewsByStudent[student] = {\n          total: 0,\n          accepted: 0,\n          declined: 0,\n          pending: 0\n        };\n      }\n\n      reviewsByStudent[student].total++;\n      if (review.status === 'accepted') reviewsByStudent[student].accepted++;\n      else if (review.status === 'declined' || review.status === 'denied') reviewsByStudent[student].declined++;\n      else reviewsByStudent[student].pending++;\n    });\n\n    // Sort by total reviews and limit to top 10 students\n    const sortedStudents = Object.entries(reviewsByStudent)\n      .sort((a, b) => b[1].total - a[1].total)\n      .slice(0, 10);\n\n    return Object.fromEntries(sortedStudents);\n  }\n\n  // Helper function to calculate average rating\n  function calculateAverageRating(reviews, field) {\n    // Filter reviews that have a valid rating for the specified field\n    const validReviews = reviews.filter(review => {\n      // Skip signature storage reviews\n      if (review.patientId && review.patientId.nationalId === 'signature-storage') return false;\n\n      // Check direct field\n      if (review[field] && typeof review[field] === 'number' && review[field] > 0) {\n        return true;\n      }\n\n      // Check nested fields (e.g., ratings.procedureQuality)\n      if (review.ratings && review.ratings[field] && typeof review.ratings[field] === 'number' && review.ratings[field] > 0) {\n        return true;\n      }\n\n      return false;\n    });\n\n    if (validReviews.length === 0) return 0;\n\n    // Calculate sum considering both direct and nested fields\n    const sum = validReviews.reduce((total, review) => {\n      if (review[field] && typeof review[field] === 'number') {\n        return total + review[field];\n      } else if (review.ratings && review.ratings[field] && typeof review.ratings[field] === 'number') {\n        return total + review.ratings[field];\n      }\n      return total;\n    }, 0);\n\n    return (sum / validReviews.length).toFixed(1);\n  }\n\n  // Helper function to get patients age distribution\n  function getPatientsAgeDistribution(patients) {\n    const ageGroups = {\n      '0-18': 0,\n      '19-30': 0,\n      '31-45': 0,\n      '46-60': 0,\n      '61+': 0\n    };\n\n    patients.forEach(patient => {\n      const age = patient.age || 0;\n      if (age <= 18) ageGroups['0-18']++;\n      else if (age <= 30) ageGroups['19-30']++;\n      else if (age <= 45) ageGroups['31-45']++;\n      else if (age <= 60) ageGroups['46-60']++;\n      else ageGroups['61+']++;\n    });\n\n    return ageGroups;\n  }\n\n  // Helper function to get patients gender distribution\n  function getPatientsGenderDistribution(patients) {\n    const genderDistribution = {\n      'Male': 0,\n      'Female': 0,\n      'Other': 0\n    };\n\n    patients.forEach(patient => {\n      const gender = patient.gender || 'Other';\n      genderDistribution[gender] = (genderDistribution[gender] || 0) + 1;\n    });\n\n    return genderDistribution;\n  }\n\n  // Helper function to get patients chronic disease distribution\n  function getPatientsChronicDiseaseDistribution(patients) {\n    const diseaseDistribution = {};\n\n    patients.forEach(patient => {\n      if (patient.medicalInfo && patient.medicalInfo.chronicDiseases) {\n        patient.medicalInfo.chronicDiseases.forEach(disease => {\n          diseaseDistribution[disease] = (diseaseDistribution[disease] || 0) + 1;\n        });\n      }\n    });\n\n    // Sort by count and return\n    return Object.fromEntries(\n      Object.entries(diseaseDistribution).sort((a, b) => b[1] - a[1])\n    );\n  }\n\n  // Helper function to get procedures by type\n  function getProceduresByType(sheets) {\n    const proceduresByType = {};\n\n    sheets.forEach(sheet => {\n      const type = sheet.type || 'Other';\n      proceduresByType[type] = (proceduresByType[type] || 0) + 1;\n    });\n\n    return proceduresByType;\n  }\n\n  // Helper function to get procedures by month\n  function getProceduresByMonth(sheets) {\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const proceduresByMonth = {};\n\n    // Initialize all months with 0\n    months.forEach(month => {\n      proceduresByMonth[month] = 0;\n    });\n\n    // Count procedures by month\n    sheets.forEach(sheet => {\n      try {\n        const date = new Date(sheet.createdAt);\n        if (isNaN(date.getTime())) return; // Skip if date is invalid\n\n        const month = months[date.getMonth()];\n        proceduresByMonth[month]++;\n      } catch (error) {\n        console.error('Error processing sheet date:', error);\n      }\n    });\n\n    // Convert to array for chart\n    return months.map(month => ({\n      month,\n      count: proceduresByMonth[month]\n    }));\n  }\n\n  // Helper function to get procedures by student\n  function getProceduresByStudent(sheets) {\n    const proceduresByStudent = {};\n\n    sheets.forEach(sheet => {\n      const student = sheet.studentName || 'Unknown Student';\n      proceduresByStudent[student] = (proceduresByStudent[student] || 0) + 1;\n    });\n\n    // Sort by count and return\n    return Object.fromEntries(\n      Object.entries(proceduresByStudent).sort((a, b) => b[1] - a[1])\n    );\n  }\n\n  // Helper function to get procedures by patient\n  function getProceduresByPatient(sheets) {\n    const proceduresByPatient = {};\n\n    sheets.forEach(sheet => {\n      const patient = sheet.patientName || 'Unknown Patient';\n      proceduresByPatient[patient] = (proceduresByPatient[patient] || 0) + 1;\n    });\n\n    // Sort by count and return top 10\n    return Object.fromEntries(\n      Object.entries(proceduresByPatient).sort((a, b) => b[1] - a[1]).slice(0, 10)\n    );\n  }\n\n  const container = {\n    hidden: { opacity: 0 },\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\n  };\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    show: { opacity: 1, y: 0 },\n  };\n\n  if (loading) return <Loader />;\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n        <main className=\"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n              >\n                <div className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 text-red-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <p className=\"text-red-700 font-medium\">{error}</p>\n                </div>\n              </motion.div>\n            )}\n            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>\n              <div className=\"mb-8\">\n                <h1 className={`text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>Analytics</h1>\n                <p className={`text-[${websiteColorPalette.text}]`}>View analytics for your university</p>\n              </div>\n\n              {/* Analytics Overview Cards */}\n              <motion.div\n                variants={container}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true }}\n                className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\"\n              >\n                <motion.div\n                  variants={item}\n                  className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\n                >\n                  <div className=\"flex items-center\">\n                    <div className=\"bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300\">\n                      <FaCalendarAlt className={`h-6 w-6 text-[${websiteColorPalette.primary}]`} />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-500\">Total Appointments</p>\n                      <p className=\"text-2xl font-bold text-blue-900\">{appointmentsAnalytics.total}</p>\n                    </div>\n                  </div>\n                </motion.div>\n\n                <motion.div\n                  variants={item}\n                  className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\n                >\n                  <div className=\"flex items-center\">\n                    <div className=\"bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300\">\n                      <FaStar className={`h-6 w-6 text-[${websiteColorPalette.primary}]`} />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-500\">Total Reviews</p>\n                      <p className=\"text-2xl font-bold text-blue-900\">{reviewsAnalytics.total}</p>\n                    </div>\n                  </div>\n                </motion.div>\n\n                <motion.div\n                  variants={item}\n                  className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\n                >\n                  <div className=\"flex items-center\">\n                    <div className=\"bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300\">\n                      <FaFileMedical className={`h-6 w-6 text-[${websiteColorPalette.primary}]`} />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-500\">Total Procedures</p>\n                      <p className=\"text-2xl font-bold text-blue-900\">{proceduresAnalytics.total}</p>\n                    </div>\n                  </div>\n                </motion.div>\n              </motion.div>\n\n              {/* Tabs Navigation */}\n              <motion.div\n                variants={item}\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden mb-8\"\n              >\n                <div className=\"border-b border-gray-200\">\n                  <nav className=\"flex -mb-px\">\n                    <button\n                      className={`py-4 px-6 font-medium text-sm border-b-2 ${\n                        activeTab === 'overview'\n                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`\n                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                      }`}\n                      onClick={() => setActiveTab('overview')}\n                    >\n                      Overview\n                    </button>\n                    <button\n                      className={`py-4 px-6 font-medium text-sm border-b-2 ${\n                        activeTab === 'appointments'\n                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`\n                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                      }`}\n                      onClick={() => setActiveTab('appointments')}\n                    >\n                      Appointments\n                    </button>\n                    <button\n                      className={`py-4 px-6 font-medium text-sm border-b-2 ${\n                        activeTab === 'reviews'\n                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`\n                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                      }`}\n                      onClick={() => setActiveTab('reviews')}\n                    >\n                      Reviews\n                    </button>\n                    <button\n                      className={`py-4 px-6 font-medium text-sm border-b-2 ${\n                        activeTab === 'procedures'\n                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`\n                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                      }`}\n                      onClick={() => setActiveTab('procedures')}\n                    >\n                      Procedures\n                    </button>\n                    <button\n                      className={`py-4 px-6 font-medium text-sm border-b-2 ${\n                        activeTab === 'patients'\n                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`\n                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                      }`}\n                      onClick={() => setActiveTab('patients')}\n                    >\n                      Patients\n                    </button>\n                  </nav>\n                </div>\n\n                {/* Tab Content */}\n                <div className=\"p-6\">\n                  {/* Overview Tab */}\n                  {activeTab === 'overview' && (\n                    <div>\n                      <div className=\"flex justify-between items-center mb-6\">\n                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>\n                          <FaChartLine className=\"h-5 w-5 mr-2\" />\n                          University Overview\n                        </h2>\n                      </div>\n                      <div className=\"bg-blue-50 p-6 rounded-lg\">\n                        <div className=\"h-96\">\n                          <Bar data={overviewChartData} options={barChartOptions} />\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Appointments Tab */}\n                  {activeTab === 'appointments' && (\n                    <div>\n                      <div className=\"flex justify-between items-center mb-6\">\n                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>\n                          <FaCalendarAlt className=\"h-5 w-5 mr-2\" />\n                          Appointment Analytics\n                        </h2>\n                      </div>\n\n                      {/* Appointment Stats */}\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6\">\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Total</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{appointmentsAnalytics.total}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Pending</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{appointmentsAnalytics.pending}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Completed</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{appointmentsAnalytics.completed}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Cancelled</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{appointmentsAnalytics.cancelled}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Today</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{appointmentsAnalytics.today}</p>\n                        </div>\n                      </div>\n\n                      {/* Appointment Charts */}\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Appointment Status\n                          </h3>\n                          <div className=\"h-64\">\n                            <Doughnut data={appointmentsStatusChartData} options={pieChartOptions} />\n                          </div>\n                        </div>\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Appointment Types\n                          </h3>\n                          <div className=\"h-64\">\n                            <Pie data={appointmentsByTypeChartData} options={pieChartOptions} />\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartArea className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Appointments by Month\n                          </h3>\n                          <div className=\"h-64\">\n                            <Line data={appointmentsByMonthChartData} options={lineChartOptions} />\n                          </div>\n                        </div>\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Appointments by Day\n                          </h3>\n                          <div className=\"h-64\">\n                            <Bar data={appointmentsByDayChartData} options={barChartOptions} />\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Reviews Tab */}\n                  {activeTab === 'reviews' && (\n                    <div>\n                      <div className=\"flex justify-between items-center mb-6\">\n                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>\n                          <FaStar className=\"h-5 w-5 mr-2\" />\n                          Review Analytics\n                        </h2>\n                      </div>\n\n                      {/* Review Stats */}\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6\">\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Total</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{reviewsAnalytics.total}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Pending</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{reviewsAnalytics.pending}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Accepted</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{reviewsAnalytics.accepted}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Declined</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{reviewsAnalytics.declined}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Avg. Quality</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{reviewsAnalytics.avgProcedureQuality}/5</p>\n                        </div>\n                      </div>\n\n                      {/* Review Charts */}\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Review Status\n                          </h3>\n                          <div className=\"h-64\">\n                            <Doughnut data={reviewsStatusChartData} options={pieChartOptions} />\n                          </div>\n                        </div>\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Procedure Types\n                          </h3>\n                          <div className=\"h-64\">\n                            <Pie data={reviewsByProcedureTypeChartData} options={pieChartOptions} />\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-6\">\n                        <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                          <FaChartArea className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                          Reviews by Month\n                        </h3>\n                        <div className=\"h-64\">\n                          <Line data={reviewsByMonthChartData} options={lineChartOptions} />\n                        </div>\n                      </div>\n\n                      {/* Top Students Table */}\n                      <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                        <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                          <FaUserGraduate className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                          Student Performance\n                        </h3>\n                        <div className=\"overflow-x-auto\">\n                          <table className=\"min-w-full divide-y divide-gray-200\">\n                            <thead className=\"bg-gray-50\">\n                              <tr>\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                  Student\n                                </th>\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                  Total Reviews\n                                </th>\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                  Accepted\n                                </th>\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                  Declined\n                                </th>\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                  Pending\n                                </th>\n                              </tr>\n                            </thead>\n                            <tbody className=\"bg-white divide-y divide-gray-200\">\n                              {Object.entries(reviewsAnalytics.byStudent)\n                                .sort((a, b) => b[1].total - a[1].total)\n                                .slice(0, 5)\n                                .map(([student, stats]) => (\n                                  <tr key={student}>\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                                      {student}\n                                    </td>\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                      {stats.total}\n                                    </td>\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                      <span className=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800\">\n                                        {stats.accepted}\n                                      </span>\n                                    </td>\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                      <span className=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800\">\n                                        {stats.declined}\n                                      </span>\n                                    </td>\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                      <span className=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800\">\n                                        {stats.pending}\n                                      </span>\n                                    </td>\n                                  </tr>\n                                ))}\n                            </tbody>\n                          </table>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Procedures Tab */}\n                  {activeTab === 'procedures' && (\n                    <div>\n                      <div className=\"flex justify-between items-center mb-6\">\n                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>\n                          <FaChartPie className=\"h-5 w-5 mr-2\" />\n                          Procedures Analytics\n                        </h2>\n                      </div>\n\n                      {/* Procedures Stats */}\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6\">\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Total</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{proceduresAnalytics.total}</p>\n                        </div>\n                        <div className=\"bg-blue-50 p-4 rounded-lg\">\n                          <p className=\"text-sm font-medium text-gray-500\">Recent Sheets</p>\n                          <p className=\"text-2xl font-bold text-blue-900\">{proceduresAnalytics.recentSheets.length}</p>\n                        </div>\n                      </div>\n\n                      {/* Procedures Charts */}\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Procedures by Type\n                          </h3>\n                          <div className=\"h-64\">\n                            <Pie data={proceduresByTypeChartData} options={pieChartOptions} />\n                          </div>\n                        </div>\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartArea className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Procedures by Month\n                          </h3>\n                          <div className=\"h-64\">\n                            <Line data={proceduresByMonthChartData} options={lineChartOptions} />\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Top Students by Procedures\n                          </h3>\n                          <div className=\"h-64\">\n                            <Bar data={proceduresByStudentChartData} options={barChartOptions} />\n                          </div>\n                        </div>\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Top Patients by Procedures\n                          </h3>\n                          <div className=\"h-64\">\n                            <Bar data={proceduresByPatientChartData} options={barChartOptions} />\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaFileMedical className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Recent Procedures\n                          </h3>\n                          <div className=\"h-64 overflow-y-auto\">\n                            <div className=\"space-y-2\">\n                              {proceduresAnalytics.recentSheets.map((sheet, index) => (\n                                <div key={index} className=\"p-3 bg-gray-50 rounded-lg\">\n                                  <div className=\"flex justify-between items-start\">\n                                    <div>\n                                      <p className=\"text-sm font-medium text-gray-900\">{sheet.type}</p>\n                                      <p className=\"text-xs text-gray-500\">Patient: {sheet.patientName}</p>\n                                      <p className=\"text-xs text-gray-500\">Student: {sheet.studentName}</p>\n                                    </div>\n                                    <span className=\"text-xs text-gray-400\">\n                                      {new Date(sheet.createdAt).toLocaleDateString()}\n                                    </span>\n                                  </div>\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Procedures Summary\n                          </h3>\n                          <div className=\"h-64 flex flex-col justify-center\">\n                            <div className=\"grid grid-cols-2 gap-4\">\n                              <div className=\"text-center\">\n                                <p className=\"text-2xl font-bold text-blue-900\">{proceduresAnalytics.total}</p>\n                                <p className=\"text-sm text-gray-500\">Total Procedures</p>\n                              </div>\n                              <div className=\"text-center\">\n                                <p className=\"text-2xl font-bold text-green-900\">{Object.keys(proceduresAnalytics.byType).length}</p>\n                                <p className=\"text-sm text-gray-500\">Procedure Types</p>\n                              </div>\n                              <div className=\"text-center\">\n                                <p className=\"text-2xl font-bold text-purple-900\">{Object.keys(proceduresAnalytics.byStudent).length}</p>\n                                <p className=\"text-sm text-gray-500\">Active Students</p>\n                              </div>\n                              <div className=\"text-center\">\n                                <p className=\"text-2xl font-bold text-orange-900\">{Object.keys(proceduresAnalytics.byPatient).length}</p>\n                                <p className=\"text-sm text-gray-500\">Patients Treated</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Patients Tab */}\n                  {activeTab === 'patients' && (\n                    <div>\n                      <div className=\"flex justify-between items-center mb-6\">\n                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>\n                          <FaUserAlt className=\"h-5 w-5 mr-2\" />\n                          Patient Analytics\n                        </h2>\n                      </div>\n\n                      {/* Patient Charts */}\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Gender Distribution\n                          </h3>\n                          <div className=\"h-64\">\n                            <Pie data={patientsByGenderChartData} options={pieChartOptions} />\n                          </div>\n                        </div>\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                          <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                            <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                            Age Distribution\n                          </h3>\n                          <div className=\"h-64\">\n                            <Bar data={patientsByAgeChartData} options={barChartOptions} />\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Chronic Diseases */}\n                      <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\n                        <h3 className=\"text-lg font-medium text-gray-700 mb-3 flex items-center\">\n                          <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\n                          Top Chronic Diseases\n                        </h3>\n                        <div className=\"h-64\">\n                          <Bar data={patientsByChronicDiseaseChartData} options={barChartOptions} />\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </motion.div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Analytics;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,WAAW,EACXC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,cAAc,EACdC,WAAW,EACXC,QAAQ,EACRC,gBAAgB,EAChBC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,aAAa,QACR,gBAAgB;AACvB,SAASC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,iBAAiB;AAC1D,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,MAAM,QACD,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAEDlB,OAAO,CAACmB,QAAQ,CACdlB,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,MACF,CAAC;;AAED;AACA,MAAMU,YAAY,GAAG;EACnBN,OAAO,EAAE;IAAEO,IAAI,EAAE,GAAGR,mBAAmB,CAACC,OAAO,IAAI;IAAEQ,MAAM,EAAET,mBAAmB,CAACC;EAAQ,CAAC;EAC1FC,SAAS,EAAE;IAAEM,IAAI,EAAE,GAAGR,mBAAmB,CAACE,SAAS,IAAI;IAAEO,MAAM,EAAET,mBAAmB,CAACE;EAAU,CAAC;EAChGG,MAAM,EAAE;IAAEG,IAAI,EAAE,GAAGR,mBAAmB,CAACK,MAAM,IAAI;IAAEI,MAAM,EAAET,mBAAmB,CAACK;EAAO,CAAC;EACvFK,GAAG,EAAE;IAAEF,IAAI,EAAE,wBAAwB;IAAEC,MAAM,EAAE;EAAuB,CAAC;EACvEE,MAAM,EAAE;IAAEH,IAAI,EAAE,yBAAyB;IAAEC,MAAM,EAAE;EAAwB,CAAC;EAC5EG,KAAK,EAAE;IAAEJ,IAAI,EAAE,yBAAyB;IAAEC,MAAM,EAAE;EAAwB;AAC5E,CAAC;AAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyE,OAAO,EAAEC,UAAU,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2E,KAAK,EAAEC,QAAQ,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM6E,QAAQ,GAAG3E,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4E,IAAI;IAAEC;EAAM,CAAC,GAAG3E,OAAO,CAAC,CAAC;EAEjCH,SAAS,CAAC,MAAM;IACd,MAAM+E,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBH,QAAQ,CAAC,kCAAkC,CAAC;QAC5CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI,CAACI,IAAI,CAACG,UAAU,EAAE;QACpBL,QAAQ,CAAC,0DAA0D,CAAC;QACpEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACF,MAAMQ,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUL,KAAK;UAAG;QAAE,CAAC;;QAEhE;QACA,MAAMM,eAAe,GAAG,CACtBlF,KAAK,CAACmF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,sCAAsCC,kBAAkB,CAACZ,IAAI,CAACG,UAAU,CAAC,EAAE,EAAEC,MAAM,CAAC,EAC9H/E,KAAK,CAACmF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,kCAAkCC,kBAAkB,CAACZ,IAAI,CAACG,UAAU,CAAC,EAAE,EAAEC,MAAM,CAAC,EAC1H/E,KAAK,CAACmF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,kCAAkCC,kBAAkB,CAACZ,IAAI,CAACG,UAAU,CAAC,EAAE,EAAEC,MAAM,CAAC,EAC1H/E,KAAK,CAACmF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,qCAAqCC,kBAAkB,CAACZ,IAAI,CAACG,UAAU,CAAC,EAAE,EAAEC,MAAM,CAAC,EAC7H/E,KAAK,CAACmF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,oCAAoCC,kBAAkB,CAACZ,IAAI,CAACG,UAAU,CAAC,EAAE,EAAEC,MAAM,CAAC,EAC5H/E,KAAK,CAACmF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,6BAA6B,EAAEP,MAAM,CAAC,CACjF;;QAED;QACA,MAAM,CAACS,eAAe,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,aAAa,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CACtHb,eAAe,CAACc,GAAG,CAACC,OAAO,IACzBA,OAAO,CAACC,KAAK,CAAC1B,KAAK,IAAI;UACrB2B,OAAO,CAAC3B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC4B,OAAO,CAAC;UACnD,OAAO;YAAEC,IAAI,EAAE;UAAG,CAAC,CAAC,CAAC;QACvB,CAAC,CACH,CACF,CAAC;QAED,MAAMvC,QAAQ,GAAG4B,WAAW,CAACW,IAAI,IAAI,EAAE;QACvCF,OAAO,CAACG,GAAG,CAAC,SAASxC,QAAQ,CAACyC,MAAM,2BAA2B5B,IAAI,CAACG,UAAU,EAAE,CAAC;;QAEjF;QACA,IAAI0B,UAAU,GAAG,EAAE;QAEnB,IAAI1C,QAAQ,CAACyC,MAAM,GAAG,CAAC,EAAE;UACvBJ,OAAO,CAACG,GAAG,CAAC,kCAAkC,CAAC;;UAE/C;UACA,MAAMG,cAAc,GAAG3C,QAAQ,CAACkC,GAAG,CAACU,OAAO,IAAI;YAC7C,MAAMC,GAAG,GAAG,GAAGvB,OAAO,CAACC,GAAG,CAACC,iBAAiB,kCAAkCoB,OAAO,CAACE,SAAS,EAAE;YACjGT,OAAO,CAACG,GAAG,CAAC,gCAAgCI,OAAO,CAACG,IAAI,cAAcF,GAAG,EAAE,CAAC;YAE5E,OAAO3G,KAAK,CAACmF,GAAG,CAACwB,GAAG,EAAE5B,MAAM,CAAC,CAC1B+B,IAAI,CAACC,QAAQ,IAAI;cAChBZ,OAAO,CAACG,GAAG,CAAC,wBAAwBS,QAAQ,CAACV,IAAI,CAACE,MAAM,wBAAwBG,OAAO,CAACG,IAAI,EAAE,CAAC;cAC/F,OAAOE,QAAQ;YACjB,CAAC,CAAC,CACDb,KAAK,CAAC1B,KAAK,IAAI;cACd2B,OAAO,CAAC3B,KAAK,CAAC,sCAAsCkC,OAAO,CAACG,IAAI,GAAG,EAAErC,KAAK,CAAC4B,OAAO,CAAC;cACnF;cACA,OAAOpG,KAAK,CAACmF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,kCAAkCoB,OAAO,CAACM,GAAG,EAAE,EAAEjC,MAAM,CAAC,CACtG+B,IAAI,CAACC,QAAQ,IAAI;gBAChBZ,OAAO,CAACG,GAAG,CAAC,gCAAgCS,QAAQ,CAACV,IAAI,CAACE,MAAM,wBAAwBG,OAAO,CAACG,IAAI,EAAE,CAAC;gBACvG,OAAOE,QAAQ;cACjB,CAAC,CAAC,CACDb,KAAK,CAACe,aAAa,IAAI;gBACtBd,OAAO,CAAC3B,KAAK,CAAC,oCAAoCkC,OAAO,CAACG,IAAI,GAAG,EAAEI,aAAa,CAACb,OAAO,CAAC;gBACzF,OAAO;kBAAEC,IAAI,EAAE;gBAAG,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC;;UAEF;UACA,MAAMa,cAAc,GAAG,MAAMpB,OAAO,CAACC,GAAG,CAACU,cAAc,CAAC;;UAExD;UACAS,cAAc,CAACC,OAAO,CAACC,MAAM,IAAI;YAC/B,IAAIA,MAAM,CAACf,IAAI,IAAIgB,KAAK,CAACC,OAAO,CAACF,MAAM,CAACf,IAAI,CAAC,EAAE;cAC7C;cACA,MAAMkB,eAAe,GAAGH,MAAM,CAACf,IAAI,CAACL,GAAG,CAACwB,MAAM,IAAI;gBAChD;gBACA,MAAM;kBAAEC,SAAS;kBAAE,GAAGC;gBAAuB,CAAC,GAAGF,MAAM;gBACvD,OAAOE,sBAAsB;cAC/B,CAAC,CAAC;cAEFlB,UAAU,GAAG,CAAC,GAAGA,UAAU,EAAE,GAAGe,eAAe,CAAC;YAClD;UACF,CAAC,CAAC;;UAEF;UACAf,UAAU,GAAGa,KAAK,CAACM,IAAI,CAAC,IAAIC,GAAG,CAACpB,UAAU,CAACR,GAAG,CAACwB,MAAM,IAAI,CAACA,MAAM,CAACR,GAAG,EAAEQ,MAAM,CAAC,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC;UAEzF1B,OAAO,CAACG,GAAG,CAAC,SAASE,UAAU,CAACD,MAAM,6CAA6C5B,IAAI,CAACG,UAAU,EAAE,CAAC;QACvG;QAEArB,eAAe,CAAC+B,eAAe,CAACa,IAAI,IAAI,EAAE,CAAC;QAC3C1C,UAAU,CAAC6C,UAAU,CAAC;QACtB3C,WAAW,CAAC4B,WAAW,CAACY,IAAI,IAAI,EAAE,CAAC;QACnCtC,WAAW,CAACD,QAAQ,CAAC;QACrBG,cAAc,CAAC0B,cAAc,CAACU,IAAI,IAAI,EAAE,CAAC;QACzClC,aAAa,CAACyB,aAAa,CAACS,IAAI,IAAI,EAAE,CAAC;QACvChC,kBAAkB,CAACwB,kBAAkB,CAACQ,IAAI,IAAI,EAAE,CAAC;QAEjD,IACEb,eAAe,CAACa,IAAI,CAACE,MAAM,KAAK,CAAC,IACjCC,UAAU,CAACD,MAAM,KAAK,CAAC,IACvBd,WAAW,CAACY,IAAI,CAACE,MAAM,KAAK,CAAC,IAC7BV,kBAAkB,CAACQ,IAAI,CAACE,MAAM,KAAK,CAAC,EACpC;UACA9B,QAAQ,CAAC,8BAA8B,CAAC;QAC1C;MACF,CAAC,CAAC,OAAOqD,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;QACZjC,OAAO,CAAC3B,KAAK,CAAC,cAAc,EAAE,EAAAuD,aAAA,GAAAD,GAAG,CAACf,QAAQ,cAAAgB,aAAA,uBAAZA,aAAA,CAAc1B,IAAI,KAAIyB,GAAG,CAAC1B,OAAO,CAAC;QAChE,MAAMiC,YAAY,GAChB,EAAAL,cAAA,GAAAF,GAAG,CAACf,QAAQ,cAAAiB,cAAA,uBAAZA,cAAA,CAAcM,MAAM,MAAK,GAAG,GACxB,gCAAgC,GAChC,EAAAL,cAAA,GAAAH,GAAG,CAACf,QAAQ,cAAAkB,cAAA,uBAAZA,cAAA,CAAcK,MAAM,MAAK,GAAG,GAC5B,oCAAoC,GACpC,EAAAJ,cAAA,GAAAJ,GAAG,CAACf,QAAQ,cAAAmB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7B,IAAI,cAAA8B,mBAAA,uBAAlBA,mBAAA,CAAoB/B,OAAO,KAAI,0BAA0B;QAC/D3B,QAAQ,CAAC4D,YAAY,CAAC;QACtB,IAAI,EAAAD,cAAA,GAAAN,GAAG,CAACf,QAAQ,cAAAqB,cAAA,uBAAZA,cAAA,CAAcE,MAAM,MAAK,GAAG,EAAE5D,QAAQ,CAAC,QAAQ,CAAC;MACtD,CAAC,SAAS;QACRH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDM,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACF,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;;EAE3B;EACA,MAAM6D,qBAAqB,GAAG;IAC5BC,KAAK,EAAEhF,YAAY,CAAC+C,MAAM;IAC1BkC,OAAO,EAAEjF,YAAY,CAACkF,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACL,MAAM,KAAK,SAAS,CAAC,CAAC/B,MAAM;IAClEqC,SAAS,EAAEpF,YAAY,CAACkF,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACL,MAAM,KAAK,WAAW,CAAC,CAAC/B,MAAM;IACtEsC,SAAS,EAAErF,YAAY,CAACkF,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACL,MAAM,KAAK,WAAW,CAAC,CAAC/B,MAAM;IACtEuC,KAAK,EAAEtF,YAAY,CAACkF,MAAM,CAAEC,CAAC,IAAK;MAChC,MAAMI,QAAQ,GAAG,IAAIC,IAAI,CAACL,CAAC,CAACM,IAAI,CAAC;MACjC,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAC5B,OAAOD,QAAQ,CAACI,YAAY,CAAC,CAAC,KAAKD,SAAS,CAACC,YAAY,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC5C,MAAM;IACT6C,QAAQ,EAAE5F,YAAY,CAACkF,MAAM,CAAEC,CAAC,IAAK;MACnC,MAAMI,QAAQ,GAAG,IAAIC,IAAI,CAACL,CAAC,CAACM,IAAI,CAAC;MACjC,MAAMH,KAAK,GAAG,IAAIE,IAAI,CAAC,CAAC;MACxB,MAAMK,WAAW,GAAG,IAAIL,IAAI,CAACF,KAAK,CAAC;MACnCO,WAAW,CAACC,OAAO,CAACR,KAAK,CAACS,OAAO,CAAC,CAAC,GAAGT,KAAK,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,MAAMC,SAAS,GAAG,IAAIT,IAAI,CAACK,WAAW,CAAC;MACvCI,SAAS,CAACH,OAAO,CAACD,WAAW,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC9C,OAAOR,QAAQ,IAAIM,WAAW,IAAIN,QAAQ,IAAIU,SAAS;IACzD,CAAC,CAAC,CAAClD,MAAM;IACTmD,OAAO,EAAEC,sBAAsB,CAACnG,YAAY,CAAC;IAC7CoG,KAAK,EAAEC,oBAAoB,CAACrG,YAAY,CAAC;IACzCsG,MAAM,EAAEC,qBAAqB,CAACvG,YAAY;EAC5C,CAAC;;EAED;EACA,MAAM+D,eAAe,GAAG7D,OAAO,CAACgF,MAAM,CAAClB,MAAM,IAC3C,EAAEA,MAAM,CAACwC,SAAS,IAAIxC,MAAM,CAACwC,SAAS,CAACC,UAAU,KAAK,mBAAmB,CAC3E,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvB1B,KAAK,EAAEjB,eAAe,CAAChB,MAAM;IAC7BkC,OAAO,EAAElB,eAAe,CAACmB,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAAC7B,MAAM,KAAK,SAAS,CAAC,CAAC/B,MAAM;IACnE6D,QAAQ,EAAE7C,eAAe,CAACmB,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAAC7B,MAAM,KAAK,UAAU,CAAC,CAAC/B,MAAM;IACrE8D,QAAQ,EAAE9C,eAAe,CAACmB,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAAC7B,MAAM,KAAK,UAAU,IAAI6B,CAAC,CAAC7B,MAAM,KAAK,QAAQ,CAAC,CAAC/B,MAAM;IAC9F+D,mBAAmB,EAAEC,sBAAsB,CAAChD,eAAe,EAAE,kBAAkB,CAAC;IAChFiD,qBAAqB,EAAED,sBAAsB,CAAChD,eAAe,EAAE,oBAAoB,CAAC;IACpFkD,eAAe,EAAEC,yBAAyB,CAACnD,eAAe,CAAC;IAC3DmC,OAAO,EAAEiB,iBAAiB,CAACpD,eAAe,CAAC;IAC3CqD,SAAS,EAAEC,mBAAmB,CAACtD,eAAe;EAChD,CAAC;EAEDpB,OAAO,CAACG,GAAG,CAAC,oBAAoB,EAAE4D,gBAAgB,CAAC;;EAEnD;EACA,MAAMY,iBAAiB,GAAG;IACxBtC,KAAK,EAAE5E,QAAQ,CAAC2C,MAAM;IACtBwE,KAAK,EAAEC,0BAA0B,CAACpH,QAAQ,CAAC;IAC3CqH,QAAQ,EAAEC,6BAA6B,CAACtH,QAAQ,CAAC;IACjDuH,gBAAgB,EAAEC,qCAAqC,CAACxH,QAAQ;EAClE,CAAC;;EAED;EACA,MAAMyH,eAAe,GAAG;IACtBvH,QAAQ,EAAEA,QAAQ,CAACyC,MAAM;IACzBvC,WAAW,EAAEA,WAAW,CAACuC,MAAM;IAC/BrC,UAAU,EAAEA,UAAU,CAACqC,MAAM;IAC7B3C,QAAQ,EAAEA,QAAQ,CAAC2C;EACrB,CAAC;;EAED;EACA,MAAM+E,mBAAmB,GAAG;IAC1B9C,KAAK,EAAEpE,eAAe,CAACmC,MAAM;IAC7BuD,MAAM,EAAEyB,mBAAmB,CAACnH,eAAe,CAAC;IAC5CsF,OAAO,EAAE8B,oBAAoB,CAACpH,eAAe,CAAC;IAC9CwG,SAAS,EAAEa,sBAAsB,CAACrH,eAAe,CAAC;IAClDsH,SAAS,EAAEC,sBAAsB,CAACvH,eAAe,CAAC;IAClDwH,YAAY,EAAExH,eAAe,CAC1ByH,IAAI,CAAC,CAAClD,CAAC,EAAEmD,CAAC,KAAK,IAAI9C,IAAI,CAAC8C,CAAC,CAACC,SAAS,CAAC,GAAG,IAAI/C,IAAI,CAACL,CAAC,CAACoD,SAAS,CAAC,CAAC,CAC7DC,KAAK,CAAC,CAAC,EAAE,EAAE;EAChB,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG;IACxBC,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,YAAY,CAAC;IACtGC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,QAAQ;MACf/F,IAAI,EAAE,CACJgF,eAAe,CAACzH,QAAQ,EACxByH,eAAe,CAACvH,QAAQ,EACxBuH,eAAe,CAACrH,WAAW,EAC3BqH,eAAe,CAACnH,UAAU,EAC1BqE,qBAAqB,CAACC,KAAK,EAC3B0B,gBAAgB,CAAC1B,KAAK,EACtB8C,mBAAmB,CAAC9C,KAAK,CAC1B;MACD6D,eAAe,EAAE,CACfzJ,YAAY,CAACN,OAAO,CAACO,IAAI,EACzBD,YAAY,CAACL,SAAS,CAACM,IAAI,EAC3BD,YAAY,CAACF,MAAM,CAACG,IAAI,EACxBD,YAAY,CAACN,OAAO,CAACO,IAAI,EACzBD,YAAY,CAACL,SAAS,CAACM,IAAI,EAC3BD,YAAY,CAACF,MAAM,CAACG,IAAI,EACxBD,YAAY,CAACI,MAAM,CAACH,IAAI,CACzB;MACDyJ,WAAW,EAAE,CACX1J,YAAY,CAACN,OAAO,CAACQ,MAAM,EAC3BF,YAAY,CAACL,SAAS,CAACO,MAAM,EAC7BF,YAAY,CAACF,MAAM,CAACI,MAAM,EAC1BF,YAAY,CAACN,OAAO,CAACQ,MAAM,EAC3BF,YAAY,CAACL,SAAS,CAACO,MAAM,EAC7BF,YAAY,CAACF,MAAM,CAACI,MAAM,EAC1BF,YAAY,CAACI,MAAM,CAACF,MAAM,CAC3B;MACDyJ,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,2BAA2B,GAAG;IAClCR,MAAM,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC;IAC7CC,QAAQ,EAAE,CACR;MACE9F,IAAI,EAAE,CACJkC,qBAAqB,CAACE,OAAO,EAC7BF,qBAAqB,CAACK,SAAS,EAC/BL,qBAAqB,CAACM,SAAS,CAChC;MACDwD,eAAe,EAAE,CACfzJ,YAAY,CAACK,KAAK,CAACJ,IAAI,EACvBD,YAAY,CAACF,MAAM,CAACG,IAAI,EACxBD,YAAY,CAACG,GAAG,CAACF,IAAI,CACtB;MACDyJ,WAAW,EAAE,CACX1J,YAAY,CAACK,KAAK,CAACH,MAAM,EACzBF,YAAY,CAACF,MAAM,CAACI,MAAM,EAC1BF,YAAY,CAACG,GAAG,CAACD,MAAM,CACxB;MACDyJ,WAAW,EAAE,CAAC;MACdE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAME,4BAA4B,GAAG;IACnCT,MAAM,EAAE3D,qBAAqB,CAACmB,OAAO,CAAC1D,GAAG,CAAC4G,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;IAC7DV,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,cAAc;MACrB/F,IAAI,EAAEkC,qBAAqB,CAACmB,OAAO,CAAC1D,GAAG,CAAC4G,IAAI,IAAIA,IAAI,CAACE,KAAK,CAAC;MAC3DT,eAAe,EAAEzJ,YAAY,CAACN,OAAO,CAACO,IAAI;MAC1CyJ,WAAW,EAAE1J,YAAY,CAACN,OAAO,CAACQ,MAAM;MACxCyJ,WAAW,EAAE,CAAC;MACdQ,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,0BAA0B,GAAG;IACjCf,MAAM,EAAE3D,qBAAqB,CAACqB,KAAK,CAAC5D,GAAG,CAAC4G,IAAI,IAAIA,IAAI,CAACM,GAAG,CAAC;IACzDf,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,cAAc;MACrB/F,IAAI,EAAEkC,qBAAqB,CAACqB,KAAK,CAAC5D,GAAG,CAAC4G,IAAI,IAAIA,IAAI,CAACE,KAAK,CAAC;MACzDT,eAAe,EAAEzJ,YAAY,CAACL,SAAS,CAACM,IAAI;MAC5CyJ,WAAW,EAAE1J,YAAY,CAACL,SAAS,CAACO,MAAM;MAC1CyJ,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB,CAAC;EAEL,CAAC;;EAED;EACA,MAAMW,2BAA2B,GAAG;IAClCjB,MAAM,EAAEkB,MAAM,CAACC,IAAI,CAAC9E,qBAAqB,CAACuB,MAAM,CAAC;IACjDqC,QAAQ,EAAE,CACR;MACE9F,IAAI,EAAE+G,MAAM,CAACvF,MAAM,CAACU,qBAAqB,CAACuB,MAAM,CAAC;MACjDuC,eAAe,EAAEe,MAAM,CAACC,IAAI,CAAC9E,qBAAqB,CAACuB,MAAM,CAAC,CAAC9D,GAAG,CAAC,CAACsH,CAAC,EAAEC,KAAK,KAAK;QAC3E,MAAMC,SAAS,GAAGJ,MAAM,CAACC,IAAI,CAACzK,YAAY,CAAC;QAC3C,OAAOA,YAAY,CAAC4K,SAAS,CAACD,KAAK,GAAGC,SAAS,CAACjH,MAAM,CAAC,CAAC,CAAC1D,IAAI;MAC/D,CAAC,CAAC;MACFyJ,WAAW,EAAEc,MAAM,CAACC,IAAI,CAAC9E,qBAAqB,CAACuB,MAAM,CAAC,CAAC9D,GAAG,CAAC,CAACsH,CAAC,EAAEC,KAAK,KAAK;QACvE,MAAMC,SAAS,GAAGJ,MAAM,CAACC,IAAI,CAACzK,YAAY,CAAC;QAC3C,OAAOA,YAAY,CAAC4K,SAAS,CAACD,KAAK,GAAGC,SAAS,CAACjH,MAAM,CAAC,CAAC,CAACzD,MAAM;MACjE,CAAC,CAAC;MACFyJ,WAAW,EAAE,CAAC;MACdE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMgB,sBAAsB,GAAG;IAC7BvB,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3CC,QAAQ,EAAE,CACR;MACE9F,IAAI,EAAE,CACJ6D,gBAAgB,CAACzB,OAAO,EACxByB,gBAAgB,CAACE,QAAQ,EACzBF,gBAAgB,CAACG,QAAQ,CAC1B;MACDgC,eAAe,EAAE,CACfzJ,YAAY,CAACK,KAAK,CAACJ,IAAI,EACvBD,YAAY,CAACF,MAAM,CAACG,IAAI,EACxBD,YAAY,CAACG,GAAG,CAACF,IAAI,CACtB;MACDyJ,WAAW,EAAE,CACX1J,YAAY,CAACK,KAAK,CAACH,MAAM,EACzBF,YAAY,CAACF,MAAM,CAACI,MAAM,EAC1BF,YAAY,CAACG,GAAG,CAACD,MAAM,CACxB;MACDyJ,WAAW,EAAE,CAAC;MACdE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMiB,+BAA+B,GAAG;IACtCxB,MAAM,EAAEkB,MAAM,CAACC,IAAI,CAACnD,gBAAgB,CAACO,eAAe,CAAC;IACrD0B,QAAQ,EAAE,CACR;MACE9F,IAAI,EAAE+G,MAAM,CAACvF,MAAM,CAACqC,gBAAgB,CAACO,eAAe,CAAC;MACrD4B,eAAe,EAAEe,MAAM,CAACC,IAAI,CAACnD,gBAAgB,CAACO,eAAe,CAAC,CAACzE,GAAG,CAAC,CAACsH,CAAC,EAAEC,KAAK,KAAK;QAC/E,MAAMC,SAAS,GAAGJ,MAAM,CAACC,IAAI,CAACzK,YAAY,CAAC;QAC3C,OAAOA,YAAY,CAAC4K,SAAS,CAACD,KAAK,GAAGC,SAAS,CAACjH,MAAM,CAAC,CAAC,CAAC1D,IAAI;MAC/D,CAAC,CAAC;MACFyJ,WAAW,EAAEc,MAAM,CAACC,IAAI,CAACnD,gBAAgB,CAACO,eAAe,CAAC,CAACzE,GAAG,CAAC,CAACsH,CAAC,EAAEC,KAAK,KAAK;QAC3E,MAAMC,SAAS,GAAGJ,MAAM,CAACC,IAAI,CAACzK,YAAY,CAAC;QAC3C,OAAOA,YAAY,CAAC4K,SAAS,CAACD,KAAK,GAAGC,SAAS,CAACjH,MAAM,CAAC,CAAC,CAACzD,MAAM;MACjE,CAAC,CAAC;MACFyJ,WAAW,EAAE,CAAC;MACdE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMkB,uBAAuB,GAAG;IAC9BzB,MAAM,EAAEhC,gBAAgB,CAACR,OAAO,CAAC1D,GAAG,CAAC4G,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;IACxDV,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,SAAS;MAChB/F,IAAI,EAAE6D,gBAAgB,CAACR,OAAO,CAAC1D,GAAG,CAAC4G,IAAI,IAAIA,IAAI,CAACE,KAAK,CAAC;MACtDT,eAAe,EAAEzJ,YAAY,CAACL,SAAS,CAACM,IAAI;MAC5CyJ,WAAW,EAAE1J,YAAY,CAACL,SAAS,CAACO,MAAM;MAC1CyJ,WAAW,EAAE,CAAC;MACdQ,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;;EAED;EACA,MAAMY,sBAAsB,GAAG;IAC7B1B,MAAM,EAAEkB,MAAM,CAACC,IAAI,CAACvC,iBAAiB,CAACC,KAAK,CAAC;IAC5CoB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,UAAU;MACjB/F,IAAI,EAAE+G,MAAM,CAACvF,MAAM,CAACiD,iBAAiB,CAACC,KAAK,CAAC;MAC5CsB,eAAe,EAAEzJ,YAAY,CAACF,MAAM,CAACG,IAAI;MACzCyJ,WAAW,EAAE1J,YAAY,CAACF,MAAM,CAACI,MAAM;MACvCyJ,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB,CAAC;EAEL,CAAC;;EAED;EACA,MAAMqB,yBAAyB,GAAG;IAChC3B,MAAM,EAAEkB,MAAM,CAACC,IAAI,CAACvC,iBAAiB,CAACG,QAAQ,CAAC;IAC/CkB,QAAQ,EAAE,CACR;MACE9F,IAAI,EAAE+G,MAAM,CAACvF,MAAM,CAACiD,iBAAiB,CAACG,QAAQ,CAAC;MAC/CoB,eAAe,EAAE,CACfzJ,YAAY,CAACN,OAAO,CAACO,IAAI,EACzBD,YAAY,CAACL,SAAS,CAACM,IAAI,EAC3BD,YAAY,CAACF,MAAM,CAACG,IAAI,CACzB;MACDyJ,WAAW,EAAE,CACX1J,YAAY,CAACN,OAAO,CAACQ,MAAM,EAC3BF,YAAY,CAACL,SAAS,CAACO,MAAM,EAC7BF,YAAY,CAACF,MAAM,CAACI,MAAM,CAC3B;MACDyJ,WAAW,EAAE,CAAC;MACdE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMqB,iCAAiC,GAAG;IACxC5B,MAAM,EAAEkB,MAAM,CAACC,IAAI,CAACvC,iBAAiB,CAACK,gBAAgB,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAAE;IACrEG,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,UAAU;MACjB/F,IAAI,EAAE+G,MAAM,CAACvF,MAAM,CAACiD,iBAAiB,CAACK,gBAAgB,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAAE;MACrEK,eAAe,EAAEe,MAAM,CAACC,IAAI,CAACvC,iBAAiB,CAACK,gBAAgB,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChG,GAAG,CAAC,CAACsH,CAAC,EAAEC,KAAK,KAAK;QAC7F,MAAMC,SAAS,GAAGJ,MAAM,CAACC,IAAI,CAACzK,YAAY,CAAC;QAC3C,OAAOA,YAAY,CAAC4K,SAAS,CAACD,KAAK,GAAGC,SAAS,CAACjH,MAAM,CAAC,CAAC,CAAC1D,IAAI;MAC/D,CAAC,CAAC;MACFyJ,WAAW,EAAEc,MAAM,CAACC,IAAI,CAACvC,iBAAiB,CAACK,gBAAgB,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChG,GAAG,CAAC,CAACsH,CAAC,EAAEC,KAAK,KAAK;QACzF,MAAMC,SAAS,GAAGJ,MAAM,CAACC,IAAI,CAACzK,YAAY,CAAC;QAC3C,OAAOA,YAAY,CAAC4K,SAAS,CAACD,KAAK,GAAGC,SAAS,CAACjH,MAAM,CAAC,CAAC,CAACzD,MAAM;MACjE,CAAC,CAAC;MACFyJ,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB,CAAC;EAEL,CAAC;;EAED;EACA,MAAMuB,yBAAyB,GAAG;IAChC7B,MAAM,EAAEkB,MAAM,CAACC,IAAI,CAAC/B,mBAAmB,CAACxB,MAAM,CAAC;IAC/CqC,QAAQ,EAAE,CACR;MACE9F,IAAI,EAAE+G,MAAM,CAACvF,MAAM,CAACyD,mBAAmB,CAACxB,MAAM,CAAC;MAC/CuC,eAAe,EAAEe,MAAM,CAACC,IAAI,CAAC/B,mBAAmB,CAACxB,MAAM,CAAC,CAAC9D,GAAG,CAAC,CAACsH,CAAC,EAAEC,KAAK,KAAK;QACzE,MAAMC,SAAS,GAAGJ,MAAM,CAACC,IAAI,CAACzK,YAAY,CAAC;QAC3C,OAAOA,YAAY,CAAC4K,SAAS,CAACD,KAAK,GAAGC,SAAS,CAACjH,MAAM,CAAC,CAAC,CAAC1D,IAAI;MAC/D,CAAC,CAAC;MACFyJ,WAAW,EAAEc,MAAM,CAACC,IAAI,CAAC/B,mBAAmB,CAACxB,MAAM,CAAC,CAAC9D,GAAG,CAAC,CAACsH,CAAC,EAAEC,KAAK,KAAK;QACrE,MAAMC,SAAS,GAAGJ,MAAM,CAACC,IAAI,CAACzK,YAAY,CAAC;QAC3C,OAAOA,YAAY,CAAC4K,SAAS,CAACD,KAAK,GAAGC,SAAS,CAACjH,MAAM,CAAC,CAAC,CAACzD,MAAM;MACjE,CAAC,CAAC;MACFyJ,WAAW,EAAE,CAAC;MACdE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMuB,0BAA0B,GAAG;IACjC9B,MAAM,EAAEZ,mBAAmB,CAAC5B,OAAO,CAAC1D,GAAG,CAAC4G,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;IAC3DV,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,YAAY;MACnB/F,IAAI,EAAEiF,mBAAmB,CAAC5B,OAAO,CAAC1D,GAAG,CAAC4G,IAAI,IAAIA,IAAI,CAACE,KAAK,CAAC;MACzDT,eAAe,EAAEzJ,YAAY,CAACF,MAAM,CAACG,IAAI;MACzCyJ,WAAW,EAAE1J,YAAY,CAACF,MAAM,CAACI,MAAM;MACvCyJ,WAAW,EAAE,CAAC;MACdQ,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;;EAED;EACA,MAAMiB,4BAA4B,GAAG;IACnC/B,MAAM,EAAEkB,MAAM,CAACC,IAAI,CAAC/B,mBAAmB,CAACV,SAAS,CAAC,CAACoB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAAE;IACjEG,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,YAAY;MACnB/F,IAAI,EAAE+G,MAAM,CAACvF,MAAM,CAACyD,mBAAmB,CAACV,SAAS,CAAC,CAACoB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAC/DK,eAAe,EAAEzJ,YAAY,CAACN,OAAO,CAACO,IAAI;MAC1CyJ,WAAW,EAAE1J,YAAY,CAACN,OAAO,CAACQ,MAAM;MACxCyJ,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB,CAAC;EAEL,CAAC;;EAED;EACA,MAAM0B,4BAA4B,GAAG;IACnChC,MAAM,EAAEkB,MAAM,CAACC,IAAI,CAAC/B,mBAAmB,CAACI,SAAS,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAAE;IAChEG,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,YAAY;MACnB/F,IAAI,EAAE+G,MAAM,CAACvF,MAAM,CAACyD,mBAAmB,CAACI,SAAS,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9DK,eAAe,EAAEzJ,YAAY,CAACL,SAAS,CAACM,IAAI;MAC5CyJ,WAAW,EAAE1J,YAAY,CAACL,SAAS,CAACO,MAAM;MAC1CyJ,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB,CAAC;EAEL,CAAC;;EAED;EACA,MAAM2B,eAAe,GAAG;IACtBC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,KAAK;QACftC,MAAM,EAAE;UACNuC,IAAI,EAAE;YACJC,MAAM,EAAE,qBAAqB;YAC7BC,IAAI,EAAE;UACR,CAAC;UACDC,OAAO,EAAE,EAAE;UACXC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE;QACZ;MACF,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,KAAK;QACdP,IAAI,EAAE;UACJC,MAAM,EAAE,qBAAqB;UAC7BC,IAAI,EAAE,EAAE;UACRM,MAAM,EAAE;QACV,CAAC;QACDC,KAAK,EAAE,SAAS;QAChBN,OAAO,EAAE;UACPO,MAAM,EAAE;QACV;MACF,CAAC;MACDC,OAAO,EAAE;QACP/C,eAAe,EAAE,0BAA0B;QAC3CgD,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,SAAS;QACpBhD,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE,CAAC;QACdqC,OAAO,EAAE,EAAE;QACXW,UAAU,EAAE,CAAC;QACbV,aAAa,EAAE,IAAI;QACnBW,QAAQ,EAAE;UACRd,MAAM,EAAE;QACV,CAAC;QACDe,SAAS,EAAE;UACTf,MAAM,EAAE,qBAAqB;UAC7BO,MAAM,EAAE;QACV;MACF;IACF,CAAC;IACDS,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,IAAI,EAAE;UACJX,KAAK,EAAE;QACT,CAAC;QACDY,KAAK,EAAE;UACLrB,IAAI,EAAE;YACJC,MAAM,EAAE,qBAAqB;YAC7BC,IAAI,EAAE;UACR,CAAC;UACDO,KAAK,EAAE;QACT;MACF,CAAC;MACDa,CAAC,EAAE;QACDF,IAAI,EAAE;UACJb,OAAO,EAAE;QACX,CAAC;QACDc,KAAK,EAAE;UACLrB,IAAI,EAAE;YACJC,MAAM,EAAE,qBAAqB;YAC7BC,IAAI,EAAE;UACR,CAAC;UACDO,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMc,eAAe,GAAG;IACtB5B,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,OAAO;QACjBtC,MAAM,EAAE;UACNuC,IAAI,EAAE;YACJC,MAAM,EAAE,qBAAqB;YAC7BC,IAAI,EAAE;UACR,CAAC;UACDC,OAAO,EAAE,EAAE;UACXC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE;QACZ;MACF,CAAC;MACDM,OAAO,EAAE;QACP/C,eAAe,EAAE,0BAA0B;QAC3CgD,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,SAAS;QACpBhD,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE,CAAC;QACdqC,OAAO,EAAE,EAAE;QACXW,UAAU,EAAE,CAAC;QACbV,aAAa,EAAE,IAAI;QACnBW,QAAQ,EAAE;UACRd,MAAM,EAAE;QACV,CAAC;QACDe,SAAS,EAAE;UACTf,MAAM,EAAE,qBAAqB;UAC7BO,MAAM,EAAE;QACV;MACF;IACF,CAAC;IACDgB,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG;IACvB/B,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,KAAK;QACftC,MAAM,EAAE;UACNuC,IAAI,EAAE;YACJC,MAAM,EAAE,qBAAqB;YAC7BC,IAAI,EAAE;UACR,CAAC;UACDC,OAAO,EAAE,EAAE;UACXC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE;QACZ;MACF,CAAC;MACDM,OAAO,EAAE;QACP/C,eAAe,EAAE,0BAA0B;QAC3CgD,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,SAAS;QACpBhD,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE,CAAC;QACdqC,OAAO,EAAE,EAAE;QACXW,UAAU,EAAE,CAAC;QACbV,aAAa,EAAE,IAAI;QACnBW,QAAQ,EAAE;UACRd,MAAM,EAAE;QACV,CAAC;QACDe,SAAS,EAAE;UACTf,MAAM,EAAE,qBAAqB;UAC7BO,MAAM,EAAE;QACV;MACF;IACF,CAAC;IACDS,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,IAAI,EAAE;UACJX,KAAK,EAAE;QACT,CAAC;QACDY,KAAK,EAAE;UACLrB,IAAI,EAAE;YACJC,MAAM,EAAE,qBAAqB;YAC7BC,IAAI,EAAE;UACR,CAAC;UACDO,KAAK,EAAE;QACT;MACF,CAAC;MACDa,CAAC,EAAE;QACDF,IAAI,EAAE;UACJb,OAAO,EAAE;QACX,CAAC;QACDc,KAAK,EAAE;UACLrB,IAAI,EAAE;YACJC,MAAM,EAAE,qBAAqB;YAC7BC,IAAI,EAAE;UACR,CAAC;UACDO,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC;;EAED;EACA,SAASvF,sBAAsBA,CAACnG,YAAY,EAAE;IAC5C,MAAM4M,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACnG,MAAMC,mBAAmB,GAAG,CAAC,CAAC;;IAE9B;IACAD,MAAM,CAACjJ,OAAO,CAAC0F,KAAK,IAAI;MACtBwD,mBAAmB,CAACxD,KAAK,CAAC,GAAG,CAAC;IAChC,CAAC,CAAC;;IAEF;IACArJ,YAAY,CAAC2D,OAAO,CAACmJ,WAAW,IAAI;MAClC,MAAMrH,IAAI,GAAG,IAAID,IAAI,CAACsH,WAAW,CAACrH,IAAI,CAAC;MACvC,MAAM4D,KAAK,GAAGuD,MAAM,CAACnH,IAAI,CAACsH,QAAQ,CAAC,CAAC,CAAC;MACrCF,mBAAmB,CAACxD,KAAK,CAAC,EAAE;IAC9B,CAAC,CAAC;;IAEF;IACA,OAAOuD,MAAM,CAACpK,GAAG,CAAC6G,KAAK,KAAK;MAC1BA,KAAK;MACLC,KAAK,EAAEuD,mBAAmB,CAACxD,KAAK;IAClC,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,SAAShD,oBAAoBA,CAACrG,YAAY,EAAE;IAC1C,MAAMgN,IAAI,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;IAC3F,MAAMC,iBAAiB,GAAG,CAAC,CAAC;;IAE5B;IACAD,IAAI,CAACrJ,OAAO,CAAC+F,GAAG,IAAI;MAClBuD,iBAAiB,CAACvD,GAAG,CAAC,GAAG,CAAC;IAC5B,CAAC,CAAC;;IAEF;IACA1J,YAAY,CAAC2D,OAAO,CAACmJ,WAAW,IAAI;MAClC,MAAMrH,IAAI,GAAG,IAAID,IAAI,CAACsH,WAAW,CAACrH,IAAI,CAAC;MACvC,MAAMiE,GAAG,GAAGsD,IAAI,CAACvH,IAAI,CAACO,MAAM,CAAC,CAAC,CAAC;MAC/BiH,iBAAiB,CAACvD,GAAG,CAAC,EAAE;IAC1B,CAAC,CAAC;;IAEF;IACA,OAAOsD,IAAI,CAACxK,GAAG,CAACkH,GAAG,KAAK;MACtBA,GAAG;MACHJ,KAAK,EAAE2D,iBAAiB,CAACvD,GAAG;IAC9B,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,SAASnD,qBAAqBA,CAACvG,YAAY,EAAE;IAC3C,MAAMkN,kBAAkB,GAAG,CAAC,CAAC;;IAE7B;IACAlN,YAAY,CAAC2D,OAAO,CAACmJ,WAAW,IAAI;MAClC,MAAMK,IAAI,GAAGL,WAAW,CAACK,IAAI,IAAI,OAAO;MACxCD,kBAAkB,CAACC,IAAI,CAAC,GAAG,CAACD,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOD,kBAAkB;EAC3B;;EAEA;EACA,SAAShG,yBAAyBA,CAAChH,OAAO,EAAE;IAC1C,MAAMkN,sBAAsB,GAAG,CAAC,CAAC;;IAEjC;IACAlN,OAAO,CAACyD,OAAO,CAACK,MAAM,IAAI;MACxB;MACA,IAAIA,MAAM,CAACwC,SAAS,IAAIxC,MAAM,CAACwC,SAAS,CAACC,UAAU,KAAK,mBAAmB,EAAE;MAE7E,IAAI0G,IAAI,GAAG,OAAO;;MAElB;MACA,IAAInJ,MAAM,CAACqJ,aAAa,EAAE;QACxBF,IAAI,GAAGnJ,MAAM,CAACqJ,aAAa;MAC7B,CAAC,MAAM,IAAIrJ,MAAM,CAACsJ,SAAS,IAAItJ,MAAM,CAACsJ,SAAS,CAACH,IAAI,EAAE;QACpDA,IAAI,GAAGnJ,MAAM,CAACsJ,SAAS,CAACH,IAAI;MAC9B;MAEAC,sBAAsB,CAACD,IAAI,CAAC,GAAG,CAACC,sBAAsB,CAACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACxE,CAAC,CAAC;;IAEF;IACA,MAAMI,OAAO,GAAG3D,MAAM,CAAC2D,OAAO,CAACH,sBAAsB,CAAC;IACtD,IAAIG,OAAO,CAACxK,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMyK,aAAa,GAAGD,OAAO,CAAClF,IAAI,CAAC,CAAClD,CAAC,EAAEmD,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAGnD,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,MAAMsI,IAAI,GAAGD,aAAa,CAAChF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACtC,MAAMkF,MAAM,GAAGF,aAAa,CAAChF,KAAK,CAAC,CAAC,CAAC,CAACmF,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAE/E,MAAMjK,MAAM,GAAGgG,MAAM,CAACkE,WAAW,CAACL,IAAI,CAAC;MACvC7J,MAAM,CAAC,OAAO,CAAC,GAAG8J,MAAM;MACxB,OAAO9J,MAAM;IACf;IAEA,OAAOwJ,sBAAsB;EAC/B;;EAEA;EACA,SAASjG,iBAAiBA,CAACjH,OAAO,EAAE;IAClC,MAAM0M,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACnG,MAAMmB,cAAc,GAAG,CAAC,CAAC;;IAEzB;IACAnB,MAAM,CAACjJ,OAAO,CAAC0F,KAAK,IAAI;MACtB0E,cAAc,CAAC1E,KAAK,CAAC,GAAG,CAAC;IAC3B,CAAC,CAAC;;IAEF;IACAnJ,OAAO,CAACyD,OAAO,CAACK,MAAM,IAAI;MACxB,IAAI;QACF;QACA,IAAIA,MAAM,CAACwC,SAAS,IAAIxC,MAAM,CAACwC,SAAS,CAACC,UAAU,KAAK,mBAAmB,EAAE;;QAE7E;QACA,IAAIuH,OAAO,GAAG,IAAI;QAClB,IAAIhK,MAAM,CAACiK,aAAa,EAAED,OAAO,GAAGhK,MAAM,CAACiK,aAAa,CAAC,KACpD,IAAIjK,MAAM,CAACuE,SAAS,EAAEyF,OAAO,GAAGhK,MAAM,CAACuE,SAAS,CAAC,KACjD,IAAIvE,MAAM,CAACyB,IAAI,EAAEuI,OAAO,GAAGhK,MAAM,CAACyB,IAAI,CAAC,KACvC,IAAIzB,MAAM,CAACkK,YAAY,EAAEF,OAAO,GAAGhK,MAAM,CAACkK,YAAY;QAE3D,IAAI,CAACF,OAAO,EAAE,OAAO,CAAC;;QAEtB,MAAMvI,IAAI,GAAG,IAAID,IAAI,CAACwI,OAAO,CAAC;QAC9B,IAAIG,KAAK,CAAC1I,IAAI,CAAC2I,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;;QAEnC,MAAM/E,KAAK,GAAGuD,MAAM,CAACnH,IAAI,CAACsH,QAAQ,CAAC,CAAC,CAAC;QACrCgB,cAAc,CAAC1E,KAAK,CAAC,EAAE;MACzB,CAAC,CAAC,OAAOrI,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC,CAAC;;IAEF;IACA,OAAO4L,MAAM,CAACpK,GAAG,CAAC6G,KAAK,KAAK;MAC1BA,KAAK;MACLC,KAAK,EAAEyE,cAAc,CAAC1E,KAAK;IAC7B,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,SAAShC,mBAAmBA,CAACnH,OAAO,EAAE;IACpC,MAAMmO,gBAAgB,GAAG,CAAC,CAAC;;IAE3B;IACAnO,OAAO,CAACyD,OAAO,CAACK,MAAM,IAAI;MACxB;MACA,IAAIA,MAAM,CAACwC,SAAS,IAAIxC,MAAM,CAACwC,SAAS,CAACC,UAAU,KAAK,mBAAmB,EAAE;;MAE7E;MACA,IAAIvD,OAAO,GAAG,SAAS;MACvB,IAAIc,MAAM,CAACsK,WAAW,EAAE;QACtBpL,OAAO,GAAGc,MAAM,CAACsK,WAAW;MAC9B,CAAC,MAAM,IAAItK,MAAM,CAACd,OAAO,IAAIc,MAAM,CAACd,OAAO,CAACG,IAAI,EAAE;QAChDH,OAAO,GAAGc,MAAM,CAACd,OAAO,CAACG,IAAI;MAC/B,CAAC,MAAM,IAAIW,MAAM,CAACZ,SAAS,EAAE;QAC3B;QACA,MAAMmL,YAAY,GAAGjO,QAAQ,CAACkO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjL,GAAG,KAAKQ,MAAM,CAACZ,SAAS,IAAIqL,CAAC,CAACrL,SAAS,KAAKY,MAAM,CAACZ,SAAS,CAAC;QACvG,IAAImL,YAAY,EAAE;UAChBrL,OAAO,GAAGqL,YAAY,CAAClL,IAAI;QAC7B;MACF;MAEA,IAAI,CAACgL,gBAAgB,CAACnL,OAAO,CAAC,EAAE;QAC9BmL,gBAAgB,CAACnL,OAAO,CAAC,GAAG;UAC1B8B,KAAK,EAAE,CAAC;UACR4B,QAAQ,EAAE,CAAC;UACXC,QAAQ,EAAE,CAAC;UACX5B,OAAO,EAAE;QACX,CAAC;MACH;MAEAoJ,gBAAgB,CAACnL,OAAO,CAAC,CAAC8B,KAAK,EAAE;MACjC,IAAIhB,MAAM,CAACc,MAAM,KAAK,UAAU,EAAEuJ,gBAAgB,CAACnL,OAAO,CAAC,CAAC0D,QAAQ,EAAE,CAAC,KAClE,IAAI5C,MAAM,CAACc,MAAM,KAAK,UAAU,IAAId,MAAM,CAACc,MAAM,KAAK,QAAQ,EAAEuJ,gBAAgB,CAACnL,OAAO,CAAC,CAAC2D,QAAQ,EAAE,CAAC,KACrGwH,gBAAgB,CAACnL,OAAO,CAAC,CAAC+B,OAAO,EAAE;IAC1C,CAAC,CAAC;;IAEF;IACA,MAAMyJ,cAAc,GAAG9E,MAAM,CAAC2D,OAAO,CAACc,gBAAgB,CAAC,CACpDhG,IAAI,CAAC,CAAClD,CAAC,EAAEmD,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,CAACtD,KAAK,GAAGG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC,CACvCwD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAEf,OAAOoB,MAAM,CAACkE,WAAW,CAACY,cAAc,CAAC;EAC3C;;EAEA;EACA,SAAS3H,sBAAsBA,CAAC7G,OAAO,EAAEyO,KAAK,EAAE;IAC9C;IACA,MAAMC,YAAY,GAAG1O,OAAO,CAACgF,MAAM,CAAClB,MAAM,IAAI;MAC5C;MACA,IAAIA,MAAM,CAACwC,SAAS,IAAIxC,MAAM,CAACwC,SAAS,CAACC,UAAU,KAAK,mBAAmB,EAAE,OAAO,KAAK;;MAEzF;MACA,IAAIzC,MAAM,CAAC2K,KAAK,CAAC,IAAI,OAAO3K,MAAM,CAAC2K,KAAK,CAAC,KAAK,QAAQ,IAAI3K,MAAM,CAAC2K,KAAK,CAAC,GAAG,CAAC,EAAE;QAC3E,OAAO,IAAI;MACb;;MAEA;MACA,IAAI3K,MAAM,CAAC6K,OAAO,IAAI7K,MAAM,CAAC6K,OAAO,CAACF,KAAK,CAAC,IAAI,OAAO3K,MAAM,CAAC6K,OAAO,CAACF,KAAK,CAAC,KAAK,QAAQ,IAAI3K,MAAM,CAAC6K,OAAO,CAACF,KAAK,CAAC,GAAG,CAAC,EAAE;QACrH,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;IAEF,IAAIC,YAAY,CAAC7L,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;IAEvC;IACA,MAAM6K,GAAG,GAAGgB,YAAY,CAACjB,MAAM,CAAC,CAAC3I,KAAK,EAAEhB,MAAM,KAAK;MACjD,IAAIA,MAAM,CAAC2K,KAAK,CAAC,IAAI,OAAO3K,MAAM,CAAC2K,KAAK,CAAC,KAAK,QAAQ,EAAE;QACtD,OAAO3J,KAAK,GAAGhB,MAAM,CAAC2K,KAAK,CAAC;MAC9B,CAAC,MAAM,IAAI3K,MAAM,CAAC6K,OAAO,IAAI7K,MAAM,CAAC6K,OAAO,CAACF,KAAK,CAAC,IAAI,OAAO3K,MAAM,CAAC6K,OAAO,CAACF,KAAK,CAAC,KAAK,QAAQ,EAAE;QAC/F,OAAO3J,KAAK,GAAGhB,MAAM,CAAC6K,OAAO,CAACF,KAAK,CAAC;MACtC;MACA,OAAO3J,KAAK;IACd,CAAC,EAAE,CAAC,CAAC;IAEL,OAAO,CAAC4I,GAAG,GAAGgB,YAAY,CAAC7L,MAAM,EAAE+L,OAAO,CAAC,CAAC,CAAC;EAC/C;;EAEA;EACA,SAAStH,0BAA0BA,CAACpH,QAAQ,EAAE;IAC5C,MAAM2O,SAAS,GAAG;MAChB,MAAM,EAAE,CAAC;MACT,OAAO,EAAE,CAAC;MACV,OAAO,EAAE,CAAC;MACV,OAAO,EAAE,CAAC;MACV,KAAK,EAAE;IACT,CAAC;IAED3O,QAAQ,CAACuD,OAAO,CAACqL,OAAO,IAAI;MAC1B,MAAMC,GAAG,GAAGD,OAAO,CAACC,GAAG,IAAI,CAAC;MAC5B,IAAIA,GAAG,IAAI,EAAE,EAAEF,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,KAC9B,IAAIE,GAAG,IAAI,EAAE,EAAEF,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,KACpC,IAAIE,GAAG,IAAI,EAAE,EAAEF,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,KACpC,IAAIE,GAAG,IAAI,EAAE,EAAEF,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,KACpCA,SAAS,CAAC,KAAK,CAAC,EAAE;IACzB,CAAC,CAAC;IAEF,OAAOA,SAAS;EAClB;;EAEA;EACA,SAASrH,6BAA6BA,CAACtH,QAAQ,EAAE;IAC/C,MAAM8O,kBAAkB,GAAG;MACzB,MAAM,EAAE,CAAC;MACT,QAAQ,EAAE,CAAC;MACX,OAAO,EAAE;IACX,CAAC;IAED9O,QAAQ,CAACuD,OAAO,CAACqL,OAAO,IAAI;MAC1B,MAAMG,MAAM,GAAGH,OAAO,CAACG,MAAM,IAAI,OAAO;MACxCD,kBAAkB,CAACC,MAAM,CAAC,GAAG,CAACD,kBAAkB,CAACC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IACpE,CAAC,CAAC;IAEF,OAAOD,kBAAkB;EAC3B;;EAEA;EACA,SAAStH,qCAAqCA,CAACxH,QAAQ,EAAE;IACvD,MAAMgP,mBAAmB,GAAG,CAAC,CAAC;IAE9BhP,QAAQ,CAACuD,OAAO,CAACqL,OAAO,IAAI;MAC1B,IAAIA,OAAO,CAACK,WAAW,IAAIL,OAAO,CAACK,WAAW,CAACC,eAAe,EAAE;QAC9DN,OAAO,CAACK,WAAW,CAACC,eAAe,CAAC3L,OAAO,CAAC4L,OAAO,IAAI;UACrDH,mBAAmB,CAACG,OAAO,CAAC,GAAG,CAACH,mBAAmB,CAACG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;QACxE,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACA,OAAO3F,MAAM,CAACkE,WAAW,CACvBlE,MAAM,CAAC2D,OAAO,CAAC6B,mBAAmB,CAAC,CAAC/G,IAAI,CAAC,CAAClD,CAAC,EAAEmD,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAGnD,CAAC,CAAC,CAAC,CAAC,CAChE,CAAC;EACH;;EAEA;EACA,SAAS4C,mBAAmBA,CAACyH,MAAM,EAAE;IACnC,MAAMC,gBAAgB,GAAG,CAAC,CAAC;IAE3BD,MAAM,CAAC7L,OAAO,CAAC+L,KAAK,IAAI;MACtB,MAAMvC,IAAI,GAAGuC,KAAK,CAACvC,IAAI,IAAI,OAAO;MAClCsC,gBAAgB,CAACtC,IAAI,CAAC,GAAG,CAACsC,gBAAgB,CAACtC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5D,CAAC,CAAC;IAEF,OAAOsC,gBAAgB;EACzB;;EAEA;EACA,SAASzH,oBAAoBA,CAACwH,MAAM,EAAE;IACpC,MAAM5C,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACnG,MAAM+C,iBAAiB,GAAG,CAAC,CAAC;;IAE5B;IACA/C,MAAM,CAACjJ,OAAO,CAAC0F,KAAK,IAAI;MACtBsG,iBAAiB,CAACtG,KAAK,CAAC,GAAG,CAAC;IAC9B,CAAC,CAAC;;IAEF;IACAmG,MAAM,CAAC7L,OAAO,CAAC+L,KAAK,IAAI;MACtB,IAAI;QACF,MAAMjK,IAAI,GAAG,IAAID,IAAI,CAACkK,KAAK,CAACnH,SAAS,CAAC;QACtC,IAAI4F,KAAK,CAAC1I,IAAI,CAAC2I,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;;QAEnC,MAAM/E,KAAK,GAAGuD,MAAM,CAACnH,IAAI,CAACsH,QAAQ,CAAC,CAAC,CAAC;QACrC4C,iBAAiB,CAACtG,KAAK,CAAC,EAAE;MAC5B,CAAC,CAAC,OAAOrI,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC,CAAC;;IAEF;IACA,OAAO4L,MAAM,CAACpK,GAAG,CAAC6G,KAAK,KAAK;MAC1BA,KAAK;MACLC,KAAK,EAAEqG,iBAAiB,CAACtG,KAAK;IAChC,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,SAASpB,sBAAsBA,CAACuH,MAAM,EAAE;IACtC,MAAMI,mBAAmB,GAAG,CAAC,CAAC;IAE9BJ,MAAM,CAAC7L,OAAO,CAAC+L,KAAK,IAAI;MACtB,MAAMxM,OAAO,GAAGwM,KAAK,CAACpB,WAAW,IAAI,iBAAiB;MACtDsB,mBAAmB,CAAC1M,OAAO,CAAC,GAAG,CAAC0M,mBAAmB,CAAC1M,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;IACxE,CAAC,CAAC;;IAEF;IACA,OAAO0G,MAAM,CAACkE,WAAW,CACvBlE,MAAM,CAAC2D,OAAO,CAACqC,mBAAmB,CAAC,CAACvH,IAAI,CAAC,CAAClD,CAAC,EAAEmD,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAGnD,CAAC,CAAC,CAAC,CAAC,CAChE,CAAC;EACH;;EAEA;EACA,SAASgD,sBAAsBA,CAACqH,MAAM,EAAE;IACtC,MAAMK,mBAAmB,GAAG,CAAC,CAAC;IAE9BL,MAAM,CAAC7L,OAAO,CAAC+L,KAAK,IAAI;MACtB,MAAMV,OAAO,GAAGU,KAAK,CAACI,WAAW,IAAI,iBAAiB;MACtDD,mBAAmB,CAACb,OAAO,CAAC,GAAG,CAACa,mBAAmB,CAACb,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;IACxE,CAAC,CAAC;;IAEF;IACA,OAAOpF,MAAM,CAACkE,WAAW,CACvBlE,MAAM,CAAC2D,OAAO,CAACsC,mBAAmB,CAAC,CAACxH,IAAI,CAAC,CAAClD,CAAC,EAAEmD,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAGnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAACqD,KAAK,CAAC,CAAC,EAAE,EAAE,CAC7E,CAAC;EACH;EAEA,MAAMuH,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMhH,IAAI,GAAG;IACX4G,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAE9D,CAAC,EAAE;IAAG,CAAC;IAC7B+D,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAE9D,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAIrL,OAAO,EAAE,oBAAOlC,OAAA,CAAChC,MAAM;IAAAyT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAE9B,oBACE5R,OAAA;IAAK6R,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC9R,OAAA,CAACjC,YAAY;MAACgU,MAAM,EAAE/Q,WAAY;MAACgR,SAAS,EAAE/Q;IAAe;MAAAwQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChE5R,OAAA;MAAK6R,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD9R,OAAA,CAAClC,MAAM;QAACmU,aAAa,EAAEA,CAAA,KAAMhR,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAyQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7D5R,OAAA;QAAM6R,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eAClF9R,OAAA;UAAK6R,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/B1P,KAAK,iBACJpC,OAAA,CAAC/B,MAAM,CAACiU,GAAG;YACTC,OAAO,EAAE;cAAEd,OAAO,EAAE,CAAC;cAAE9D,CAAC,EAAE,CAAC;YAAG,CAAE;YAChC6E,OAAO,EAAE;cAAEf,OAAO,EAAE,CAAC;cAAE9D,CAAC,EAAE;YAAE,CAAE;YAC9BsE,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7E9R,OAAA;cAAK6R,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9R,OAAA;gBAAK6R,SAAS,EAAC,2BAA2B;gBAACjH,IAAI,EAAC,cAAc;gBAACyH,OAAO,EAAC,WAAW;gBAAAP,QAAA,eAChF9R,OAAA;kBAAMsS,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,yNAAyN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvQ,CAAC,eACN5R,OAAA;gBAAG6R,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE1P;cAAK;gBAAAqP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eACD5R,OAAA,CAAC/B,MAAM,CAACiU,GAAG;YAACC,OAAO,EAAE;cAAEd,OAAO,EAAE;YAAE,CAAE;YAACe,OAAO,EAAE;cAAEf,OAAO,EAAE;YAAE,CAAE;YAACE,UAAU,EAAE;cAAEkB,QAAQ,EAAE;YAAI,CAAE;YAAAX,QAAA,gBAC1F9R,OAAA;cAAK6R,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9R,OAAA;gBAAI6R,SAAS,EAAE,wCAAwC5R,mBAAmB,CAACC,OAAO,QAAS;gBAAA4R,QAAA,EAAC;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1G5R,OAAA;gBAAG6R,SAAS,EAAE,SAAS5R,mBAAmB,CAACI,IAAI,GAAI;gBAAAyR,QAAA,EAAC;cAAkC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eAGN5R,OAAA,CAAC/B,MAAM,CAACiU,GAAG;cACTQ,QAAQ,EAAEvB,SAAU;cACpBgB,OAAO,EAAC,QAAQ;cAChBQ,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBhB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBAErE9R,OAAA,CAAC/B,MAAM,CAACiU,GAAG;gBACTQ,QAAQ,EAAElI,IAAK;gBACfqH,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,eAE5I9R,OAAA;kBAAK6R,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9R,OAAA;oBAAK6R,SAAS,EAAC,8HAA8H;oBAAAC,QAAA,eAC3I9R,OAAA,CAAC7B,aAAa;sBAAC0T,SAAS,EAAE,iBAAiB5R,mBAAmB,CAACC,OAAO;oBAAI;sBAAAuR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACN5R,OAAA;oBAAA8R,QAAA,gBACE9R,OAAA;sBAAG6R,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACvE5R,OAAA;sBAAG6R,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAE3L,qBAAqB,CAACC;oBAAK;sBAAAqL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEb5R,OAAA,CAAC/B,MAAM,CAACiU,GAAG;gBACTQ,QAAQ,EAAElI,IAAK;gBACfqH,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,eAE5I9R,OAAA;kBAAK6R,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9R,OAAA;oBAAK6R,SAAS,EAAC,8HAA8H;oBAAAC,QAAA,eAC3I9R,OAAA,CAAC5B,MAAM;sBAACyT,SAAS,EAAE,iBAAiB5R,mBAAmB,CAACC,OAAO;oBAAI;sBAAAuR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACN5R,OAAA;oBAAA8R,QAAA,gBACE9R,OAAA;sBAAG6R,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClE5R,OAAA;sBAAG6R,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAEhK,gBAAgB,CAAC1B;oBAAK;sBAAAqL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEb5R,OAAA,CAAC/B,MAAM,CAACiU,GAAG;gBACTQ,QAAQ,EAAElI,IAAK;gBACfqH,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,eAE5I9R,OAAA;kBAAK6R,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9R,OAAA;oBAAK6R,SAAS,EAAC,8HAA8H;oBAAAC,QAAA,eAC3I9R,OAAA,CAAClB,aAAa;sBAAC+S,SAAS,EAAE,iBAAiB5R,mBAAmB,CAACC,OAAO;oBAAI;sBAAAuR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACN5R,OAAA;oBAAA8R,QAAA,gBACE9R,OAAA;sBAAG6R,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrE5R,OAAA;sBAAG6R,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAE5I,mBAAmB,CAAC9C;oBAAK;sBAAAqL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGb5R,OAAA,CAAC/B,MAAM,CAACiU,GAAG;cACTQ,QAAQ,EAAElI,IAAK;cACfqH,SAAS,EAAC,uHAAuH;cAAAC,QAAA,gBAEjI9R,OAAA;gBAAK6R,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,eACvC9R,OAAA;kBAAK6R,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1B9R,OAAA;oBACE6R,SAAS,EAAE,4CACT3Q,SAAS,KAAK,UAAU,GACpB,WAAWjB,mBAAmB,CAACC,OAAO,WAAWD,mBAAmB,CAACC,OAAO,GAAG,GAC/E,4EAA4E,EAC/E;oBACH4S,OAAO,EAAEA,CAAA,KAAM3R,YAAY,CAAC,UAAU,CAAE;oBAAA2Q,QAAA,EACzC;kBAED;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5R,OAAA;oBACE6R,SAAS,EAAE,4CACT3Q,SAAS,KAAK,cAAc,GACxB,WAAWjB,mBAAmB,CAACC,OAAO,WAAWD,mBAAmB,CAACC,OAAO,GAAG,GAC/E,4EAA4E,EAC/E;oBACH4S,OAAO,EAAEA,CAAA,KAAM3R,YAAY,CAAC,cAAc,CAAE;oBAAA2Q,QAAA,EAC7C;kBAED;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5R,OAAA;oBACE6R,SAAS,EAAE,4CACT3Q,SAAS,KAAK,SAAS,GACnB,WAAWjB,mBAAmB,CAACC,OAAO,WAAWD,mBAAmB,CAACC,OAAO,GAAG,GAC/E,4EAA4E,EAC/E;oBACH4S,OAAO,EAAEA,CAAA,KAAM3R,YAAY,CAAC,SAAS,CAAE;oBAAA2Q,QAAA,EACxC;kBAED;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5R,OAAA;oBACE6R,SAAS,EAAE,4CACT3Q,SAAS,KAAK,YAAY,GACtB,WAAWjB,mBAAmB,CAACC,OAAO,WAAWD,mBAAmB,CAACC,OAAO,GAAG,GAC/E,4EAA4E,EAC/E;oBACH4S,OAAO,EAAEA,CAAA,KAAM3R,YAAY,CAAC,YAAY,CAAE;oBAAA2Q,QAAA,EAC3C;kBAED;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5R,OAAA;oBACE6R,SAAS,EAAE,4CACT3Q,SAAS,KAAK,UAAU,GACpB,WAAWjB,mBAAmB,CAACC,OAAO,WAAWD,mBAAmB,CAACC,OAAO,GAAG,GAC/E,4EAA4E,EAC/E;oBACH4S,OAAO,EAAEA,CAAA,KAAM3R,YAAY,CAAC,UAAU,CAAE;oBAAA2Q,QAAA,EACzC;kBAED;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5R,OAAA;gBAAK6R,SAAS,EAAC,KAAK;gBAAAC,QAAA,GAEjB5Q,SAAS,KAAK,UAAU,iBACvBlB,OAAA;kBAAA8R,QAAA,gBACE9R,OAAA;oBAAK6R,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eACrD9R,OAAA;sBAAI6R,SAAS,EAAE,2BAA2B5R,mBAAmB,CAACC,OAAO,qBAAsB;sBAAA4R,QAAA,gBACzF9R,OAAA,CAAC9B,WAAW;wBAAC2T,SAAS,EAAC;sBAAc;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,uBAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACN5R,OAAA;oBAAK6R,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxC9R,OAAA;sBAAK6R,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9R,OAAA,CAACjB,GAAG;wBAACkF,IAAI,EAAE4F,iBAAkB;wBAACkJ,OAAO,EAAEhH;sBAAgB;wBAAA0F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAGA1Q,SAAS,KAAK,cAAc,iBAC3BlB,OAAA;kBAAA8R,QAAA,gBACE9R,OAAA;oBAAK6R,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eACrD9R,OAAA;sBAAI6R,SAAS,EAAE,2BAA2B5R,mBAAmB,CAACC,OAAO,qBAAsB;sBAAA4R,QAAA,gBACzF9R,OAAA,CAAC7B,aAAa;wBAAC0T,SAAS,EAAC;sBAAc;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,yBAE5C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,gBACxE9R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAK;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC1D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAE3L,qBAAqB,CAACC;sBAAK;wBAAAqL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAE3L,qBAAqB,CAACE;sBAAO;wBAAAoL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAS;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC9D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAE3L,qBAAqB,CAACK;sBAAS;wBAAAiL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAS;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC9D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAE3L,qBAAqB,CAACM;sBAAS;wBAAAgL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAK;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC1D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAE3L,qBAAqB,CAACO;sBAAK;wBAAA+K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD9R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACrB,UAAU;0BAACkT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,sBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAACf,QAAQ;0BAACgF,IAAI,EAAEqG,2BAA4B;0BAACyI,OAAO,EAAEnF;wBAAgB;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACrB,UAAU;0BAACkT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,qBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAAChB,GAAG;0BAACiF,IAAI,EAAE8G,2BAA4B;0BAACgI,OAAO,EAAEnF;wBAAgB;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5R,OAAA;oBAAK6R,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD9R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACnB,WAAW;0BAACgT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,yBAElF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAACd,IAAI;0BAAC+E,IAAI,EAAEsG,4BAA6B;0BAACwI,OAAO,EAAEhF;wBAAiB;0BAAA0D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACpB,UAAU;0BAACiT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,uBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAACjB,GAAG;0BAACkF,IAAI,EAAE4G,0BAA2B;0BAACkI,OAAO,EAAEhH;wBAAgB;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAGA1Q,SAAS,KAAK,SAAS,iBACtBlB,OAAA;kBAAA8R,QAAA,gBACE9R,OAAA;oBAAK6R,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eACrD9R,OAAA;sBAAI6R,SAAS,EAAE,2BAA2B5R,mBAAmB,CAACC,OAAO,qBAAsB;sBAAA4R,QAAA,gBACzF9R,OAAA,CAAC5B,MAAM;wBAACyT,SAAS,EAAC;sBAAc;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,oBAErC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,gBACxE9R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAK;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC1D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAEhK,gBAAgB,CAAC1B;sBAAK;wBAAAqL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAEhK,gBAAgB,CAACzB;sBAAO;wBAAAoL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3E,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC7D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAEhK,gBAAgB,CAACE;sBAAQ;wBAAAyJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC7D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAEhK,gBAAgB,CAACG;sBAAQ;wBAAAwJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAY;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACjE5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,GAAEhK,gBAAgB,CAACI,mBAAmB,EAAC,IAAE;sBAAA;wBAAAuJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD9R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACrB,UAAU;0BAACkT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,iBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAACf,QAAQ;0BAACgF,IAAI,EAAEoH,sBAAuB;0BAAC0H,OAAO,EAAEnF;wBAAgB;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACrB,UAAU;0BAACkT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,mBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAAChB,GAAG;0BAACiF,IAAI,EAAEqH,+BAAgC;0BAACyH,OAAO,EAAEnF;wBAAgB;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5R,OAAA;oBAAK6R,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,gBAC5E9R,OAAA;sBAAI6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACtE9R,OAAA,CAACnB,WAAW;wBAACgT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;sBAAI;wBAAAuR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,oBAElF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5R,OAAA;sBAAK6R,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9R,OAAA,CAACd,IAAI;wBAAC+E,IAAI,EAAEsH,uBAAwB;wBAACwH,OAAO,EAAEhF;sBAAiB;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,gBACvE9R,OAAA;sBAAI6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACtE9R,OAAA,CAACzB,cAAc;wBAACsT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;sBAAI;wBAAAuR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,uBAErF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5R,OAAA;sBAAK6R,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,eAC9B9R,OAAA;wBAAO6R,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBACpD9R,OAAA;0BAAO6R,SAAS,EAAC,YAAY;0BAAAC,QAAA,eAC3B9R,OAAA;4BAAA8R,QAAA,gBACE9R,OAAA;8BAAIgT,KAAK,EAAC,KAAK;8BAACnB,SAAS,EAAC,gFAAgF;8BAAAC,QAAA,EAAC;4BAE3G;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACL5R,OAAA;8BAAIgT,KAAK,EAAC,KAAK;8BAACnB,SAAS,EAAC,gFAAgF;8BAAAC,QAAA,EAAC;4BAE3G;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACL5R,OAAA;8BAAIgT,KAAK,EAAC,KAAK;8BAACnB,SAAS,EAAC,gFAAgF;8BAAAC,QAAA,EAAC;4BAE3G;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACL5R,OAAA;8BAAIgT,KAAK,EAAC,KAAK;8BAACnB,SAAS,EAAC,gFAAgF;8BAAAC,QAAA,EAAC;4BAE3G;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACL5R,OAAA;8BAAIgT,KAAK,EAAC,KAAK;8BAACnB,SAAS,EAAC,gFAAgF;8BAAAC,QAAA,EAAC;4BAE3G;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACR5R,OAAA;0BAAO6R,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EACjD9G,MAAM,CAAC2D,OAAO,CAAC7G,gBAAgB,CAACU,SAAS,CAAC,CACxCiB,IAAI,CAAC,CAAClD,CAAC,EAAEmD,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,CAACtD,KAAK,GAAGG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC,CACvCwD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXhG,GAAG,CAAC,CAAC,CAACU,OAAO,EAAE2O,KAAK,CAAC,kBACpBjT,OAAA;4BAAA8R,QAAA,gBACE9R,OAAA;8BAAI6R,SAAS,EAAC,+DAA+D;8BAAAC,QAAA,EAC1ExN;4BAAO;8BAAAmN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CAAC,eACL5R,OAAA;8BAAI6R,SAAS,EAAC,mDAAmD;8BAAAC,QAAA,EAC9DmB,KAAK,CAAC7M;4BAAK;8BAAAqL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACL5R,OAAA;8BAAI6R,SAAS,EAAC,mDAAmD;8BAAAC,QAAA,eAC/D9R,OAAA;gCAAM6R,SAAS,EAAC,2FAA2F;gCAAAC,QAAA,EACxGmB,KAAK,CAACjL;8BAAQ;gCAAAyJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACX;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACL5R,OAAA;8BAAI6R,SAAS,EAAC,mDAAmD;8BAAAC,QAAA,eAC/D9R,OAAA;gCAAM6R,SAAS,EAAC,uFAAuF;gCAAAC,QAAA,EACpGmB,KAAK,CAAChL;8BAAQ;gCAAAwJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACX;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACL5R,OAAA;8BAAI6R,SAAS,EAAC,mDAAmD;8BAAAC,QAAA,eAC/D9R,OAAA;gCAAM6R,SAAS,EAAC,6FAA6F;gCAAAC,QAAA,EAC1GmB,KAAK,CAAC5M;8BAAO;gCAAAoL,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA,GArBEtN,OAAO;4BAAAmN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAsBZ,CACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAGA1Q,SAAS,KAAK,YAAY,iBACzBlB,OAAA;kBAAA8R,QAAA,gBACE9R,OAAA;oBAAK6R,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eACrD9R,OAAA;sBAAI6R,SAAS,EAAE,2BAA2B5R,mBAAmB,CAACC,OAAO,qBAAsB;sBAAA4R,QAAA,gBACzF9R,OAAA,CAACrB,UAAU;wBAACkT,SAAS,EAAC;sBAAc;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,wBAEzC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,gBACxE9R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAK;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC1D5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAE5I,mBAAmB,CAAC9C;sBAAK;wBAAAqL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC9R,OAAA;wBAAG6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAa;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAClE5R,OAAA;wBAAG6R,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAE5I,mBAAmB,CAACM,YAAY,CAACrF;sBAAM;wBAAAsN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD9R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACrB,UAAU;0BAACkT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,sBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAAChB,GAAG;0BAACiF,IAAI,EAAE0H,yBAA0B;0BAACoH,OAAO,EAAEnF;wBAAgB;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACnB,WAAW;0BAACgT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,uBAElF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAACd,IAAI;0BAAC+E,IAAI,EAAE2H,0BAA2B;0BAACmH,OAAO,EAAEhF;wBAAiB;0BAAA0D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5R,OAAA;oBAAK6R,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD9R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACpB,UAAU;0BAACiT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,8BAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAACjB,GAAG;0BAACkF,IAAI,EAAE4H,4BAA6B;0BAACkH,OAAO,EAAEhH;wBAAgB;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACpB,UAAU;0BAACiT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,8BAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAACjB,GAAG;0BAACkF,IAAI,EAAE6H,4BAA6B;0BAACiH,OAAO,EAAEhH;wBAAgB;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5R,OAAA;oBAAK6R,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD9R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAAClB,aAAa;0BAAC+S,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,qBAEpF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,eACnC9R,OAAA;0BAAK6R,SAAS,EAAC,WAAW;0BAAAC,QAAA,EACvB5I,mBAAmB,CAACM,YAAY,CAAC5F,GAAG,CAAC,CAACkN,KAAK,EAAE3F,KAAK,kBACjDnL,OAAA;4BAAiB6R,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,eACpD9R,OAAA;8BAAK6R,SAAS,EAAC,kCAAkC;8BAAAC,QAAA,gBAC/C9R,OAAA;gCAAA8R,QAAA,gBACE9R,OAAA;kCAAG6R,SAAS,EAAC,mCAAmC;kCAAAC,QAAA,EAAEhB,KAAK,CAACvC;gCAAI;kCAAAkD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC,eACjE5R,OAAA;kCAAG6R,SAAS,EAAC,uBAAuB;kCAAAC,QAAA,GAAC,WAAS,EAAChB,KAAK,CAACI,WAAW;gCAAA;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC,eACrE5R,OAAA;kCAAG6R,SAAS,EAAC,uBAAuB;kCAAAC,QAAA,GAAC,WAAS,EAAChB,KAAK,CAACpB,WAAW;gCAAA;kCAAA+B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAClE,CAAC,eACN5R,OAAA;gCAAM6R,SAAS,EAAC,uBAAuB;gCAAAC,QAAA,EACpC,IAAIlL,IAAI,CAACkK,KAAK,CAACnH,SAAS,CAAC,CAACuJ,kBAAkB,CAAC;8BAAC;gCAAAzB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3C,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ;0BAAC,GAVEzG,KAAK;4BAAAsG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAWV,CACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACrB,UAAU;0BAACkT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,sBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,eAChD9R,OAAA;0BAAK6R,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,gBACrC9R,OAAA;4BAAK6R,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1B9R,OAAA;8BAAG6R,SAAS,EAAC,kCAAkC;8BAAAC,QAAA,EAAE5I,mBAAmB,CAAC9C;4BAAK;8BAAAqL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC/E5R,OAAA;8BAAG6R,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,EAAC;4BAAgB;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtD,CAAC,eACN5R,OAAA;4BAAK6R,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1B9R,OAAA;8BAAG6R,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,EAAE9G,MAAM,CAACC,IAAI,CAAC/B,mBAAmB,CAACxB,MAAM,CAAC,CAACvD;4BAAM;8BAAAsN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACrG5R,OAAA;8BAAG6R,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,EAAC;4BAAe;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD,CAAC,eACN5R,OAAA;4BAAK6R,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1B9R,OAAA;8BAAG6R,SAAS,EAAC,oCAAoC;8BAAAC,QAAA,EAAE9G,MAAM,CAACC,IAAI,CAAC/B,mBAAmB,CAACV,SAAS,CAAC,CAACrE;4BAAM;8BAAAsN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACzG5R,OAAA;8BAAG6R,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,EAAC;4BAAe;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD,CAAC,eACN5R,OAAA;4BAAK6R,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1B9R,OAAA;8BAAG6R,SAAS,EAAC,oCAAoC;8BAAAC,QAAA,EAAE9G,MAAM,CAACC,IAAI,CAAC/B,mBAAmB,CAACI,SAAS,CAAC,CAACnF;4BAAM;8BAAAsN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACzG5R,OAAA;8BAAG6R,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,EAAC;4BAAgB;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAGA1Q,SAAS,KAAK,UAAU,iBACvBlB,OAAA;kBAAA8R,QAAA,gBACE9R,OAAA;oBAAK6R,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,eACrD9R,OAAA;sBAAI6R,SAAS,EAAE,2BAA2B5R,mBAAmB,CAACC,OAAO,qBAAsB;sBAAA4R,QAAA,gBACzF9R,OAAA,CAAC3B,SAAS;wBAACwT,SAAS,EAAC;sBAAc;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,qBAExC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD9R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACrB,UAAU;0BAACkT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,uBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAAChB,GAAG;0BAACiF,IAAI,EAAEwH,yBAA0B;0BAACsH,OAAO,EAAEnF;wBAAgB;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5R,OAAA;sBAAK6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACvE9R,OAAA;wBAAI6R,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACtE9R,OAAA,CAACpB,UAAU;0BAACiT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;wBAAI;0BAAAuR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,oBAEjF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL5R,OAAA;wBAAK6R,SAAS,EAAC,MAAM;wBAAAC,QAAA,eACnB9R,OAAA,CAACjB,GAAG;0BAACkF,IAAI,EAAEuH,sBAAuB;0BAACuH,OAAO,EAAEhH;wBAAgB;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN5R,OAAA;oBAAK6R,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,gBACvE9R,OAAA;sBAAI6R,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,gBACtE9R,OAAA,CAACpB,UAAU;wBAACiT,SAAS,EAAE,sBAAsB5R,mBAAmB,CAACC,OAAO;sBAAI;wBAAAuR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,wBAEjF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5R,OAAA;sBAAK6R,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9R,OAAA,CAACjB,GAAG;wBAACkF,IAAI,EAAEyH,iCAAkC;wBAACqH,OAAO,EAAEhH;sBAAgB;wBAAA0F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7Q,EAAA,CA7jDID,SAAS;EAAA,QAYInD,WAAW,EACJE,OAAO;AAAA;AAAAsV,EAAA,GAb3BrS,SAAS;AA+jDf,eAAeA,SAAS;AAAC,IAAAqS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}